version: 2
updates:
  # Flutter/Dart 의존성 업데이트
  - package-ecosystem: "pub"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "blue-booth-manager"
    assignees:
      - "blue-booth-manager"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "flutter"
    ignore:
      # 주요 버전 업데이트는 수동으로 처리
      - dependency-name: "flutter"
      - dependency-name: "dart"
      - dependency-name: "riverpod"
      - dependency-name: "flutter_riverpod"
      - dependency-name: "hooks_riverpod"
      - dependency-name: "riverpod_annotation"
      - dependency-name: "sqflite"
      - dependency-name: "freezed"
      - dependency-name: "json_annotation"
      - dependency-name: "mockito"

  # GitHub Actions 업데이트
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "blue-booth-manager"
    assignees:
      - "blue-booth-manager"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions" 