name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # 코드 품질 검사
  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Analyze code
      run: flutter analyze
      
    - name: Format check
      run: dart format --set-exit-if-changed .
      
    - name: Custom lint check
      run: |
        if [ -f "custom_lint.log" ]; then
          echo "Custom lint log found, checking for errors..."
          if grep -q "ERROR" custom_lint.log; then
            echo "Custom lint errors found:"
            grep "ERROR" custom_lint.log
            exit 1
          fi
        fi

  # 테스트 실행
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run unit tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v5
      with:
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
        token: ${{ secrets.CODECOV_TOKEN }} # 보안 강화: 토큰 사용

  # 빌드 검증
  build:
    name: Build Verification
    runs-on: ubuntu-latest
    strategy:
      matrix:
        platform: [android, ios, web, windows, macos, linux]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build for ${{ matrix.platform }}
      run: |
        case ${{ matrix.platform }} in
          android)
            flutter build apk --debug
            ;;
          ios)
            flutter build ios --debug --no-codesign
            ;;
          web)
            flutter build web
            ;;
          windows)
            flutter build windows
            ;;
          macos)
            flutter build macos
            ;;
          linux)
            flutter build linux
            ;;
        esac
      continue-on-error: true

  # 성능 테스트
  performance:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: [test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run performance tests
      run: |
        echo "Running performance tests..."
        # 메모리 사용량 체크
        flutter test test/utils/memory_manager_test.dart --verbose
        flutter test test/utils/batch_processor_test.dart --verbose
        echo "Performance tests completed"

  # 보안 스캔
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Security audit
      run: |
        echo "Checking for known vulnerabilities..."
        flutter pub deps --style=tree
        echo "Security scan completed"

  # 결과 요약
  summary:
    name: CI Summary
    runs-on: ubuntu-latest
    needs: [code-quality, test, build, performance, security]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Generate summary
      run: |
        echo "## CI Pipeline Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Jobs Status:" >> $GITHUB_STEP_SUMMARY
        echo "- Code Quality: ${{ needs.code-quality.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Tests: ${{ needs.test.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Build: ${{ needs.build.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Performance: ${{ needs.performance.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Security: ${{ needs.security.result }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Next Steps:" >> $GITHUB_STEP_SUMMARY
        echo "1. Review any failed jobs" >> $GITHUB_STEP_SUMMARY
        echo "2. Fix issues if any" >> $GITHUB_STEP_SUMMARY
        echo "3. Re-run pipeline" >> $GITHUB_STEP_SUMMARY 