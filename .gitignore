# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# Node.js (불필요한 파일들)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# 🔒 최고 보안 수준 - 모든 키 관련 파일 차단
android/key.properties
android/*.jks
android/*.keystore
android/app-release-key.jks
android/upload-keystore.jks
**/keystore/**
**/signing/**
**/*key*
**/*password*
**/*secret*

# 로컬 환경 설정 파일 (패스워드 포함)
scripts/env_local.*
scripts/*_local.*
**/*local*env*
.env.local
.env.*.local

# 디버그 정보 (난독화 해제 가능)
build/debug-info/
**/mapping.txt
**/seeds.txt
**/usage.txt

#Ignore vscode AI rules
.github\copilot-instructions.md
