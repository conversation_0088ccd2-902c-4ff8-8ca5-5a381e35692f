{"coding_rules": {"ui_ux": ["모바일 화면은 디바이스의 상단바를 침범하지 않음", "접근성 지원은 고려하지 않음"], "code_modification": ["한 파일에 대한 수정은 한 번에 완료", "기존 기능/UI의 생성, 삭제, 변경은 사용자 요청 시에만 진행", "임의의 기능 추가/삭제 금지", "대용량 파일은 반드시 전체를 확인 후 수정", "여러 선택지가 있는 경우 사용자에게 선택 요청"], "code_quality": ["클래스/필드 생성 시 중복 여부 확인", "이름 변경 시 참조되는 모든 파일 일괄 수정", "오류 방지를 위한 철저한 사전 검토"]}, "feature_development": {"existing_functionality": ["수정 대상이 아닌 다른 기능/UI는 변경하지 않음", "필요한 경우 사용자 승인 후 진행"], "error_handling": ["오류 발생 시 직전 상황과 오류 내용 저장", "오류 해결 후 원래 목표로 복귀", "정보 부족 시 최신 정보 검색"]}, "workflow": {"roadmap": ["목표 제시 시 전체 로드맵 작성", "진행 상황을 (예: 1-2/5, 40%)로 공유", "대단원 종료 시 철저한 검증", "로드맵은 별도 메모리로 관리"], "testing": ["테스트 가능 시점에 체크리스트 제공", "커밋이 필요한 시점에 요약 정보와 함께 커밋 권유"]}, "environment": {"language": "한국어로 모든 대답 제공", "os": "Windows 11, PowerShell 7 환경 고려", "dependencies": "pubspec.yaml 직접 수정하지 않고 업데이트 필요 시 알림"}, "security": {"sql_injection": ["모든 사용자 입력에 대한 검증 강화", "테스트 케이스를 통한 보안 검증"]}}