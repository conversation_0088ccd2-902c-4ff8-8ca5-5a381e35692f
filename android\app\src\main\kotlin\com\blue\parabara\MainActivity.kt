package com.blue.parabara

import io.flutter.embedding.android.FlutterFragmentActivity
import android.os.Bundle
import android.view.WindowManager
import android.webkit.WebView

class MainActivity : FlutterFragmentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 🔥 WebView 전역 최적화 설정
        try {
            // WebView 디버깅 비활성화 (성능 향상)
            WebView.setWebContentsDebuggingEnabled(false)

            // WebView 데이터 디렉토리 최적화
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                try {
                    val processName = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                        android.app.Application.getProcessName()
                    } else {
                        packageName
                    }
                    if (processName != packageName) {
                        WebView.setDataDirectorySuffix(processName)
                    }
                } catch (e: Exception) {
                    // 프로세스 이름 가져오기 실패 시 무시
                }
            }
        } catch (e: Exception) {
            // WebView 설정 실패 시 무시
        }

        // 하드웨어 가속 최적화
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )

        // 🔥 추가 성능 최적화 플래그
        window.setFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
        )

        // 렌더링 최적화를 위한 추가 설정
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.WHITE // 아이디어스처럼 하얀색
    }
}
