<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 로컬 개발 환경 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
    </domain-config>

    <!-- IP 조회 서비스들 -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.ipify.org</domain>
        <domain includeSubdomains="true">ipinfo.io</domain>
        <domain includeSubdomains="true">icanhazip.com</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- Google 서비스들 -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">google.com</domain>
        <domain includeSubdomains="true">gstatic.com</domain>
        <domain includeSubdomains="true">googleusercontent.com</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- Firebase 서비스들 -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">firebase.com</domain>
        <domain includeSubdomains="true">firebaseapp.com</domain>
        <domain includeSubdomains="true">firebaseio.com</domain>
        <domain includeSubdomains="true">firebasestorage.googleapis.com</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- 기본 설정 -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
