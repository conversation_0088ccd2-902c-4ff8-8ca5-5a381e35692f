# 바라 부스 매니저 - 배포 가이드

## 📋 개요

이 문서는 바라 부스 매니저의 배포 프로세스를 안내합니다. 개발 환경에서 프로덕션 환경으로의 배포, 릴리즈 관리, 모니터링 등을 포함합니다.

## 🏗️ 배포 아키텍처

### 배포 환경 구성
```
┌─────────────────────────────────────────────────────────────┐
│                    Production Environment                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   Android   │ │     iOS     │ │     Web     │ │Desktop  │ │
│  │   Store     │ │   App Store │ │   Server    │ │Builds   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    CI/CD Pipeline                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   GitHub    │ │   Build     │ │   Test      │ │ Deploy  │ │
│  │  Actions    │ │  Process    │ │  Process    │ │Process  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Development Environment                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   Local     │ │   Staging   │ │   Testing   │ │Quality  │ │
│  │  Development│ │ Environment │ │ Environment │ │Assurance│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 빌드 설정

### 1. 환경별 설정

#### 개발 환경 (Development)
```dart
// lib/config/environment.dart
class Environment {
  static const String apiUrl = 'http://localhost:3000';
  static const bool enableDebug = true;
  static const bool enableLogging = true;
  static const String appName = '바라 부스 매니저 (Dev)';
}
```

#### 스테이징 환경 (Staging)
```dart
// lib/config/environment.dart
class Environment {
  static const String apiUrl = 'https://staging-api.bluebooth.com';
  static const bool enableDebug = false;
  static const bool enableLogging = true;
  static const String appName = '바라 부스 매니저 (Staging)';
}
```

#### 프로덕션 환경 (Production)
```dart
// lib/config/environment.dart
class Environment {
  static const String apiUrl = 'https://api.bluebooth.com';
  static const bool enableDebug = false;
  static const bool enableLogging = false;
  static const String appName = '바라 부스 매니저';
}
```

### 2. 플랫폼별 빌드 설정

#### Android 빌드 설정
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.bluebooth.manager"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }
    
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### iOS 빌드 설정
```swift
// ios/Runner/Info.plist
<key>CFBundleDisplayName</key>
<string>바라 부스 매니저</string>
<key>CFBundleVersion</key>
<string>1.0.0</string>
<key>CFBundleShortVersionString</key>
<string>1.0.0</string>
```

#### Web 빌드 설정
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
```

## 🚀 빌드 프로세스

### 1. 로컬 빌드

#### Android APK 빌드
```bash
# 디버그 APK
flutter build apk --debug

# 릴리즈 APK
flutter build apk --release

# 분할 APK (크기 최적화)
flutter build apk --split-per-abi --release
```

#### Android App Bundle 빌드
```bash
# 릴리즈 App Bundle
flutter build appbundle --release
```

#### iOS 빌드
```bash
# iOS 시뮬레이터용
flutter build ios --debug

# iOS 디바이스용
flutter build ios --release
```

#### Web 빌드
```bash
# 웹 빌드
flutter build web --release

# 웹 빌드 (최적화)
flutter build web --release --web-renderer html
```

#### 데스크톱 빌드
```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

### 2. 자동화된 빌드

#### GitHub Actions 워크플로우
```yaml
# .github/workflows/build.yml
name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Run tests
      run: flutter test --coverage
    
    - name: Build Android APK
      run: flutter build apk --release
    
    - name: Build Web
      run: flutter build web --release
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: build/
```

## 📱 스토어 배포

### 1. Google Play Store 배포

#### 준비 사항
1. **Google Play Console 계정 생성**
2. **개발자 계정 등록** ($25 일회성)
3. **앱 서명 키 생성**
4. **개인정보처리방침 작성**

#### 앱 서명 설정
```bash
# 키스토어 생성
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# 키스토어 정보를 key.properties에 저장
echo "storePassword=<password>" > android/key.properties
echo "keyPassword=<password>" >> android/key.properties
echo "keyAlias=upload" >> android/key.properties
echo "storeFile=<path to keystore>" >> android/key.properties
```

#### 배포 단계
1. **앱 번들 생성**:
   ```bash
   flutter build appbundle --release
   ```

2. **Google Play Console 업로드**:
   - 앱 정보 입력
   - 스크린샷 및 설명 추가
   - 개인정보처리방침 URL 입력
   - 콘텐츠 등급 설정

3. **내부 테스트**:
   - 테스터 그룹 생성
   - 테스트 APK 업로드
   - 테스트 진행

4. **프로덕션 배포**:
   - 검토 완료 후 프로덕션 트랙으로 이동
   - 단계적 배포 설정

### 2. Apple App Store 배포

#### 준비 사항
1. **Apple Developer Program 가입** ($99/년)
2. **App Store Connect 계정 생성**
3. **앱 ID 및 프로비저닝 프로파일 설정**
4. **개인정보처리방침 작성**

#### 인증서 설정
```bash
# 인증서 생성 (Xcode에서 자동 처리)
# 1. Xcode → Preferences → Accounts
# 2. Apple ID 추가
# 3. Manage Certificates → + 버튼
# 4. Apple Development 선택
```

#### 배포 단계
1. **iOS 빌드**:
   ```bash
   flutter build ios --release
   ```

2. **Xcode에서 아카이브**:
   - Xcode에서 ios/Runner.xcworkspace 열기
   - Product → Archive 선택
   - Organizer에서 배포 설정

3. **App Store Connect 업로드**:
   - 앱 정보 입력
   - 스크린샷 및 설명 추가
   - 개인정보처리방침 URL 입력
   - 콘텐츠 등급 설정

4. **TestFlight 테스트**:
   - 내부 테스터 그룹 생성
   - 외부 테스터 초대
   - 테스트 진행

5. **App Store 배포**:
   - 검토 완료 후 App Store 배포

### 3. Web 배포

#### Firebase Hosting 배포
```bash
# Firebase CLI 설치
npm install -g firebase-tools

# Firebase 프로젝트 초기화
firebase init hosting

# 웹 빌드
flutter build web --release

# 배포
firebase deploy --only hosting
```

#### Netlify 배포
```bash
# Netlify CLI 설치
npm install -g netlify-cli

# 웹 빌드
flutter build web --release

# 배포
netlify deploy --prod --dir=build/web
```

## 🔄 릴리즈 관리

### 1. 버전 관리

#### 시맨틱 버저닝
```yaml
# 버전 형식: MAJOR.MINOR.PATCH
# 예: 1.2.3
# MAJOR: 호환되지 않는 API 변경
# MINOR: 이전 버전과 호환되는 기능 추가
# PATCH: 이전 버전과 호환되는 버그 수정
```

#### 버전 업데이트 스크립트
```bash
#!/bin/bash
# scripts/update_version.sh

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "Usage: ./update_version.sh <version>"
    exit 1
fi

# pubspec.yaml 업데이트
sed -i "s/version: .*/version: $VERSION/" pubspec.yaml

# Android 버전 업데이트
sed -i "s/versionCode .*/versionCode $(echo $VERSION | tr -d .)/" android/app/build.gradle
sed -i "s/versionName .*/versionName \"$VERSION\"/" android/app/build.gradle

# iOS 버전 업데이트
plutil -replace CFBundleShortVersionString -string $VERSION ios/Runner/Info.plist
plutil -replace CFBundleVersion -string $VERSION ios/Runner/Info.plist

echo "Version updated to $VERSION"
```

### 2. 릴리즈 노트 생성

#### 자동 릴리즈 노트 생성
```bash
#!/bin/bash
# scripts/generate_release_notes.sh

PREVIOUS_TAG=$1
CURRENT_TAG=$2

if [ -z "$PREVIOUS_TAG" ] || [ -z "$CURRENT_TAG" ]; then
    echo "Usage: ./generate_release_notes.sh <previous_tag> <current_tag>"
    exit 1
fi

echo "# Release Notes - $CURRENT_TAG"
echo ""
echo "## Changes since $PREVIOUS_TAG"
echo ""

# 커밋 메시지에서 변경사항 추출
git log --oneline $PREVIOUS_TAG..$CURRENT_TAG | while read commit; do
    echo "- $commit"
done

echo ""
echo "## Installation"
echo ""
echo "### Android"
echo "- Download from Google Play Store"
echo ""
echo "### iOS"
echo "- Download from App Store"
echo ""
echo "### Web"
echo "- Visit https://app.bluebooth.com"
```

### 3. 자동화된 릴리즈

#### GitHub Actions 릴리즈 워크플로우
```yaml
# .github/workflows/release.yml
name: Create Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.0'
    
    - name: Build Android APK
      run: flutter build apk --release
    
    - name: Build Android App Bundle
      run: flutter build appbundle --release
    
    - name: Build Web
      run: flutter build web --release
    
    - name: Generate Release Notes
      run: |
        ./scripts/generate_release_notes.sh $(git describe --tags --abbrev=0 HEAD~1) ${{ github.ref_name }}
    
    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body_path: release_notes.md
        draft: false
        prerelease: false
    
    - name: Upload Android APK
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: build/app/outputs/flutter-apk/app-release.apk
        asset_name: blue-booth-manager-${{ github.ref_name }}.apk
        asset_content_type: application/vnd.android.package-archive
    
    - name: Upload Android App Bundle
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: build/app/outputs/bundle/release/app-release.aab
        asset_name: blue-booth-manager-${{ github.ref_name }}.aab
        asset_content_type: application/octet-stream
```

## 📊 모니터링 및 분석

### 1. 앱 성능 모니터링

#### Firebase Performance Monitoring
```dart
// lib/main.dart
import 'package:firebase_performance/firebase_performance.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // 성능 모니터링 활성화
  FirebasePerformance.instance.setPerformanceCollectionEnabled(true);
  
  runApp(MyApp());
}
```

#### 커스텀 성능 추적
```dart
// lib/utils/performance_tracker.dart
class PerformanceTracker {
  static final FirebasePerformance _performance = FirebasePerformance.instance;
  
  static Future<void> trackOperation(String operationName, Future<void> Function() operation) async {
    final trace = _performance.newTrace(operationName);
    await trace.start();
    
    try {
      await operation();
    } finally {
      await trace.stop();
    }
  }
}
```

### 2. 크래시 리포팅

#### Firebase Crashlytics
```dart
// lib/main.dart
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // 크래시 리포팅 활성화
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
  
  runApp(MyApp());
}
```

#### 커스텀 에러 핸들링
```dart
// lib/utils/error_handler.dart
class ErrorHandler {
  static void handleError(dynamic error, StackTrace? stackTrace) {
    // Firebase Crashlytics에 에러 전송
    FirebaseCrashlytics.instance.recordError(error, stackTrace);
    
    // 로컬 로깅
    print('Error: $error');
    print('StackTrace: $stackTrace');
  }
}
```

### 3. 사용자 분석

#### Firebase Analytics
```dart
// lib/utils/analytics.dart
import 'package:firebase_analytics/firebase_analytics.dart';

class Analytics {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  
  static Future<void> logEvent(String name, Map<String, dynamic>? parameters) async {
    await _analytics.logEvent(name: name, parameters: parameters);
  }
  
  static Future<void> logPurchase(String productId, double price) async {
    await _analytics.logPurchase(
      currency: 'KRW',
      value: price,
      items: [
        AnalyticsEventItem(
          itemId: productId,
          itemName: 'Product Purchase',
        ),
      ],
    );
  }
}
```

## 🔒 보안 고려사항

### 1. 코드 난독화

#### Android ProGuard 설정
```proguard
# android/app/proguard-rules.pro
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# 민감한 정보 난독화
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}
```

#### iOS 코드 난독화
```bash
# Xcode에서 설정
# Build Settings → Swift Compiler - Code Generation → Optimization Level → Release
# Build Settings → Swift Compiler - Custom Flags → Other Swift Flags → -O
```

### 2. API 키 보안

#### 환경 변수 사용
```dart
// lib/config/secrets.dart
class Secrets {
  static const String apiKey = String.fromEnvironment('API_KEY');
  static const String firebaseConfig = String.fromEnvironment('FIREBASE_CONFIG');
}
```

#### 빌드 시 환경 변수 전달
```bash
flutter build apk --release --dart-define=API_KEY=your_api_key
```

### 3. 데이터 암호화

#### SQLite 데이터베이스 암호화
```dart
// lib/services/database_service.dart
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

class DatabaseService {
  static Future<Database> getEncryptedDatabase() async {
    final databaseFactory = databaseFactoryFfi;
    
    return await databaseFactory.openDatabase(
      'encrypted_database.db',
      options: OpenDatabaseOptions(
        version: 1,
        onCreate: (db, version) async {
          // 테이블 생성
        },
        password: 'your_encryption_key',
      ),
    );
  }
}
```

## 📈 배포 후 검증

### 1. 기능 테스트

#### 자동화된 테스트
```bash
# 배포 후 자동 테스트 실행
flutter test --coverage
flutter drive --target=test_driver/app.dart
```

#### 수동 테스트 체크리스트
- [ ] 앱 설치 및 실행
- [ ] 주요 기능 동작 확인
- [ ] 성능 테스트
- [ ] 메모리 사용량 확인
- [ ] 네트워크 연결 테스트

### 2. 성능 모니터링

#### 성능 지표 추적
```dart
// lib/utils/performance_monitor.dart
class PerformanceMonitor {
  static void trackAppStartup() {
    final stopwatch = Stopwatch()..start();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      Analytics.logEvent('app_startup_time', {
        'duration_ms': stopwatch.elapsedMilliseconds,
      });
    });
  }
  
  static void trackMemoryUsage() {
    final memoryInfo = ProcessInfo.currentRss;
    Analytics.logEvent('memory_usage', {
      'memory_mb': memoryInfo / 1024 / 1024,
    });
  }
}
```

### 3. 사용자 피드백 수집

#### 인앱 피드백 시스템
```dart
// lib/widgets/feedback_widget.dart
class FeedbackWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => FeedbackDialog(),
        );
      },
      child: Icon(Icons.feedback),
    );
  }
}
```

## 🔄 롤백 전략

### 1. 자동 롤백 조건
- 크래시율 5% 이상
- 성능 지표 50% 이상 저하
- 사용자 불만 10건 이상

### 2. 롤백 프로세스
```bash
#!/bin/bash
# scripts/rollback.sh

PREVIOUS_VERSION=$1
if [ -z "$PREVIOUS_VERSION" ]; then
    echo "Usage: ./rollback.sh <previous_version>"
    exit 1
fi

echo "Rolling back to version $PREVIOUS_VERSION"

# 이전 버전으로 태그 체크아웃
git checkout $PREVIOUS_VERSION

# 빌드 및 배포
flutter build apk --release
flutter build appbundle --release

# 스토어에 롤백 배포
echo "Rollback completed. Please manually upload to stores."
```

---

**작성자**: Blue  
**버전**: 1.0.0  
**최종 업데이트**: 2025년 1월 
