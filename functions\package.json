{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"axios": "^1.6.0", "crypto": "^1.0.1", "firebase-admin": "^12.7.0", "firebase-functions": "^6.0.1", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}