import '../utils/mobile_performance_utils.dart';
import '../utils/database_optimizer.dart';
import '../utils/image_cache.dart';
import '../utils/logger_utils.dart';
import '../utils/batch_processor.dart';
import '../utils/object_pool.dart';
import '../utils/memory_manager.dart';
// 로컬 전용 모드: 실시간 동기화 서비스 제거됨

Future<void> cleanupAppResources() async {
  try {
  // 실행 중 메모리 모니터링 및 최적화 루프 중지/정리 (레거시 main 대응)
  MobilePerformanceUtils.stopMemoryMonitoring();
  await MobilePerformanceUtils.performMemoryCleanup();
    MobilePerformanceUtils.shutdown();
    DatabaseOptimizer.shutdown();
    await ImageCacheManager.clearAllCache();
    LoggerUtils.shutdown();
    BatchProcessor.shutdown();
    ObjectPoolManager.shutdown();
    MemoryManager.shutdown();
    // 로컬 전용 모드: 실시간 동기화 서비스 제거됨
    // RealtimeSyncService.shutdown(); // 제거됨
    LoggerUtils.logInfo('앱 리소스 정리 완료');
  } catch (e) {
    LoggerUtils.logError('앱 리소스 정리 중 오류', error: e);
  }
}
