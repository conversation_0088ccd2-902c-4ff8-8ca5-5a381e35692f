/// 앱 전체 설정 관리
/// 
/// 앱스토어 심사, 개발/운영 환경 등에 따른 설정을 관리합니다.
class AppConfig {
  /// 전화번호 인증 요구 여부
  /// 
  /// ⚠️ 앱스토어 심사용 임시 비활성화
  /// 심사 완료 후 반드시 true로 변경해야 합니다!
  /// 
  /// true: 구독 관리 페이지 접근 시 전화번호 인증 필요 (운영 환경)
  /// false: 전화번호 인증 없이 구독 관리 페이지 접근 가능 (심사용)
  static const bool PHONE_VERIFICATION_REQUIRED = false; // 임시 비활성화
  
  /// 앱스토어 심사 모드 여부
  /// 심사 관련 기능들을 일괄 제어할 때 사용
  static const bool APP_STORE_REVIEW_MODE = true;
  
  /// 개발 모드 여부
  static const bool DEBUG_MODE = false;
}
