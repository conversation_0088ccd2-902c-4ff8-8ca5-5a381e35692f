/// 바라 부스 매니저 - 체크리스트 아이템 데이터 모델
///
/// 행사별 체크리스트 체크 상태 정보를 표현하는 데이터 모델 클래스입니다.
/// - 템플릿 ID, 행사 ID, 체크 여부, 체크 시간 등 포함
/// - DB 연동, CRUD, 실시간 동기화 등에서 사용
///
/// 주요 특징:
/// - Freezed 기반 상태 비교 최적화
/// - 불변 객체 패턴 (immutable)
/// - JSON/SQLite 직렬화 지원
/// - copyWith 메서드로 부분 업데이트
/// - 실시간 동기화 메타데이터 포함
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'sync_metadata.dart';

part 'checklist_item.freezed.dart';
part 'checklist_item.g.dart';

/// 체크리스트 아이템 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class ChecklistItem with _$ChecklistItem {
  const factory ChecklistItem({
    int? id,
    required int templateId,
    required int eventId,
    @Default(false) bool isChecked,
    DateTime? checkedAt,
    DateTime? updatedAt,

    // 실시간 동기화 메타데이터
    SyncMetadata? syncMetadata,
  }) = _ChecklistItem;

  factory ChecklistItem.fromJson(Map<String, dynamic> json) => _$ChecklistItemFromJson(json);

  // SQLite 맵에서 직접 생성
  factory ChecklistItem.fromMap(Map<String, dynamic> map) {
    // 동기화 메타데이터 파싱
    SyncMetadata? syncMetadata;
    if (map['syncMetadata'] != null) {
      try {
        final syncMetadataJson = jsonDecode(map['syncMetadata'] as String);
        syncMetadata = SyncMetadata.fromJson(syncMetadataJson);
      } catch (e) {
        // 파싱 실패 시 null로 처리
        syncMetadata = null;
      }
    }

    return ChecklistItem(
      id: map['id'] as int?,
      templateId: map['templateId'] as int,
      eventId: map['eventId'] as int,
      isChecked: (map['isChecked'] ?? 0) == 1,
      checkedAt: map['checkedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['checkedAt'] as int)
          : null,
      updatedAt: map['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] as int)
          : null,
      syncMetadata: syncMetadata,
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory ChecklistItem.create({
    int? id,
    required int templateId,
    required int eventId,
    bool isChecked = false,
  }) {
    final now = DateTime.now();
    return ChecklistItem(
      id: id,
      templateId: templateId,
      eventId: eventId,
      isChecked: isChecked,
      checkedAt: isChecked ? now : null,
      updatedAt: now,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension ChecklistItemMapper on ChecklistItem {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'templateId': templateId,
      'eventId': eventId,
      'isChecked': isChecked ? 1 : 0,
      'checkedAt': checkedAt?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
    
    // 동기화 메타데이터 추가
    if (syncMetadata != null) {
      map['syncMetadata'] = jsonEncode(syncMetadata!.toJson());
    }
    
    // id가 null이 아닌 경우에만 추가 (AUTOINCREMENT를 위해)
    if (id != null) {
      map['id'] = id;
    }
    
    return map;
  }
}

// 체크리스트 아이템 상태 관련 Extension
extension ChecklistItemStatus on ChecklistItem {
  bool get isValid => templateId > 0 && eventId > 0;
  
  /// 체크 상태를 토글하고 새로운 인스턴스 반환
  ChecklistItem toggleCheck() {
    final now = DateTime.now();
    return copyWith(
      isChecked: !isChecked,
      checkedAt: !isChecked ? now : null,
      updatedAt: now,
    );
  }
}
