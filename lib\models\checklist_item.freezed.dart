// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'checklist_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChecklistItem {

 int? get id; int get templateId; int get eventId; bool get isChecked; DateTime? get checkedAt; DateTime? get updatedAt;// 실시간 동기화 메타데이터
 SyncMetadata? get syncMetadata;
/// Create a copy of ChecklistItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChecklistItemCopyWith<ChecklistItem> get copyWith => _$ChecklistItemCopyWithImpl<ChecklistItem>(this as ChecklistItem, _$identity);

  /// Serializes this ChecklistItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChecklistItem&&(identical(other.id, id) || other.id == id)&&(identical(other.templateId, templateId) || other.templateId == templateId)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.isChecked, isChecked) || other.isChecked == isChecked)&&(identical(other.checkedAt, checkedAt) || other.checkedAt == checkedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncMetadata, syncMetadata) || other.syncMetadata == syncMetadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,templateId,eventId,isChecked,checkedAt,updatedAt,syncMetadata);

@override
String toString() {
  return 'ChecklistItem(id: $id, templateId: $templateId, eventId: $eventId, isChecked: $isChecked, checkedAt: $checkedAt, updatedAt: $updatedAt, syncMetadata: $syncMetadata)';
}


}

/// @nodoc
abstract mixin class $ChecklistItemCopyWith<$Res>  {
  factory $ChecklistItemCopyWith(ChecklistItem value, $Res Function(ChecklistItem) _then) = _$ChecklistItemCopyWithImpl;
@useResult
$Res call({
 int? id, int templateId, int eventId, bool isChecked, DateTime? checkedAt, DateTime? updatedAt, SyncMetadata? syncMetadata
});


$SyncMetadataCopyWith<$Res>? get syncMetadata;

}
/// @nodoc
class _$ChecklistItemCopyWithImpl<$Res>
    implements $ChecklistItemCopyWith<$Res> {
  _$ChecklistItemCopyWithImpl(this._self, this._then);

  final ChecklistItem _self;
  final $Res Function(ChecklistItem) _then;

/// Create a copy of ChecklistItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? templateId = null,Object? eventId = null,Object? isChecked = null,Object? checkedAt = freezed,Object? updatedAt = freezed,Object? syncMetadata = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,templateId: null == templateId ? _self.templateId : templateId // ignore: cast_nullable_to_non_nullable
as int,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,isChecked: null == isChecked ? _self.isChecked : isChecked // ignore: cast_nullable_to_non_nullable
as bool,checkedAt: freezed == checkedAt ? _self.checkedAt : checkedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,syncMetadata: freezed == syncMetadata ? _self.syncMetadata : syncMetadata // ignore: cast_nullable_to_non_nullable
as SyncMetadata?,
  ));
}
/// Create a copy of ChecklistItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<$Res>? get syncMetadata {
    if (_self.syncMetadata == null) {
    return null;
  }

  return $SyncMetadataCopyWith<$Res>(_self.syncMetadata!, (value) {
    return _then(_self.copyWith(syncMetadata: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChecklistItem].
extension ChecklistItemPatterns on ChecklistItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChecklistItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChecklistItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChecklistItem value)  $default,){
final _that = this;
switch (_that) {
case _ChecklistItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChecklistItem value)?  $default,){
final _that = this;
switch (_that) {
case _ChecklistItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  int templateId,  int eventId,  bool isChecked,  DateTime? checkedAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChecklistItem() when $default != null:
return $default(_that.id,_that.templateId,_that.eventId,_that.isChecked,_that.checkedAt,_that.updatedAt,_that.syncMetadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  int templateId,  int eventId,  bool isChecked,  DateTime? checkedAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)  $default,) {final _that = this;
switch (_that) {
case _ChecklistItem():
return $default(_that.id,_that.templateId,_that.eventId,_that.isChecked,_that.checkedAt,_that.updatedAt,_that.syncMetadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  int templateId,  int eventId,  bool isChecked,  DateTime? checkedAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)?  $default,) {final _that = this;
switch (_that) {
case _ChecklistItem() when $default != null:
return $default(_that.id,_that.templateId,_that.eventId,_that.isChecked,_that.checkedAt,_that.updatedAt,_that.syncMetadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChecklistItem implements ChecklistItem {
  const _ChecklistItem({this.id, required this.templateId, required this.eventId, this.isChecked = false, this.checkedAt, this.updatedAt, this.syncMetadata});
  factory _ChecklistItem.fromJson(Map<String, dynamic> json) => _$ChecklistItemFromJson(json);

@override final  int? id;
@override final  int templateId;
@override final  int eventId;
@override@JsonKey() final  bool isChecked;
@override final  DateTime? checkedAt;
@override final  DateTime? updatedAt;
// 실시간 동기화 메타데이터
@override final  SyncMetadata? syncMetadata;

/// Create a copy of ChecklistItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChecklistItemCopyWith<_ChecklistItem> get copyWith => __$ChecklistItemCopyWithImpl<_ChecklistItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChecklistItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChecklistItem&&(identical(other.id, id) || other.id == id)&&(identical(other.templateId, templateId) || other.templateId == templateId)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.isChecked, isChecked) || other.isChecked == isChecked)&&(identical(other.checkedAt, checkedAt) || other.checkedAt == checkedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncMetadata, syncMetadata) || other.syncMetadata == syncMetadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,templateId,eventId,isChecked,checkedAt,updatedAt,syncMetadata);

@override
String toString() {
  return 'ChecklistItem(id: $id, templateId: $templateId, eventId: $eventId, isChecked: $isChecked, checkedAt: $checkedAt, updatedAt: $updatedAt, syncMetadata: $syncMetadata)';
}


}

/// @nodoc
abstract mixin class _$ChecklistItemCopyWith<$Res> implements $ChecklistItemCopyWith<$Res> {
  factory _$ChecklistItemCopyWith(_ChecklistItem value, $Res Function(_ChecklistItem) _then) = __$ChecklistItemCopyWithImpl;
@override @useResult
$Res call({
 int? id, int templateId, int eventId, bool isChecked, DateTime? checkedAt, DateTime? updatedAt, SyncMetadata? syncMetadata
});


@override $SyncMetadataCopyWith<$Res>? get syncMetadata;

}
/// @nodoc
class __$ChecklistItemCopyWithImpl<$Res>
    implements _$ChecklistItemCopyWith<$Res> {
  __$ChecklistItemCopyWithImpl(this._self, this._then);

  final _ChecklistItem _self;
  final $Res Function(_ChecklistItem) _then;

/// Create a copy of ChecklistItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? templateId = null,Object? eventId = null,Object? isChecked = null,Object? checkedAt = freezed,Object? updatedAt = freezed,Object? syncMetadata = freezed,}) {
  return _then(_ChecklistItem(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,templateId: null == templateId ? _self.templateId : templateId // ignore: cast_nullable_to_non_nullable
as int,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,isChecked: null == isChecked ? _self.isChecked : isChecked // ignore: cast_nullable_to_non_nullable
as bool,checkedAt: freezed == checkedAt ? _self.checkedAt : checkedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,syncMetadata: freezed == syncMetadata ? _self.syncMetadata : syncMetadata // ignore: cast_nullable_to_non_nullable
as SyncMetadata?,
  ));
}

/// Create a copy of ChecklistItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<$Res>? get syncMetadata {
    if (_self.syncMetadata == null) {
    return null;
  }

  return $SyncMetadataCopyWith<$Res>(_self.syncMetadata!, (value) {
    return _then(_self.copyWith(syncMetadata: value));
  });
}
}

// dart format on
