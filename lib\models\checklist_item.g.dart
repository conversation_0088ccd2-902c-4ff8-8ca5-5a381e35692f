// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checklist_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChecklistItem _$ChecklistItemFromJson(Map<String, dynamic> json) =>
    _ChecklistItem(
      id: (json['id'] as num?)?.toInt(),
      templateId: (json['templateId'] as num).toInt(),
      eventId: (json['eventId'] as num).toInt(),
      isChecked: json['isChecked'] as bool? ?? false,
      checkedAt: json['checkedAt'] == null
          ? null
          : DateTime.parse(json['checkedAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      syncMetadata: json['syncMetadata'] == null
          ? null
          : SyncMetadata.fromJson(json['syncMetadata'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ChecklistItemToJson(_ChecklistItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'templateId': instance.templateId,
      'eventId': instance.eventId,
      'isChecked': instance.isChecked,
      'checkedAt': instance.checkedAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'syncMetadata': instance.syncMetadata,
    };
