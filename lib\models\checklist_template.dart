/// 바라 부스 매니저 - 체크리스트 템플릿 데이터 모델
///
/// 행사별 체크리스트 템플릿 정보를 표현하는 데이터 모델 클래스입니다.
/// - 체크리스트 제목, 설명, 정렬 순서, 행사 ID 등 포함
/// - DB 연동, CRUD, 실시간 동기화 등에서 사용
///
/// 주요 특징:
/// - Freezed 기반 상태 비교 최적화
/// - 불변 객체 패턴 (immutable)
/// - JSON/SQLite 직렬화 지원
/// - copyWith 메서드로 부분 업데이트
/// - 실시간 동기화 메타데이터 포함
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'sync_metadata.dart';

part 'checklist_template.freezed.dart';
part 'checklist_template.g.dart';

/// 체크리스트 템플릿 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class ChecklistTemplate with _$ChecklistTemplate {
  const factory ChecklistTemplate({
    int? id,
    required String title,
    required int eventId,
    @Default(0) int order,
    @Default(true) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,

    // 실시간 동기화 메타데이터
    SyncMetadata? syncMetadata,
  }) = _ChecklistTemplate;

  factory ChecklistTemplate.fromJson(Map<String, dynamic> json) => _$ChecklistTemplateFromJson(json);

  // SQLite 맵에서 직접 생성
  factory ChecklistTemplate.fromMap(Map<String, dynamic> map) {
    // 동기화 메타데이터 파싱
    SyncMetadata? syncMetadata;
    if (map['syncMetadata'] != null) {
      try {
        final syncMetadataJson = jsonDecode(map['syncMetadata'] as String);
        syncMetadata = SyncMetadata.fromJson(syncMetadataJson);
      } catch (e) {
        // 파싱 실패 시 null로 처리
        syncMetadata = null;
      }
    }

    return ChecklistTemplate(
      id: map['id'] as int?,
      title: map['title'] as String,
      eventId: map['eventId'] as int,
      order: map['order'] as int? ?? 0,
      isActive: (map['isActive'] ?? 1) == 1,
      createdAt: map['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int)
          : null,
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] as int)
          : null,
      syncMetadata: syncMetadata,
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory ChecklistTemplate.create({
    int? id,
    required String title,
    required int eventId,
    int order = 0,
    bool isActive = true,
  }) {
    final now = DateTime.now();
    return ChecklistTemplate(
      id: id,
      title: title,
      eventId: eventId,
      order: order,
      isActive: isActive,
      createdAt: now,
      updatedAt: now,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension ChecklistTemplateMapper on ChecklistTemplate {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'title': title,
      'eventId': eventId,
      'order': order,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
    
    // 동기화 메타데이터 추가
    if (syncMetadata != null) {
      map['syncMetadata'] = jsonEncode(syncMetadata!.toJson());
    }
    
    // id가 null이 아닌 경우에만 추가 (AUTOINCREMENT를 위해)
    if (id != null) {
      map['id'] = id;
    }
    
    return map;
  }
}

// 체크리스트 템플릿 상태 관련 Extension
extension ChecklistTemplateStatus on ChecklistTemplate {
  bool get isValid => title.trim().isNotEmpty;
}
