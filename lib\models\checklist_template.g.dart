// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checklist_template.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChecklistTemplate _$ChecklistTemplateFromJson(Map<String, dynamic> json) =>
    _ChecklistTemplate(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String,
      eventId: (json['eventId'] as num).toInt(),
      order: (json['order'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      syncMetadata: json['syncMetadata'] == null
          ? null
          : SyncMetadata.fromJson(json['syncMetadata'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ChecklistTemplateToJson(_ChecklistTemplate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'eventId': instance.eventId,
      'order': instance.order,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'syncMetadata': instance.syncMetadata,
    };
