import 'package:freezed_annotation/freezed_annotation.dart';

part 'event.freezed.dart';
part 'event.g.dart';

/// 목표 수익 관리 모드
enum RevenueGoalMode {
  overall, // 전체 목표
  seller,  // 판매자별 목표
}

/// 행사 정보를 표현하는 데이터 모델 클래스입니다.
/// - 행사명, 이미지, 시작/종료 날짜 등 다양한 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
/// - freezed를 사용하여 불변 객체로 생성
@freezed
abstract class Event with _$Event {
  const factory Event({
    int? id,
    required String name,
    String? imagePath,
    required DateTime startDate,
    required DateTime endDate,
    @Default(true) bool isActive,
    String? description,
    @Default(RevenueGoalMode.overall) RevenueGoalMode revenueGoalMode,
    required DateTime createdAt,
    DateTime? updatedAt,
    // 소유권 정보 (로컬 권한 체크용)
    String? ownerUserId,
    @Default(true) bool isOwner,
  }) = _Event;

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);

  // SQLite 맵에서 직접 생성
  factory Event.fromMap(Map<String, dynamic> map) {
    return Event(
      id: map['id'],
      name: map['name'] ?? '',
      imagePath: map['imagePath'],
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      isActive: map['isActive'] == 1 || map['isActive'] == true,
      description: map['description'],
      revenueGoalMode: _parseRevenueGoalMode(map['revenueGoalMode']),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
      ownerUserId: map['ownerUserId'],
      isOwner: map['isOwner'] == 1 || map['isOwner'] == true,
    );
  }

  // Firebase 문서에서 안전하게 생성 (Timestamp 처리 포함)
  factory Event.fromFirebaseMap(Map<String, dynamic> map) {
    try {
      return Event(
        id: map['id'],
        name: map['name'] ?? '',
        imagePath: map['imagePath'],
        startDate: _parseFirebaseDateTime(map['startDate']),
        endDate: _parseFirebaseDateTime(map['endDate']),
        isActive: map['isActive'] ?? true,
        revenueGoalMode: _parseRevenueGoalMode(map['revenueGoalMode']),
        description: map['description'],
        createdAt: _parseFirebaseDateTime(map['createdAt']),
        updatedAt: map['updatedAt'] != null ? _parseFirebaseDateTime(map['updatedAt']) : null,
      );
    } catch (e) {
      throw FormatException('Firebase Event 데이터 파싱 실패: $e\nData: $map');
    }
  }

  // Firebase DateTime/Timestamp 안전 파싱
  static DateTime _parseFirebaseDateTime(dynamic value) {
    if (value == null) throw ArgumentError('DateTime 값이 null입니다');

    if (value is DateTime) return value;
    if (value is String) return DateTime.parse(value);

    // Firebase Timestamp 처리
    if (value.runtimeType.toString().contains('Timestamp')) {
      return (value as dynamic).toDate() as DateTime;
    }

    throw ArgumentError('지원되지 않는 DateTime 형식: ${value.runtimeType}');
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory Event.create({
    int? id,
    required String name,
    String? imagePath,
    required DateTime startDate,
    required DateTime endDate,
    bool isActive = true,
    String? description,
    DateTime? createdAt,
    String? ownerUserId,
    bool isOwner = true,
  }) {
    return Event(
      id: id,
      name: name,
      imagePath: imagePath,
      startDate: startDate,
      endDate: endDate,
      isActive: isActive,
      description: description,
      createdAt: createdAt ?? DateTime.now(),
      ownerUserId: ownerUserId,
      isOwner: isOwner,
    );
  }
}



// 날짜 관련 Extension
extension EventDate on Event {
  /// 행사가 현재 진행 중인지 확인
  bool get isOngoing {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate.add(const Duration(days: 1)));
  }

  /// 행사가 종료되었는지 확인
  bool get isEnded {
    final now = DateTime.now();
    return now.isAfter(endDate.add(const Duration(days: 1)));
  }

  /// 행사가 시작 전인지 확인
  bool get isUpcoming {
    final now = DateTime.now();
    return now.isBefore(startDate);
  }

  /// 행사 기간 문자열 반환 (예: "2024년 10월 13일 - 16일")
  String get dateRangeString {
    if (startDate.year == endDate.year && startDate.month == endDate.month) {
      return '${startDate.year}년 ${startDate.month}월 ${startDate.day}일 - ${endDate.day}일';
    } else if (startDate.year == endDate.year) {
      return '${startDate.year}년 ${startDate.month}월 ${startDate.day}일 - ${endDate.month}월 ${endDate.day}일';
    } else {
      return '${startDate.year}년 ${startDate.month}월 ${startDate.day}일 - ${endDate.year}년 ${endDate.month}월 ${endDate.day}일';
    }
  }

  /// 행사 상태 문자열 반환
  String get statusString {
    if (isUpcoming) return '예정';
    if (isOngoing) return '진행중';
    if (isEnded) return '종료';
    return '알 수 없음';
  }

  /// 행사 기간 (일 수)
  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }
}

// SQLite 맵 변환을 위한 Extension
extension EventMapper on Event {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'name': name,
      'imagePath': imagePath,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive ? 1 : 0,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'ownerUserId': ownerUserId,
      'isOwner': isOwner ? 1 : 0,
    };

    // id가 null이 아닌 경우에만 추가 (AUTOINCREMENT를 위해)
    if (id != null) {
      map['id'] = id;
    }

    return map;
  }

  // Firebase 업로드용 맵 변환 (필요한 필드만 포함, null 값 제외)
  Map<String, dynamic> toFirebaseMap() {
    final map = <String, dynamic>{
      // 기본 정보 (사용자가 원하는 순서)
      'name': name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'revenueGoalMode': revenueGoalMode.name,
      'createdAt': createdAt.toIso8601String(),
    };

    // 선택적 필드들 (null이 아닌 경우만 포함)
    if (description != null && description!.isNotEmpty) {
      map['description'] = description;
    }
    if (imagePath != null && imagePath!.isNotEmpty) {
      map['imagePath'] = imagePath;
    }
    if (updatedAt != null) {
      map['updatedAt'] = updatedAt!.toIso8601String();
    }

    return map;
  }
}

/// RevenueGoalMode 파싱 헬퍼 함수
RevenueGoalMode _parseRevenueGoalMode(dynamic value) {
  if (value == null) return RevenueGoalMode.overall;

  if (value is String) {
    switch (value.toLowerCase()) {
      case 'seller':
        return RevenueGoalMode.seller;
      case 'overall':
      default:
        return RevenueGoalMode.overall;
    }
  }

  return RevenueGoalMode.overall;
}
