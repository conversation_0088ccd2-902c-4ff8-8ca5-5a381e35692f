// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_permission.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EventPermission {

/// 행사 ID
 int get eventId;/// 사용자 ID
 String get userId;/// 사용자 닉네임 (캐시용)
 String get userNickname;/// 사용자 역할
 UserRole get role;/// 권한 목록
 List<Permission> get permissions;/// 권한 부여 시간
 DateTime get grantedAt;/// 권한 수정 시간
 DateTime? get updatedAt;/// 권한을 부여한 사용자 ID (소유자)
 String? get grantedByUserId;
/// Create a copy of EventPermission
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventPermissionCopyWith<EventPermission> get copyWith => _$EventPermissionCopyWithImpl<EventPermission>(this as EventPermission, _$identity);

  /// Serializes this EventPermission to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventPermission&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userNickname, userNickname) || other.userNickname == userNickname)&&(identical(other.role, role) || other.role == role)&&const DeepCollectionEquality().equals(other.permissions, permissions)&&(identical(other.grantedAt, grantedAt) || other.grantedAt == grantedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.grantedByUserId, grantedByUserId) || other.grantedByUserId == grantedByUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,eventId,userId,userNickname,role,const DeepCollectionEquality().hash(permissions),grantedAt,updatedAt,grantedByUserId);

@override
String toString() {
  return 'EventPermission(eventId: $eventId, userId: $userId, userNickname: $userNickname, role: $role, permissions: $permissions, grantedAt: $grantedAt, updatedAt: $updatedAt, grantedByUserId: $grantedByUserId)';
}


}

/// @nodoc
abstract mixin class $EventPermissionCopyWith<$Res>  {
  factory $EventPermissionCopyWith(EventPermission value, $Res Function(EventPermission) _then) = _$EventPermissionCopyWithImpl;
@useResult
$Res call({
 int eventId, String userId, String userNickname, UserRole role, List<Permission> permissions, DateTime grantedAt, DateTime? updatedAt, String? grantedByUserId
});




}
/// @nodoc
class _$EventPermissionCopyWithImpl<$Res>
    implements $EventPermissionCopyWith<$Res> {
  _$EventPermissionCopyWithImpl(this._self, this._then);

  final EventPermission _self;
  final $Res Function(EventPermission) _then;

/// Create a copy of EventPermission
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? eventId = null,Object? userId = null,Object? userNickname = null,Object? role = null,Object? permissions = null,Object? grantedAt = null,Object? updatedAt = freezed,Object? grantedByUserId = freezed,}) {
  return _then(_self.copyWith(
eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userNickname: null == userNickname ? _self.userNickname : userNickname // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole,permissions: null == permissions ? _self.permissions : permissions // ignore: cast_nullable_to_non_nullable
as List<Permission>,grantedAt: null == grantedAt ? _self.grantedAt : grantedAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,grantedByUserId: freezed == grantedByUserId ? _self.grantedByUserId : grantedByUserId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [EventPermission].
extension EventPermissionPatterns on EventPermission {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EventPermission value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EventPermission() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EventPermission value)  $default,){
final _that = this;
switch (_that) {
case _EventPermission():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EventPermission value)?  $default,){
final _that = this;
switch (_that) {
case _EventPermission() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int eventId,  String userId,  String userNickname,  UserRole role,  List<Permission> permissions,  DateTime grantedAt,  DateTime? updatedAt,  String? grantedByUserId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EventPermission() when $default != null:
return $default(_that.eventId,_that.userId,_that.userNickname,_that.role,_that.permissions,_that.grantedAt,_that.updatedAt,_that.grantedByUserId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int eventId,  String userId,  String userNickname,  UserRole role,  List<Permission> permissions,  DateTime grantedAt,  DateTime? updatedAt,  String? grantedByUserId)  $default,) {final _that = this;
switch (_that) {
case _EventPermission():
return $default(_that.eventId,_that.userId,_that.userNickname,_that.role,_that.permissions,_that.grantedAt,_that.updatedAt,_that.grantedByUserId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int eventId,  String userId,  String userNickname,  UserRole role,  List<Permission> permissions,  DateTime grantedAt,  DateTime? updatedAt,  String? grantedByUserId)?  $default,) {final _that = this;
switch (_that) {
case _EventPermission() when $default != null:
return $default(_that.eventId,_that.userId,_that.userNickname,_that.role,_that.permissions,_that.grantedAt,_that.updatedAt,_that.grantedByUserId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EventPermission implements EventPermission {
  const _EventPermission({required this.eventId, required this.userId, required this.userNickname, required this.role, final  List<Permission> permissions = const [], required this.grantedAt, this.updatedAt, this.grantedByUserId}): _permissions = permissions;
  factory _EventPermission.fromJson(Map<String, dynamic> json) => _$EventPermissionFromJson(json);

/// 행사 ID
@override final  int eventId;
/// 사용자 ID
@override final  String userId;
/// 사용자 닉네임 (캐시용)
@override final  String userNickname;
/// 사용자 역할
@override final  UserRole role;
/// 권한 목록
 final  List<Permission> _permissions;
/// 권한 목록
@override@JsonKey() List<Permission> get permissions {
  if (_permissions is EqualUnmodifiableListView) return _permissions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_permissions);
}

/// 권한 부여 시간
@override final  DateTime grantedAt;
/// 권한 수정 시간
@override final  DateTime? updatedAt;
/// 권한을 부여한 사용자 ID (소유자)
@override final  String? grantedByUserId;

/// Create a copy of EventPermission
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventPermissionCopyWith<_EventPermission> get copyWith => __$EventPermissionCopyWithImpl<_EventPermission>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EventPermissionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventPermission&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userNickname, userNickname) || other.userNickname == userNickname)&&(identical(other.role, role) || other.role == role)&&const DeepCollectionEquality().equals(other._permissions, _permissions)&&(identical(other.grantedAt, grantedAt) || other.grantedAt == grantedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.grantedByUserId, grantedByUserId) || other.grantedByUserId == grantedByUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,eventId,userId,userNickname,role,const DeepCollectionEquality().hash(_permissions),grantedAt,updatedAt,grantedByUserId);

@override
String toString() {
  return 'EventPermission(eventId: $eventId, userId: $userId, userNickname: $userNickname, role: $role, permissions: $permissions, grantedAt: $grantedAt, updatedAt: $updatedAt, grantedByUserId: $grantedByUserId)';
}


}

/// @nodoc
abstract mixin class _$EventPermissionCopyWith<$Res> implements $EventPermissionCopyWith<$Res> {
  factory _$EventPermissionCopyWith(_EventPermission value, $Res Function(_EventPermission) _then) = __$EventPermissionCopyWithImpl;
@override @useResult
$Res call({
 int eventId, String userId, String userNickname, UserRole role, List<Permission> permissions, DateTime grantedAt, DateTime? updatedAt, String? grantedByUserId
});




}
/// @nodoc
class __$EventPermissionCopyWithImpl<$Res>
    implements _$EventPermissionCopyWith<$Res> {
  __$EventPermissionCopyWithImpl(this._self, this._then);

  final _EventPermission _self;
  final $Res Function(_EventPermission) _then;

/// Create a copy of EventPermission
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? eventId = null,Object? userId = null,Object? userNickname = null,Object? role = null,Object? permissions = null,Object? grantedAt = null,Object? updatedAt = freezed,Object? grantedByUserId = freezed,}) {
  return _then(_EventPermission(
eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userNickname: null == userNickname ? _self.userNickname : userNickname // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole,permissions: null == permissions ? _self._permissions : permissions // ignore: cast_nullable_to_non_nullable
as List<Permission>,grantedAt: null == grantedAt ? _self.grantedAt : grantedAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,grantedByUserId: freezed == grantedByUserId ? _self.grantedByUserId : grantedByUserId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
