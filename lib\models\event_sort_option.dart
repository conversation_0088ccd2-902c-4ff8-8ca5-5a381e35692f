/// 행사 정렬 옵션을 정의하는 열거형입니다.
/// 
/// 행사 목록을 다양한 기준으로 정렬할 수 있도록 지원합니다.
enum EventSortOption {
  /// 최근 생성순 (기본값)
  recentlyCreated('최근 생성순', 'createdAt DESC'),
  
  /// 오래된 생성순
  oldestCreated('오래된 생성순', 'createdAt ASC'),
  
  /// 행사명 가나다순
  nameAscending('행사명 가나다순', 'name ASC'),
  
  /// 행사명 가나다 역순
  nameDescending('행사명 가나다 역순', 'name DESC'),
  
  /// 시작일 빠른순
  startDateAscending('시작일 빠른순', 'startDate ASC'),
  
  /// 시작일 늦은순
  startDateDescending('시작일 늦은순', 'startDate DESC'),
  
  /// 종료일 빠른순
  endDateAscending('종료일 빠른순', 'endDate ASC'),
  
  /// 종료일 늦은순
  endDateDescending('종료일 늦은순', 'endDate DESC');

  const EventSortOption(this.displayName, this.sqlOrderBy);

  /// 사용자에게 표시될 이름
  final String displayName;
  
  /// SQL ORDER BY 절에 사용될 문자열
  final String sqlOrderBy;

  /// 기본 정렬 옵션
  static const EventSortOption defaultOption = EventSortOption.recentlyCreated;

  /// 모든 정렬 옵션 목록
  static const List<EventSortOption> allOptions = [
    recentlyCreated,
    oldestCreated,
    nameAscending,
    nameDescending,
    startDateAscending,
    startDateDescending,
    endDateAscending,
    endDateDescending,
  ];

  /// 자주 사용되는 정렬 옵션들
  static const List<EventSortOption> commonOptions = [
    recentlyCreated,
    nameAscending,
    startDateAscending,
  ];

  @override
  String toString() => displayName;
}

/// 행사 필터 클래스
/// 
/// 행사 목록을 필터링하기 위한 조건들을 정의합니다.
class EventFilter {
  /// 행사 필터 생성자
  const EventFilter({
    /// 검색 키워드
    this.searchKeyword = '',
    
    /// 정렬 옵션
    this.sortOption = EventSortOption.recentlyCreated,
    
    /// 활성 행사만 표시
    this.onlyActive = true,
    
    /// 진행중인 행사만 표시
    this.onlyOngoing = false,
    
    /// 예정된 행사만 표시
    this.onlyUpcoming = false,
    
    /// 종료된 행사만 표시
    this.onlyEnded = false,
    
    /// 특정 날짜 범위 필터
    this.dateRangeStart,
    this.dateRangeEnd,
  });

  final String searchKeyword;
  final EventSortOption sortOption;
  final bool onlyActive;
  final bool onlyOngoing;
  final bool onlyUpcoming;
  final bool onlyEnded;
  final DateTime? dateRangeStart;
  final DateTime? dateRangeEnd;

  /// 기본 필터
  static const EventFilter defaultFilter = EventFilter();

  /// 활성 행사만 보는 필터
  static const EventFilter activeOnly = EventFilter(onlyActive: true);

  /// 진행중인 행사만 보는 필터
  static const EventFilter ongoingOnly = EventFilter(onlyOngoing: true);

  /// 필터 복사 (copyWith 패턴)
  EventFilter copyWith({
    String? searchKeyword,
    EventSortOption? sortOption,
    bool? onlyActive,
    bool? onlyOngoing,
    bool? onlyUpcoming,
    bool? onlyEnded,
    DateTime? dateRangeStart,
    DateTime? dateRangeEnd,
  }) {
    return EventFilter(
      searchKeyword: searchKeyword ?? this.searchKeyword,
      sortOption: sortOption ?? this.sortOption,
      onlyActive: onlyActive ?? this.onlyActive,
      onlyOngoing: onlyOngoing ?? this.onlyOngoing,
      onlyUpcoming: onlyUpcoming ?? this.onlyUpcoming,
      onlyEnded: onlyEnded ?? this.onlyEnded,
      dateRangeStart: dateRangeStart ?? this.dateRangeStart,
      dateRangeEnd: dateRangeEnd ?? this.dateRangeEnd,
    );
  }

  /// 필터가 기본값인지 확인
  bool get isDefault {
    return searchKeyword.isEmpty &&
        sortOption == EventSortOption.recentlyCreated &&
        onlyActive == true &&
        !onlyOngoing &&
        !onlyUpcoming &&
        !onlyEnded &&
        dateRangeStart == null &&
        dateRangeEnd == null;
  }

  /// 필터 조건이 있는지 확인
  bool get hasActiveFilters {
    return searchKeyword.isNotEmpty ||
        onlyOngoing ||
        onlyUpcoming ||
        onlyEnded ||
        dateRangeStart != null ||
        dateRangeEnd != null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventFilter &&
        other.searchKeyword == searchKeyword &&
        other.sortOption == sortOption &&
        other.onlyActive == onlyActive &&
        other.onlyOngoing == onlyOngoing &&
        other.onlyUpcoming == onlyUpcoming &&
        other.onlyEnded == onlyEnded &&
        other.dateRangeStart == dateRangeStart &&
        other.dateRangeEnd == dateRangeEnd;
  }

  @override
  int get hashCode {
    return Object.hash(
      searchKeyword,
      sortOption,
      onlyActive,
      onlyOngoing,
      onlyUpcoming,
      onlyEnded,
      dateRangeStart,
      dateRangeEnd,
    );
  }

  @override
  String toString() {
    return 'EventFilter(searchKeyword: $searchKeyword, sortOption: $sortOption, '
        'onlyActive: $onlyActive, onlyOngoing: $onlyOngoing, '
        'onlyUpcoming: $onlyUpcoming, onlyEnded: $onlyEnded, '
        'dateRange: $dateRangeStart ~ $dateRangeEnd)';
  }
}
