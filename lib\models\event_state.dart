import 'package:freezed_annotation/freezed_annotation.dart';
import 'event.dart';
import 'event_sort_option.dart';

part 'event_state.freezed.dart';

/// Event 관련 상태를 관리하는 State 클래스입니다.
/// 
/// 주요 기능:
/// - 행사 목록 상태 관리
/// - 로딩 상태 관리
/// - 에러 상태 관리
/// - 필터링 상태 관리
@freezed
abstract class EventState with _$EventState {
  const factory EventState({
    /// 행사 목록
    @Default([]) List<Event> events,
    
    /// 로딩 상태
    @Default(false) bool isLoading,
    
    /// 에러 메시지
    String? errorMessage,
    
    /// 현재 적용된 필터
    @Default(EventFilter.defaultFilter) EventFilter currentFilter,
    
    /// 마지막 업데이트 시간
    DateTime? lastUpdated,
    
    /// 총 행사 개수 (필터링 전)
    @Default(0) int totalCount,
    
    /// 필터링된 행사 개수
    @Default(0) int filteredCount,
    
    /// 선택된 행사 (상세보기용)
    Event? selectedEvent,
    
    /// 검색 모드 여부
    @Default(false) bool isSearchMode,
    
    /// 새로고침 중 여부
    @Default(false) bool isRefreshing,
  }) = _EventState;

  const EventState._();

  /// 에러가 있는지 확인
  bool get hasError => errorMessage != null;

  /// 행사가 있는지 확인
  bool get hasEvents => events.isNotEmpty;

  /// 필터가 적용되었는지 확인
  bool get hasActiveFilter => !currentFilter.isDefault;

  /// 검색 키워드가 있는지 확인
  bool get hasSearchKeyword => currentFilter.searchKeyword.isNotEmpty;

  /// 진행 중인 행사 목록
  List<Event> get ongoingEvents => events.where((event) => event.isOngoing).toList();

  /// 예정된 행사 목록
  List<Event> get upcomingEvents => events.where((event) => event.isUpcoming).toList();

  /// 종료된 행사 목록
  List<Event> get endedEvents => events.where((event) => event.isEnded).toList();

  /// 활성 행사 목록
  List<Event> get activeEvents => events.where((event) => event.isActive).toList();

  /// 비활성 행사 목록
  List<Event> get inactiveEvents => events.where((event) => !event.isActive).toList();

  /// 상태별 행사 개수 정보
  Map<String, int> get statusCounts => {
    'total': events.length,
    'ongoing': ongoingEvents.length,
    'upcoming': upcomingEvents.length,
    'ended': endedEvents.length,
    'active': activeEvents.length,
    'inactive': inactiveEvents.length,
  };

  /// 로딩 중이거나 새로고침 중인지 확인
  bool get isBusy => isLoading || isRefreshing;

  /// 데이터가 비어있는지 확인 (로딩 중이 아닌 상태에서)
  bool get isEmpty => !isLoading && events.isEmpty;

  /// 검색 결과가 없는지 확인
  bool get isSearchEmpty => isSearchMode && !isLoading && events.isEmpty;

  /// 필터 결과가 없는지 확인
  bool get isFilterEmpty => hasActiveFilter && !isLoading && events.isEmpty;
}

/// Event 관련 액션을 정의하는 열거형
enum EventAction {
  /// 목록 새로고침
  refresh,
  
  /// 검색
  search,
  
  /// 필터 적용
  filter,
  
  /// 정렬
  sort,
  
  /// 행사 추가
  add,
  
  /// 행사 수정
  edit,
  
  /// 행사 삭제
  delete,
  
  /// 행사 선택
  select,
  
  /// 기본 행사 설정
  setDefault,
  
  /// 행사 활성화/비활성화
  toggleActive,
}

/// Event 관련 에러 타입을 정의하는 열거형
enum EventErrorType {
  /// 네트워크 에러
  network,
  
  /// 데이터베이스 에러
  database,
  
  /// 유효성 검사 에러
  validation,
  
  /// 권한 에러
  permission,
  
  /// 알 수 없는 에러
  unknown,
}

/// Event 에러 정보를 담는 클래스
@freezed
abstract class EventError with _$EventError {
  const factory EventError({
    required EventErrorType type,
    required String message,
    String? details,
    DateTime? timestamp,
    Map<String, dynamic>? context,
  }) = _EventError;

  const EventError._();

  /// 사용자에게 표시할 메시지
  String get displayMessage {
    switch (type) {
      case EventErrorType.network:
        return '네트워크 연결을 확인해주세요.';
      case EventErrorType.database:
        return '데이터 처리 중 오류가 발생했습니다.';
      case EventErrorType.validation:
        return '입력 정보를 확인해주세요.';
      case EventErrorType.permission:
        return '권한이 없습니다.';
      case EventErrorType.unknown:
        return '알 수 없는 오류가 발생했습니다.';
    }
  }

  /// 에러가 재시도 가능한지 확인
  bool get isRetryable {
    switch (type) {
      case EventErrorType.network:
      case EventErrorType.database:
        return true;
      case EventErrorType.validation:
      case EventErrorType.permission:
      case EventErrorType.unknown:
        return false;
    }
  }
}

/// Event 로딩 상태를 정의하는 열거형
enum EventLoadingState {
  /// 초기 상태
  initial,
  
  /// 로딩 중
  loading,
  
  /// 새로고침 중
  refreshing,
  
  /// 검색 중
  searching,
  
  /// 필터링 중
  filtering,
  
  /// 완료
  completed,
  
  /// 에러
  error,
}

/// Event 정렬 방향을 정의하는 열거형
enum EventSortDirection {
  /// 오름차순
  ascending,
  
  /// 내림차순
  descending,
}

/// Event 필터 타입을 정의하는 열거형
enum EventFilterType {
  /// 전체
  all,
  
  /// 진행 중
  ongoing,
  
  /// 예정
  upcoming,
  
  /// 종료
  ended,
  
  /// 활성
  active,
  
  /// 비활성
  inactive,
  
  /// 기본 행사
  defaultOnly,
  
  /// 검색
  search,
  
  /// 날짜 범위
  dateRange,
}

/// Event 뷰 모드를 정의하는 열거형
enum EventViewMode {
  /// 목록 보기
  list,
  
  /// 그리드 보기
  grid,
  
  /// 카드 보기
  card,
  
  /// 달력 보기
  calendar,
}

/// Event 관련 설정을 담는 클래스
@freezed
abstract class EventSettings with _$EventSettings {
  const factory EventSettings({
    /// 기본 정렬 옵션
    @Default(EventSortOption.recentlyCreated) EventSortOption defaultSortOption,
    
    /// 기본 뷰 모드
    @Default(EventViewMode.list) EventViewMode defaultViewMode,
    
    /// 페이지당 항목 수
    @Default(20) int itemsPerPage,
    
    /// 자동 새로고침 간격 (분)
    @Default(5) int autoRefreshInterval,
    
    /// 검색 디바운스 시간 (밀리초)
    @Default(300) int searchDebounceMs,
    
    /// 캐시 유효 시간 (분)
    @Default(10) int cacheValidityMinutes,
    
    /// 오프라인 모드 지원 여부
    @Default(true) bool offlineSupport,
    
    /// 자동 백업 여부
    @Default(false) bool autoBackup,
  }) = _EventSettings;
}
