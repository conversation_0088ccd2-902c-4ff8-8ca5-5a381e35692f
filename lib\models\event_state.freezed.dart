// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$EventState {

/// 행사 목록
 List<Event> get events;/// 로딩 상태
 bool get isLoading;/// 에러 메시지
 String? get errorMessage;/// 현재 적용된 필터
 EventFilter get currentFilter;/// 마지막 업데이트 시간
 DateTime? get lastUpdated;/// 총 행사 개수 (필터링 전)
 int get totalCount;/// 필터링된 행사 개수
 int get filteredCount;/// 선택된 행사 (상세보기용)
 Event? get selectedEvent;/// 검색 모드 여부
 bool get isSearchMode;/// 새로고침 중 여부
 bool get isRefreshing;
/// Create a copy of EventState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventStateCopyWith<EventState> get copyWith => _$EventStateCopyWithImpl<EventState>(this as EventState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventState&&const DeepCollectionEquality().equals(other.events, events)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.currentFilter, currentFilter) || other.currentFilter == currentFilter)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount)&&(identical(other.filteredCount, filteredCount) || other.filteredCount == filteredCount)&&(identical(other.selectedEvent, selectedEvent) || other.selectedEvent == selectedEvent)&&(identical(other.isSearchMode, isSearchMode) || other.isSearchMode == isSearchMode)&&(identical(other.isRefreshing, isRefreshing) || other.isRefreshing == isRefreshing));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(events),isLoading,errorMessage,currentFilter,lastUpdated,totalCount,filteredCount,selectedEvent,isSearchMode,isRefreshing);

@override
String toString() {
  return 'EventState(events: $events, isLoading: $isLoading, errorMessage: $errorMessage, currentFilter: $currentFilter, lastUpdated: $lastUpdated, totalCount: $totalCount, filteredCount: $filteredCount, selectedEvent: $selectedEvent, isSearchMode: $isSearchMode, isRefreshing: $isRefreshing)';
}


}

/// @nodoc
abstract mixin class $EventStateCopyWith<$Res>  {
  factory $EventStateCopyWith(EventState value, $Res Function(EventState) _then) = _$EventStateCopyWithImpl;
@useResult
$Res call({
 List<Event> events, bool isLoading, String? errorMessage, EventFilter currentFilter, DateTime? lastUpdated, int totalCount, int filteredCount, Event? selectedEvent, bool isSearchMode, bool isRefreshing
});


$EventCopyWith<$Res>? get selectedEvent;

}
/// @nodoc
class _$EventStateCopyWithImpl<$Res>
    implements $EventStateCopyWith<$Res> {
  _$EventStateCopyWithImpl(this._self, this._then);

  final EventState _self;
  final $Res Function(EventState) _then;

/// Create a copy of EventState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? events = null,Object? isLoading = null,Object? errorMessage = freezed,Object? currentFilter = null,Object? lastUpdated = freezed,Object? totalCount = null,Object? filteredCount = null,Object? selectedEvent = freezed,Object? isSearchMode = null,Object? isRefreshing = null,}) {
  return _then(_self.copyWith(
events: null == events ? _self.events : events // ignore: cast_nullable_to_non_nullable
as List<Event>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,currentFilter: null == currentFilter ? _self.currentFilter : currentFilter // ignore: cast_nullable_to_non_nullable
as EventFilter,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,filteredCount: null == filteredCount ? _self.filteredCount : filteredCount // ignore: cast_nullable_to_non_nullable
as int,selectedEvent: freezed == selectedEvent ? _self.selectedEvent : selectedEvent // ignore: cast_nullable_to_non_nullable
as Event?,isSearchMode: null == isSearchMode ? _self.isSearchMode : isSearchMode // ignore: cast_nullable_to_non_nullable
as bool,isRefreshing: null == isRefreshing ? _self.isRefreshing : isRefreshing // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of EventState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EventCopyWith<$Res>? get selectedEvent {
    if (_self.selectedEvent == null) {
    return null;
  }

  return $EventCopyWith<$Res>(_self.selectedEvent!, (value) {
    return _then(_self.copyWith(selectedEvent: value));
  });
}
}


/// Adds pattern-matching-related methods to [EventState].
extension EventStatePatterns on EventState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EventState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EventState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EventState value)  $default,){
final _that = this;
switch (_that) {
case _EventState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EventState value)?  $default,){
final _that = this;
switch (_that) {
case _EventState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<Event> events,  bool isLoading,  String? errorMessage,  EventFilter currentFilter,  DateTime? lastUpdated,  int totalCount,  int filteredCount,  Event? selectedEvent,  bool isSearchMode,  bool isRefreshing)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EventState() when $default != null:
return $default(_that.events,_that.isLoading,_that.errorMessage,_that.currentFilter,_that.lastUpdated,_that.totalCount,_that.filteredCount,_that.selectedEvent,_that.isSearchMode,_that.isRefreshing);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<Event> events,  bool isLoading,  String? errorMessage,  EventFilter currentFilter,  DateTime? lastUpdated,  int totalCount,  int filteredCount,  Event? selectedEvent,  bool isSearchMode,  bool isRefreshing)  $default,) {final _that = this;
switch (_that) {
case _EventState():
return $default(_that.events,_that.isLoading,_that.errorMessage,_that.currentFilter,_that.lastUpdated,_that.totalCount,_that.filteredCount,_that.selectedEvent,_that.isSearchMode,_that.isRefreshing);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<Event> events,  bool isLoading,  String? errorMessage,  EventFilter currentFilter,  DateTime? lastUpdated,  int totalCount,  int filteredCount,  Event? selectedEvent,  bool isSearchMode,  bool isRefreshing)?  $default,) {final _that = this;
switch (_that) {
case _EventState() when $default != null:
return $default(_that.events,_that.isLoading,_that.errorMessage,_that.currentFilter,_that.lastUpdated,_that.totalCount,_that.filteredCount,_that.selectedEvent,_that.isSearchMode,_that.isRefreshing);case _:
  return null;

}
}

}

/// @nodoc


class _EventState extends EventState {
  const _EventState({final  List<Event> events = const [], this.isLoading = false, this.errorMessage, this.currentFilter = EventFilter.defaultFilter, this.lastUpdated, this.totalCount = 0, this.filteredCount = 0, this.selectedEvent, this.isSearchMode = false, this.isRefreshing = false}): _events = events,super._();
  

/// 행사 목록
 final  List<Event> _events;
/// 행사 목록
@override@JsonKey() List<Event> get events {
  if (_events is EqualUnmodifiableListView) return _events;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_events);
}

/// 로딩 상태
@override@JsonKey() final  bool isLoading;
/// 에러 메시지
@override final  String? errorMessage;
/// 현재 적용된 필터
@override@JsonKey() final  EventFilter currentFilter;
/// 마지막 업데이트 시간
@override final  DateTime? lastUpdated;
/// 총 행사 개수 (필터링 전)
@override@JsonKey() final  int totalCount;
/// 필터링된 행사 개수
@override@JsonKey() final  int filteredCount;
/// 선택된 행사 (상세보기용)
@override final  Event? selectedEvent;
/// 검색 모드 여부
@override@JsonKey() final  bool isSearchMode;
/// 새로고침 중 여부
@override@JsonKey() final  bool isRefreshing;

/// Create a copy of EventState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventStateCopyWith<_EventState> get copyWith => __$EventStateCopyWithImpl<_EventState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventState&&const DeepCollectionEquality().equals(other._events, _events)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.currentFilter, currentFilter) || other.currentFilter == currentFilter)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount)&&(identical(other.filteredCount, filteredCount) || other.filteredCount == filteredCount)&&(identical(other.selectedEvent, selectedEvent) || other.selectedEvent == selectedEvent)&&(identical(other.isSearchMode, isSearchMode) || other.isSearchMode == isSearchMode)&&(identical(other.isRefreshing, isRefreshing) || other.isRefreshing == isRefreshing));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_events),isLoading,errorMessage,currentFilter,lastUpdated,totalCount,filteredCount,selectedEvent,isSearchMode,isRefreshing);

@override
String toString() {
  return 'EventState(events: $events, isLoading: $isLoading, errorMessage: $errorMessage, currentFilter: $currentFilter, lastUpdated: $lastUpdated, totalCount: $totalCount, filteredCount: $filteredCount, selectedEvent: $selectedEvent, isSearchMode: $isSearchMode, isRefreshing: $isRefreshing)';
}


}

/// @nodoc
abstract mixin class _$EventStateCopyWith<$Res> implements $EventStateCopyWith<$Res> {
  factory _$EventStateCopyWith(_EventState value, $Res Function(_EventState) _then) = __$EventStateCopyWithImpl;
@override @useResult
$Res call({
 List<Event> events, bool isLoading, String? errorMessage, EventFilter currentFilter, DateTime? lastUpdated, int totalCount, int filteredCount, Event? selectedEvent, bool isSearchMode, bool isRefreshing
});


@override $EventCopyWith<$Res>? get selectedEvent;

}
/// @nodoc
class __$EventStateCopyWithImpl<$Res>
    implements _$EventStateCopyWith<$Res> {
  __$EventStateCopyWithImpl(this._self, this._then);

  final _EventState _self;
  final $Res Function(_EventState) _then;

/// Create a copy of EventState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? events = null,Object? isLoading = null,Object? errorMessage = freezed,Object? currentFilter = null,Object? lastUpdated = freezed,Object? totalCount = null,Object? filteredCount = null,Object? selectedEvent = freezed,Object? isSearchMode = null,Object? isRefreshing = null,}) {
  return _then(_EventState(
events: null == events ? _self._events : events // ignore: cast_nullable_to_non_nullable
as List<Event>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,currentFilter: null == currentFilter ? _self.currentFilter : currentFilter // ignore: cast_nullable_to_non_nullable
as EventFilter,lastUpdated: freezed == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime?,totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,filteredCount: null == filteredCount ? _self.filteredCount : filteredCount // ignore: cast_nullable_to_non_nullable
as int,selectedEvent: freezed == selectedEvent ? _self.selectedEvent : selectedEvent // ignore: cast_nullable_to_non_nullable
as Event?,isSearchMode: null == isSearchMode ? _self.isSearchMode : isSearchMode // ignore: cast_nullable_to_non_nullable
as bool,isRefreshing: null == isRefreshing ? _self.isRefreshing : isRefreshing // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of EventState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EventCopyWith<$Res>? get selectedEvent {
    if (_self.selectedEvent == null) {
    return null;
  }

  return $EventCopyWith<$Res>(_self.selectedEvent!, (value) {
    return _then(_self.copyWith(selectedEvent: value));
  });
}
}

/// @nodoc
mixin _$EventError {

 EventErrorType get type; String get message; String? get details; DateTime? get timestamp; Map<String, dynamic>? get context;
/// Create a copy of EventError
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventErrorCopyWith<EventError> get copyWith => _$EventErrorCopyWithImpl<EventError>(this as EventError, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventError&&(identical(other.type, type) || other.type == type)&&(identical(other.message, message) || other.message == message)&&(identical(other.details, details) || other.details == details)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&const DeepCollectionEquality().equals(other.context, context));
}


@override
int get hashCode => Object.hash(runtimeType,type,message,details,timestamp,const DeepCollectionEquality().hash(context));

@override
String toString() {
  return 'EventError(type: $type, message: $message, details: $details, timestamp: $timestamp, context: $context)';
}


}

/// @nodoc
abstract mixin class $EventErrorCopyWith<$Res>  {
  factory $EventErrorCopyWith(EventError value, $Res Function(EventError) _then) = _$EventErrorCopyWithImpl;
@useResult
$Res call({
 EventErrorType type, String message, String? details, DateTime? timestamp, Map<String, dynamic>? context
});




}
/// @nodoc
class _$EventErrorCopyWithImpl<$Res>
    implements $EventErrorCopyWith<$Res> {
  _$EventErrorCopyWithImpl(this._self, this._then);

  final EventError _self;
  final $Res Function(EventError) _then;

/// Create a copy of EventError
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? message = null,Object? details = freezed,Object? timestamp = freezed,Object? context = freezed,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as EventErrorType,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,context: freezed == context ? _self.context : context // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [EventError].
extension EventErrorPatterns on EventError {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EventError value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EventError() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EventError value)  $default,){
final _that = this;
switch (_that) {
case _EventError():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EventError value)?  $default,){
final _that = this;
switch (_that) {
case _EventError() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( EventErrorType type,  String message,  String? details,  DateTime? timestamp,  Map<String, dynamic>? context)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EventError() when $default != null:
return $default(_that.type,_that.message,_that.details,_that.timestamp,_that.context);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( EventErrorType type,  String message,  String? details,  DateTime? timestamp,  Map<String, dynamic>? context)  $default,) {final _that = this;
switch (_that) {
case _EventError():
return $default(_that.type,_that.message,_that.details,_that.timestamp,_that.context);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( EventErrorType type,  String message,  String? details,  DateTime? timestamp,  Map<String, dynamic>? context)?  $default,) {final _that = this;
switch (_that) {
case _EventError() when $default != null:
return $default(_that.type,_that.message,_that.details,_that.timestamp,_that.context);case _:
  return null;

}
}

}

/// @nodoc


class _EventError extends EventError {
  const _EventError({required this.type, required this.message, this.details, this.timestamp, final  Map<String, dynamic>? context}): _context = context,super._();
  

@override final  EventErrorType type;
@override final  String message;
@override final  String? details;
@override final  DateTime? timestamp;
 final  Map<String, dynamic>? _context;
@override Map<String, dynamic>? get context {
  final value = _context;
  if (value == null) return null;
  if (_context is EqualUnmodifiableMapView) return _context;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of EventError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventErrorCopyWith<_EventError> get copyWith => __$EventErrorCopyWithImpl<_EventError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventError&&(identical(other.type, type) || other.type == type)&&(identical(other.message, message) || other.message == message)&&(identical(other.details, details) || other.details == details)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&const DeepCollectionEquality().equals(other._context, _context));
}


@override
int get hashCode => Object.hash(runtimeType,type,message,details,timestamp,const DeepCollectionEquality().hash(_context));

@override
String toString() {
  return 'EventError(type: $type, message: $message, details: $details, timestamp: $timestamp, context: $context)';
}


}

/// @nodoc
abstract mixin class _$EventErrorCopyWith<$Res> implements $EventErrorCopyWith<$Res> {
  factory _$EventErrorCopyWith(_EventError value, $Res Function(_EventError) _then) = __$EventErrorCopyWithImpl;
@override @useResult
$Res call({
 EventErrorType type, String message, String? details, DateTime? timestamp, Map<String, dynamic>? context
});




}
/// @nodoc
class __$EventErrorCopyWithImpl<$Res>
    implements _$EventErrorCopyWith<$Res> {
  __$EventErrorCopyWithImpl(this._self, this._then);

  final _EventError _self;
  final $Res Function(_EventError) _then;

/// Create a copy of EventError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? message = null,Object? details = freezed,Object? timestamp = freezed,Object? context = freezed,}) {
  return _then(_EventError(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as EventErrorType,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,context: freezed == context ? _self._context : context // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc
mixin _$EventSettings {

/// 기본 정렬 옵션
 EventSortOption get defaultSortOption;/// 기본 뷰 모드
 EventViewMode get defaultViewMode;/// 페이지당 항목 수
 int get itemsPerPage;/// 자동 새로고침 간격 (분)
 int get autoRefreshInterval;/// 검색 디바운스 시간 (밀리초)
 int get searchDebounceMs;/// 캐시 유효 시간 (분)
 int get cacheValidityMinutes;/// 오프라인 모드 지원 여부
 bool get offlineSupport;/// 자동 백업 여부
 bool get autoBackup;
/// Create a copy of EventSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventSettingsCopyWith<EventSettings> get copyWith => _$EventSettingsCopyWithImpl<EventSettings>(this as EventSettings, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventSettings&&(identical(other.defaultSortOption, defaultSortOption) || other.defaultSortOption == defaultSortOption)&&(identical(other.defaultViewMode, defaultViewMode) || other.defaultViewMode == defaultViewMode)&&(identical(other.itemsPerPage, itemsPerPage) || other.itemsPerPage == itemsPerPage)&&(identical(other.autoRefreshInterval, autoRefreshInterval) || other.autoRefreshInterval == autoRefreshInterval)&&(identical(other.searchDebounceMs, searchDebounceMs) || other.searchDebounceMs == searchDebounceMs)&&(identical(other.cacheValidityMinutes, cacheValidityMinutes) || other.cacheValidityMinutes == cacheValidityMinutes)&&(identical(other.offlineSupport, offlineSupport) || other.offlineSupport == offlineSupport)&&(identical(other.autoBackup, autoBackup) || other.autoBackup == autoBackup));
}


@override
int get hashCode => Object.hash(runtimeType,defaultSortOption,defaultViewMode,itemsPerPage,autoRefreshInterval,searchDebounceMs,cacheValidityMinutes,offlineSupport,autoBackup);

@override
String toString() {
  return 'EventSettings(defaultSortOption: $defaultSortOption, defaultViewMode: $defaultViewMode, itemsPerPage: $itemsPerPage, autoRefreshInterval: $autoRefreshInterval, searchDebounceMs: $searchDebounceMs, cacheValidityMinutes: $cacheValidityMinutes, offlineSupport: $offlineSupport, autoBackup: $autoBackup)';
}


}

/// @nodoc
abstract mixin class $EventSettingsCopyWith<$Res>  {
  factory $EventSettingsCopyWith(EventSettings value, $Res Function(EventSettings) _then) = _$EventSettingsCopyWithImpl;
@useResult
$Res call({
 EventSortOption defaultSortOption, EventViewMode defaultViewMode, int itemsPerPage, int autoRefreshInterval, int searchDebounceMs, int cacheValidityMinutes, bool offlineSupport, bool autoBackup
});




}
/// @nodoc
class _$EventSettingsCopyWithImpl<$Res>
    implements $EventSettingsCopyWith<$Res> {
  _$EventSettingsCopyWithImpl(this._self, this._then);

  final EventSettings _self;
  final $Res Function(EventSettings) _then;

/// Create a copy of EventSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? defaultSortOption = null,Object? defaultViewMode = null,Object? itemsPerPage = null,Object? autoRefreshInterval = null,Object? searchDebounceMs = null,Object? cacheValidityMinutes = null,Object? offlineSupport = null,Object? autoBackup = null,}) {
  return _then(_self.copyWith(
defaultSortOption: null == defaultSortOption ? _self.defaultSortOption : defaultSortOption // ignore: cast_nullable_to_non_nullable
as EventSortOption,defaultViewMode: null == defaultViewMode ? _self.defaultViewMode : defaultViewMode // ignore: cast_nullable_to_non_nullable
as EventViewMode,itemsPerPage: null == itemsPerPage ? _self.itemsPerPage : itemsPerPage // ignore: cast_nullable_to_non_nullable
as int,autoRefreshInterval: null == autoRefreshInterval ? _self.autoRefreshInterval : autoRefreshInterval // ignore: cast_nullable_to_non_nullable
as int,searchDebounceMs: null == searchDebounceMs ? _self.searchDebounceMs : searchDebounceMs // ignore: cast_nullable_to_non_nullable
as int,cacheValidityMinutes: null == cacheValidityMinutes ? _self.cacheValidityMinutes : cacheValidityMinutes // ignore: cast_nullable_to_non_nullable
as int,offlineSupport: null == offlineSupport ? _self.offlineSupport : offlineSupport // ignore: cast_nullable_to_non_nullable
as bool,autoBackup: null == autoBackup ? _self.autoBackup : autoBackup // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [EventSettings].
extension EventSettingsPatterns on EventSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EventSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EventSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EventSettings value)  $default,){
final _that = this;
switch (_that) {
case _EventSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EventSettings value)?  $default,){
final _that = this;
switch (_that) {
case _EventSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( EventSortOption defaultSortOption,  EventViewMode defaultViewMode,  int itemsPerPage,  int autoRefreshInterval,  int searchDebounceMs,  int cacheValidityMinutes,  bool offlineSupport,  bool autoBackup)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EventSettings() when $default != null:
return $default(_that.defaultSortOption,_that.defaultViewMode,_that.itemsPerPage,_that.autoRefreshInterval,_that.searchDebounceMs,_that.cacheValidityMinutes,_that.offlineSupport,_that.autoBackup);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( EventSortOption defaultSortOption,  EventViewMode defaultViewMode,  int itemsPerPage,  int autoRefreshInterval,  int searchDebounceMs,  int cacheValidityMinutes,  bool offlineSupport,  bool autoBackup)  $default,) {final _that = this;
switch (_that) {
case _EventSettings():
return $default(_that.defaultSortOption,_that.defaultViewMode,_that.itemsPerPage,_that.autoRefreshInterval,_that.searchDebounceMs,_that.cacheValidityMinutes,_that.offlineSupport,_that.autoBackup);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( EventSortOption defaultSortOption,  EventViewMode defaultViewMode,  int itemsPerPage,  int autoRefreshInterval,  int searchDebounceMs,  int cacheValidityMinutes,  bool offlineSupport,  bool autoBackup)?  $default,) {final _that = this;
switch (_that) {
case _EventSettings() when $default != null:
return $default(_that.defaultSortOption,_that.defaultViewMode,_that.itemsPerPage,_that.autoRefreshInterval,_that.searchDebounceMs,_that.cacheValidityMinutes,_that.offlineSupport,_that.autoBackup);case _:
  return null;

}
}

}

/// @nodoc


class _EventSettings implements EventSettings {
  const _EventSettings({this.defaultSortOption = EventSortOption.recentlyCreated, this.defaultViewMode = EventViewMode.list, this.itemsPerPage = 20, this.autoRefreshInterval = 5, this.searchDebounceMs = 300, this.cacheValidityMinutes = 10, this.offlineSupport = true, this.autoBackup = false});
  

/// 기본 정렬 옵션
@override@JsonKey() final  EventSortOption defaultSortOption;
/// 기본 뷰 모드
@override@JsonKey() final  EventViewMode defaultViewMode;
/// 페이지당 항목 수
@override@JsonKey() final  int itemsPerPage;
/// 자동 새로고침 간격 (분)
@override@JsonKey() final  int autoRefreshInterval;
/// 검색 디바운스 시간 (밀리초)
@override@JsonKey() final  int searchDebounceMs;
/// 캐시 유효 시간 (분)
@override@JsonKey() final  int cacheValidityMinutes;
/// 오프라인 모드 지원 여부
@override@JsonKey() final  bool offlineSupport;
/// 자동 백업 여부
@override@JsonKey() final  bool autoBackup;

/// Create a copy of EventSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventSettingsCopyWith<_EventSettings> get copyWith => __$EventSettingsCopyWithImpl<_EventSettings>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventSettings&&(identical(other.defaultSortOption, defaultSortOption) || other.defaultSortOption == defaultSortOption)&&(identical(other.defaultViewMode, defaultViewMode) || other.defaultViewMode == defaultViewMode)&&(identical(other.itemsPerPage, itemsPerPage) || other.itemsPerPage == itemsPerPage)&&(identical(other.autoRefreshInterval, autoRefreshInterval) || other.autoRefreshInterval == autoRefreshInterval)&&(identical(other.searchDebounceMs, searchDebounceMs) || other.searchDebounceMs == searchDebounceMs)&&(identical(other.cacheValidityMinutes, cacheValidityMinutes) || other.cacheValidityMinutes == cacheValidityMinutes)&&(identical(other.offlineSupport, offlineSupport) || other.offlineSupport == offlineSupport)&&(identical(other.autoBackup, autoBackup) || other.autoBackup == autoBackup));
}


@override
int get hashCode => Object.hash(runtimeType,defaultSortOption,defaultViewMode,itemsPerPage,autoRefreshInterval,searchDebounceMs,cacheValidityMinutes,offlineSupport,autoBackup);

@override
String toString() {
  return 'EventSettings(defaultSortOption: $defaultSortOption, defaultViewMode: $defaultViewMode, itemsPerPage: $itemsPerPage, autoRefreshInterval: $autoRefreshInterval, searchDebounceMs: $searchDebounceMs, cacheValidityMinutes: $cacheValidityMinutes, offlineSupport: $offlineSupport, autoBackup: $autoBackup)';
}


}

/// @nodoc
abstract mixin class _$EventSettingsCopyWith<$Res> implements $EventSettingsCopyWith<$Res> {
  factory _$EventSettingsCopyWith(_EventSettings value, $Res Function(_EventSettings) _then) = __$EventSettingsCopyWithImpl;
@override @useResult
$Res call({
 EventSortOption defaultSortOption, EventViewMode defaultViewMode, int itemsPerPage, int autoRefreshInterval, int searchDebounceMs, int cacheValidityMinutes, bool offlineSupport, bool autoBackup
});




}
/// @nodoc
class __$EventSettingsCopyWithImpl<$Res>
    implements _$EventSettingsCopyWith<$Res> {
  __$EventSettingsCopyWithImpl(this._self, this._then);

  final _EventSettings _self;
  final $Res Function(_EventSettings) _then;

/// Create a copy of EventSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? defaultSortOption = null,Object? defaultViewMode = null,Object? itemsPerPage = null,Object? autoRefreshInterval = null,Object? searchDebounceMs = null,Object? cacheValidityMinutes = null,Object? offlineSupport = null,Object? autoBackup = null,}) {
  return _then(_EventSettings(
defaultSortOption: null == defaultSortOption ? _self.defaultSortOption : defaultSortOption // ignore: cast_nullable_to_non_nullable
as EventSortOption,defaultViewMode: null == defaultViewMode ? _self.defaultViewMode : defaultViewMode // ignore: cast_nullable_to_non_nullable
as EventViewMode,itemsPerPage: null == itemsPerPage ? _self.itemsPerPage : itemsPerPage // ignore: cast_nullable_to_non_nullable
as int,autoRefreshInterval: null == autoRefreshInterval ? _self.autoRefreshInterval : autoRefreshInterval // ignore: cast_nullable_to_non_nullable
as int,searchDebounceMs: null == searchDebounceMs ? _self.searchDebounceMs : searchDebounceMs // ignore: cast_nullable_to_non_nullable
as int,cacheValidityMinutes: null == cacheValidityMinutes ? _self.cacheValidityMinutes : cacheValidityMinutes // ignore: cast_nullable_to_non_nullable
as int,offlineSupport: null == offlineSupport ? _self.offlineSupport : offlineSupport // ignore: cast_nullable_to_non_nullable
as bool,autoBackup: null == autoBackup ? _self.autoBackup : autoBackup // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
