// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'nickname.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Nickname {

 String get name; int? get sellerId;// nullable로 변경
 String? get profileImagePath;
/// Create a copy of Nickname
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NicknameCopyWith<Nickname> get copyWith => _$NicknameCopyWithImpl<Nickname>(this as Nickname, _$identity);

  /// Serializes this Nickname to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Nickname&&(identical(other.name, name) || other.name == name)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.profileImagePath, profileImagePath) || other.profileImagePath == profileImagePath));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,sellerId,profileImagePath);

@override
String toString() {
  return 'Nickname(name: $name, sellerId: $sellerId, profileImagePath: $profileImagePath)';
}


}

/// @nodoc
abstract mixin class $NicknameCopyWith<$Res>  {
  factory $NicknameCopyWith(Nickname value, $Res Function(Nickname) _then) = _$NicknameCopyWithImpl;
@useResult
$Res call({
 String name, int? sellerId, String? profileImagePath
});




}
/// @nodoc
class _$NicknameCopyWithImpl<$Res>
    implements $NicknameCopyWith<$Res> {
  _$NicknameCopyWithImpl(this._self, this._then);

  final Nickname _self;
  final $Res Function(Nickname) _then;

/// Create a copy of Nickname
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? sellerId = freezed,Object? profileImagePath = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int?,profileImagePath: freezed == profileImagePath ? _self.profileImagePath : profileImagePath // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Nickname].
extension NicknamePatterns on Nickname {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Nickname value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Nickname() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Nickname value)  $default,){
final _that = this;
switch (_that) {
case _Nickname():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Nickname value)?  $default,){
final _that = this;
switch (_that) {
case _Nickname() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  int? sellerId,  String? profileImagePath)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Nickname() when $default != null:
return $default(_that.name,_that.sellerId,_that.profileImagePath);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  int? sellerId,  String? profileImagePath)  $default,) {final _that = this;
switch (_that) {
case _Nickname():
return $default(_that.name,_that.sellerId,_that.profileImagePath);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  int? sellerId,  String? profileImagePath)?  $default,) {final _that = this;
switch (_that) {
case _Nickname() when $default != null:
return $default(_that.name,_that.sellerId,_that.profileImagePath);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Nickname implements Nickname {
  const _Nickname({required this.name, this.sellerId, this.profileImagePath});
  factory _Nickname.fromJson(Map<String, dynamic> json) => _$NicknameFromJson(json);

@override final  String name;
@override final  int? sellerId;
// nullable로 변경
@override final  String? profileImagePath;

/// Create a copy of Nickname
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NicknameCopyWith<_Nickname> get copyWith => __$NicknameCopyWithImpl<_Nickname>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NicknameToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Nickname&&(identical(other.name, name) || other.name == name)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.profileImagePath, profileImagePath) || other.profileImagePath == profileImagePath));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,sellerId,profileImagePath);

@override
String toString() {
  return 'Nickname(name: $name, sellerId: $sellerId, profileImagePath: $profileImagePath)';
}


}

/// @nodoc
abstract mixin class _$NicknameCopyWith<$Res> implements $NicknameCopyWith<$Res> {
  factory _$NicknameCopyWith(_Nickname value, $Res Function(_Nickname) _then) = __$NicknameCopyWithImpl;
@override @useResult
$Res call({
 String name, int? sellerId, String? profileImagePath
});




}
/// @nodoc
class __$NicknameCopyWithImpl<$Res>
    implements _$NicknameCopyWith<$Res> {
  __$NicknameCopyWithImpl(this._self, this._then);

  final _Nickname _self;
  final $Res Function(_Nickname) _then;

/// Create a copy of Nickname
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? sellerId = freezed,Object? profileImagePath = freezed,}) {
  return _then(_Nickname(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as int?,profileImagePath: freezed == profileImagePath ? _self.profileImagePath : profileImagePath // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
