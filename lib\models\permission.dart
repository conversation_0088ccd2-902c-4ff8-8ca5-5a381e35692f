/// 바라 부스 매니저 - 권한 열거형
///
/// 행사에서 사용자가 가질 수 있는 권한을 나타내는 열거형입니다.
/// - read: 데이터 읽기 권한
/// - download: PDF/엑셀 다운로드 권한
/// - prepaymentManage: 선입금 수령/미수령 조작 권한
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

/// 권한을 나타내는 열거형
enum Permission {
  /// 데이터 읽기 권한 (통계 보기, 데이터 조회)
  read('read', '데이터 보기'),
  
  /// PDF/엑셀 다운로드 권한
  download('download', '다운로드'),
  
  /// 선입금 수령/미수령 조작 권한
  prepaymentManage('prepayment_manage', '선입금 관리');

  const Permission(this.value, this.displayName);

  /// 데이터베이스 저장용 값
  final String value;
  
  /// 사용자에게 표시할 이름
  final String displayName;

  /// 문자열 값으로부터 Permission 생성
  static Permission fromString(String value) {
    switch (value) {
      case 'read':
        return Permission.read;
      case 'download':
        return Permission.download;
      case 'prepayment_manage':
        return Permission.prepaymentManage;
      default:
        throw ArgumentError('Unknown permission: $value');
    }
  }

  /// JSON 직렬화를 위한 문자열 변환
  String toJson() => value;

  /// JSON 역직렬화를 위한 팩토리 생성자
  static Permission fromJson(String json) => fromString(json);

  /// 초대받은 사용자가 기본적으로 가지는 권한들
  static List<Permission> get defaultInvitedPermissions => [
    Permission.read,
    Permission.download,
    Permission.prepaymentManage,
  ];

  /// 모든 권한 목록
  static List<Permission> get allPermissions => Permission.values;
}
