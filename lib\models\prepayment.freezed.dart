// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'prepayment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Prepayment {

 int get id; String get buyerName; String? get buyerContact;// 선택사항으로 변경
 int get amount; List<String> get pickupDays; String get productNameList; String? get purchasedProductsJson;// 구조화된 상품-수량 JSON
 String? get memo; DateTime get registrationDate; bool get isReceived; int get registrationActualDayOfWeek; String? get bankName;// 선택사항으로 변경
 String? get email;// 선택사항으로 변경
 String? get twitterAccount; int get registrationTimestamp; int get eventId;// 행사 ID 추가
 String? get orderNumber;
/// Create a copy of Prepayment
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PrepaymentCopyWith<Prepayment> get copyWith => _$PrepaymentCopyWithImpl<Prepayment>(this as Prepayment, _$identity);

  /// Serializes this Prepayment to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Prepayment&&(identical(other.id, id) || other.id == id)&&(identical(other.buyerName, buyerName) || other.buyerName == buyerName)&&(identical(other.buyerContact, buyerContact) || other.buyerContact == buyerContact)&&(identical(other.amount, amount) || other.amount == amount)&&const DeepCollectionEquality().equals(other.pickupDays, pickupDays)&&(identical(other.productNameList, productNameList) || other.productNameList == productNameList)&&(identical(other.purchasedProductsJson, purchasedProductsJson) || other.purchasedProductsJson == purchasedProductsJson)&&(identical(other.memo, memo) || other.memo == memo)&&(identical(other.registrationDate, registrationDate) || other.registrationDate == registrationDate)&&(identical(other.isReceived, isReceived) || other.isReceived == isReceived)&&(identical(other.registrationActualDayOfWeek, registrationActualDayOfWeek) || other.registrationActualDayOfWeek == registrationActualDayOfWeek)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.email, email) || other.email == email)&&(identical(other.twitterAccount, twitterAccount) || other.twitterAccount == twitterAccount)&&(identical(other.registrationTimestamp, registrationTimestamp) || other.registrationTimestamp == registrationTimestamp)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.orderNumber, orderNumber) || other.orderNumber == orderNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,buyerName,buyerContact,amount,const DeepCollectionEquality().hash(pickupDays),productNameList,purchasedProductsJson,memo,registrationDate,isReceived,registrationActualDayOfWeek,bankName,email,twitterAccount,registrationTimestamp,eventId,orderNumber);

@override
String toString() {
  return 'Prepayment(id: $id, buyerName: $buyerName, buyerContact: $buyerContact, amount: $amount, pickupDays: $pickupDays, productNameList: $productNameList, purchasedProductsJson: $purchasedProductsJson, memo: $memo, registrationDate: $registrationDate, isReceived: $isReceived, registrationActualDayOfWeek: $registrationActualDayOfWeek, bankName: $bankName, email: $email, twitterAccount: $twitterAccount, registrationTimestamp: $registrationTimestamp, eventId: $eventId, orderNumber: $orderNumber)';
}


}

/// @nodoc
abstract mixin class $PrepaymentCopyWith<$Res>  {
  factory $PrepaymentCopyWith(Prepayment value, $Res Function(Prepayment) _then) = _$PrepaymentCopyWithImpl;
@useResult
$Res call({
 int id, String buyerName, String? buyerContact, int amount, List<String> pickupDays, String productNameList, String? purchasedProductsJson, String? memo, DateTime registrationDate, bool isReceived, int registrationActualDayOfWeek, String? bankName, String? email, String? twitterAccount, int registrationTimestamp, int eventId, String? orderNumber
});




}
/// @nodoc
class _$PrepaymentCopyWithImpl<$Res>
    implements $PrepaymentCopyWith<$Res> {
  _$PrepaymentCopyWithImpl(this._self, this._then);

  final Prepayment _self;
  final $Res Function(Prepayment) _then;

/// Create a copy of Prepayment
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? buyerName = null,Object? buyerContact = freezed,Object? amount = null,Object? pickupDays = null,Object? productNameList = null,Object? purchasedProductsJson = freezed,Object? memo = freezed,Object? registrationDate = null,Object? isReceived = null,Object? registrationActualDayOfWeek = null,Object? bankName = freezed,Object? email = freezed,Object? twitterAccount = freezed,Object? registrationTimestamp = null,Object? eventId = null,Object? orderNumber = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,buyerName: null == buyerName ? _self.buyerName : buyerName // ignore: cast_nullable_to_non_nullable
as String,buyerContact: freezed == buyerContact ? _self.buyerContact : buyerContact // ignore: cast_nullable_to_non_nullable
as String?,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as int,pickupDays: null == pickupDays ? _self.pickupDays : pickupDays // ignore: cast_nullable_to_non_nullable
as List<String>,productNameList: null == productNameList ? _self.productNameList : productNameList // ignore: cast_nullable_to_non_nullable
as String,purchasedProductsJson: freezed == purchasedProductsJson ? _self.purchasedProductsJson : purchasedProductsJson // ignore: cast_nullable_to_non_nullable
as String?,memo: freezed == memo ? _self.memo : memo // ignore: cast_nullable_to_non_nullable
as String?,registrationDate: null == registrationDate ? _self.registrationDate : registrationDate // ignore: cast_nullable_to_non_nullable
as DateTime,isReceived: null == isReceived ? _self.isReceived : isReceived // ignore: cast_nullable_to_non_nullable
as bool,registrationActualDayOfWeek: null == registrationActualDayOfWeek ? _self.registrationActualDayOfWeek : registrationActualDayOfWeek // ignore: cast_nullable_to_non_nullable
as int,bankName: freezed == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,twitterAccount: freezed == twitterAccount ? _self.twitterAccount : twitterAccount // ignore: cast_nullable_to_non_nullable
as String?,registrationTimestamp: null == registrationTimestamp ? _self.registrationTimestamp : registrationTimestamp // ignore: cast_nullable_to_non_nullable
as int,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,orderNumber: freezed == orderNumber ? _self.orderNumber : orderNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Prepayment].
extension PrepaymentPatterns on Prepayment {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Prepayment value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Prepayment() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Prepayment value)  $default,){
final _that = this;
switch (_that) {
case _Prepayment():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Prepayment value)?  $default,){
final _that = this;
switch (_that) {
case _Prepayment() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String buyerName,  String? buyerContact,  int amount,  List<String> pickupDays,  String productNameList,  String? purchasedProductsJson,  String? memo,  DateTime registrationDate,  bool isReceived,  int registrationActualDayOfWeek,  String? bankName,  String? email,  String? twitterAccount,  int registrationTimestamp,  int eventId,  String? orderNumber)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Prepayment() when $default != null:
return $default(_that.id,_that.buyerName,_that.buyerContact,_that.amount,_that.pickupDays,_that.productNameList,_that.purchasedProductsJson,_that.memo,_that.registrationDate,_that.isReceived,_that.registrationActualDayOfWeek,_that.bankName,_that.email,_that.twitterAccount,_that.registrationTimestamp,_that.eventId,_that.orderNumber);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String buyerName,  String? buyerContact,  int amount,  List<String> pickupDays,  String productNameList,  String? purchasedProductsJson,  String? memo,  DateTime registrationDate,  bool isReceived,  int registrationActualDayOfWeek,  String? bankName,  String? email,  String? twitterAccount,  int registrationTimestamp,  int eventId,  String? orderNumber)  $default,) {final _that = this;
switch (_that) {
case _Prepayment():
return $default(_that.id,_that.buyerName,_that.buyerContact,_that.amount,_that.pickupDays,_that.productNameList,_that.purchasedProductsJson,_that.memo,_that.registrationDate,_that.isReceived,_that.registrationActualDayOfWeek,_that.bankName,_that.email,_that.twitterAccount,_that.registrationTimestamp,_that.eventId,_that.orderNumber);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String buyerName,  String? buyerContact,  int amount,  List<String> pickupDays,  String productNameList,  String? purchasedProductsJson,  String? memo,  DateTime registrationDate,  bool isReceived,  int registrationActualDayOfWeek,  String? bankName,  String? email,  String? twitterAccount,  int registrationTimestamp,  int eventId,  String? orderNumber)?  $default,) {final _that = this;
switch (_that) {
case _Prepayment() when $default != null:
return $default(_that.id,_that.buyerName,_that.buyerContact,_that.amount,_that.pickupDays,_that.productNameList,_that.purchasedProductsJson,_that.memo,_that.registrationDate,_that.isReceived,_that.registrationActualDayOfWeek,_that.bankName,_that.email,_that.twitterAccount,_that.registrationTimestamp,_that.eventId,_that.orderNumber);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Prepayment extends Prepayment {
  const _Prepayment({required this.id, required this.buyerName, this.buyerContact, required this.amount, required final  List<String> pickupDays, required this.productNameList, this.purchasedProductsJson, this.memo, required this.registrationDate, this.isReceived = false, required this.registrationActualDayOfWeek, this.bankName, this.email, this.twitterAccount, required this.registrationTimestamp, this.eventId = 1, this.orderNumber}): _pickupDays = pickupDays,super._();
  factory _Prepayment.fromJson(Map<String, dynamic> json) => _$PrepaymentFromJson(json);

@override final  int id;
@override final  String buyerName;
@override final  String? buyerContact;
// 선택사항으로 변경
@override final  int amount;
 final  List<String> _pickupDays;
@override List<String> get pickupDays {
  if (_pickupDays is EqualUnmodifiableListView) return _pickupDays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_pickupDays);
}

@override final  String productNameList;
@override final  String? purchasedProductsJson;
// 구조화된 상품-수량 JSON
@override final  String? memo;
@override final  DateTime registrationDate;
@override@JsonKey() final  bool isReceived;
@override final  int registrationActualDayOfWeek;
@override final  String? bankName;
// 선택사항으로 변경
@override final  String? email;
// 선택사항으로 변경
@override final  String? twitterAccount;
@override final  int registrationTimestamp;
@override@JsonKey() final  int eventId;
// 행사 ID 추가
@override final  String? orderNumber;

/// Create a copy of Prepayment
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PrepaymentCopyWith<_Prepayment> get copyWith => __$PrepaymentCopyWithImpl<_Prepayment>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PrepaymentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Prepayment&&(identical(other.id, id) || other.id == id)&&(identical(other.buyerName, buyerName) || other.buyerName == buyerName)&&(identical(other.buyerContact, buyerContact) || other.buyerContact == buyerContact)&&(identical(other.amount, amount) || other.amount == amount)&&const DeepCollectionEquality().equals(other._pickupDays, _pickupDays)&&(identical(other.productNameList, productNameList) || other.productNameList == productNameList)&&(identical(other.purchasedProductsJson, purchasedProductsJson) || other.purchasedProductsJson == purchasedProductsJson)&&(identical(other.memo, memo) || other.memo == memo)&&(identical(other.registrationDate, registrationDate) || other.registrationDate == registrationDate)&&(identical(other.isReceived, isReceived) || other.isReceived == isReceived)&&(identical(other.registrationActualDayOfWeek, registrationActualDayOfWeek) || other.registrationActualDayOfWeek == registrationActualDayOfWeek)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.email, email) || other.email == email)&&(identical(other.twitterAccount, twitterAccount) || other.twitterAccount == twitterAccount)&&(identical(other.registrationTimestamp, registrationTimestamp) || other.registrationTimestamp == registrationTimestamp)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.orderNumber, orderNumber) || other.orderNumber == orderNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,buyerName,buyerContact,amount,const DeepCollectionEquality().hash(_pickupDays),productNameList,purchasedProductsJson,memo,registrationDate,isReceived,registrationActualDayOfWeek,bankName,email,twitterAccount,registrationTimestamp,eventId,orderNumber);

@override
String toString() {
  return 'Prepayment(id: $id, buyerName: $buyerName, buyerContact: $buyerContact, amount: $amount, pickupDays: $pickupDays, productNameList: $productNameList, purchasedProductsJson: $purchasedProductsJson, memo: $memo, registrationDate: $registrationDate, isReceived: $isReceived, registrationActualDayOfWeek: $registrationActualDayOfWeek, bankName: $bankName, email: $email, twitterAccount: $twitterAccount, registrationTimestamp: $registrationTimestamp, eventId: $eventId, orderNumber: $orderNumber)';
}


}

/// @nodoc
abstract mixin class _$PrepaymentCopyWith<$Res> implements $PrepaymentCopyWith<$Res> {
  factory _$PrepaymentCopyWith(_Prepayment value, $Res Function(_Prepayment) _then) = __$PrepaymentCopyWithImpl;
@override @useResult
$Res call({
 int id, String buyerName, String? buyerContact, int amount, List<String> pickupDays, String productNameList, String? purchasedProductsJson, String? memo, DateTime registrationDate, bool isReceived, int registrationActualDayOfWeek, String? bankName, String? email, String? twitterAccount, int registrationTimestamp, int eventId, String? orderNumber
});




}
/// @nodoc
class __$PrepaymentCopyWithImpl<$Res>
    implements _$PrepaymentCopyWith<$Res> {
  __$PrepaymentCopyWithImpl(this._self, this._then);

  final _Prepayment _self;
  final $Res Function(_Prepayment) _then;

/// Create a copy of Prepayment
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? buyerName = null,Object? buyerContact = freezed,Object? amount = null,Object? pickupDays = null,Object? productNameList = null,Object? purchasedProductsJson = freezed,Object? memo = freezed,Object? registrationDate = null,Object? isReceived = null,Object? registrationActualDayOfWeek = null,Object? bankName = freezed,Object? email = freezed,Object? twitterAccount = freezed,Object? registrationTimestamp = null,Object? eventId = null,Object? orderNumber = freezed,}) {
  return _then(_Prepayment(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,buyerName: null == buyerName ? _self.buyerName : buyerName // ignore: cast_nullable_to_non_nullable
as String,buyerContact: freezed == buyerContact ? _self.buyerContact : buyerContact // ignore: cast_nullable_to_non_nullable
as String?,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as int,pickupDays: null == pickupDays ? _self._pickupDays : pickupDays // ignore: cast_nullable_to_non_nullable
as List<String>,productNameList: null == productNameList ? _self.productNameList : productNameList // ignore: cast_nullable_to_non_nullable
as String,purchasedProductsJson: freezed == purchasedProductsJson ? _self.purchasedProductsJson : purchasedProductsJson // ignore: cast_nullable_to_non_nullable
as String?,memo: freezed == memo ? _self.memo : memo // ignore: cast_nullable_to_non_nullable
as String?,registrationDate: null == registrationDate ? _self.registrationDate : registrationDate // ignore: cast_nullable_to_non_nullable
as DateTime,isReceived: null == isReceived ? _self.isReceived : isReceived // ignore: cast_nullable_to_non_nullable
as bool,registrationActualDayOfWeek: null == registrationActualDayOfWeek ? _self.registrationActualDayOfWeek : registrationActualDayOfWeek // ignore: cast_nullable_to_non_nullable
as int,bankName: freezed == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,twitterAccount: freezed == twitterAccount ? _self.twitterAccount : twitterAccount // ignore: cast_nullable_to_non_nullable
as String?,registrationTimestamp: null == registrationTimestamp ? _self.registrationTimestamp : registrationTimestamp // ignore: cast_nullable_to_non_nullable
as int,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,orderNumber: freezed == orderNumber ? _self.orderNumber : orderNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
