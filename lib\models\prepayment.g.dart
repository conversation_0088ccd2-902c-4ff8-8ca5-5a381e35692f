// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prepayment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Prepayment _$PrepaymentFromJson(Map<String, dynamic> json) => _Prepayment(
  id: (json['id'] as num).toInt(),
  buyerName: json['buyerName'] as String,
  buyerContact: json['buyerContact'] as String?,
  amount: (json['amount'] as num).toInt(),
  pickupDays: (json['pickupDays'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  productNameList: json['productNameList'] as String,
  purchasedProductsJson: json['purchasedProductsJson'] as String?,
  memo: json['memo'] as String?,
  registrationDate: DateTime.parse(json['registrationDate'] as String),
  isReceived: json['isReceived'] as bool? ?? false,
  registrationActualDayOfWeek: (json['registrationActualDayOfWeek'] as num)
      .toInt(),
  bankName: json['bankName'] as String?,
  email: json['email'] as String?,
  twitterAccount: json['twitterAccount'] as String?,
  registrationTimestamp: (json['registrationTimestamp'] as num).toInt(),
  eventId: (json['eventId'] as num?)?.toInt() ?? 1,
  orderNumber: json['orderNumber'] as String?,
);

Map<String, dynamic> _$PrepaymentToJson(_Prepayment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'buyerName': instance.buyerName,
      'buyerContact': instance.buyerContact,
      'amount': instance.amount,
      'pickupDays': instance.pickupDays,
      'productNameList': instance.productNameList,
      'purchasedProductsJson': instance.purchasedProductsJson,
      'memo': instance.memo,
      'registrationDate': instance.registrationDate.toIso8601String(),
      'isReceived': instance.isReceived,
      'registrationActualDayOfWeek': instance.registrationActualDayOfWeek,
      'bankName': instance.bankName,
      'email': instance.email,
      'twitterAccount': instance.twitterAccount,
      'registrationTimestamp': instance.registrationTimestamp,
      'eventId': instance.eventId,
      'orderNumber': instance.orderNumber,
    };
