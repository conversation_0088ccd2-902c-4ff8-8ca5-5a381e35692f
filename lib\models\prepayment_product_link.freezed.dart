// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'prepayment_product_link.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PrepaymentProductLink {

 int get virtualProductId; int get productId; DateTime get linkedAt; int get quantity; int get eventId;
/// Create a copy of PrepaymentProductLink
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PrepaymentProductLinkCopyWith<PrepaymentProductLink> get copyWith => _$PrepaymentProductLinkCopyWithImpl<PrepaymentProductLink>(this as PrepaymentProductLink, _$identity);

  /// Serializes this PrepaymentProductLink to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PrepaymentProductLink&&(identical(other.virtualProductId, virtualProductId) || other.virtualProductId == virtualProductId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.linkedAt, linkedAt) || other.linkedAt == linkedAt)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,virtualProductId,productId,linkedAt,quantity,eventId);

@override
String toString() {
  return 'PrepaymentProductLink(virtualProductId: $virtualProductId, productId: $productId, linkedAt: $linkedAt, quantity: $quantity, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class $PrepaymentProductLinkCopyWith<$Res>  {
  factory $PrepaymentProductLinkCopyWith(PrepaymentProductLink value, $Res Function(PrepaymentProductLink) _then) = _$PrepaymentProductLinkCopyWithImpl;
@useResult
$Res call({
 int virtualProductId, int productId, DateTime linkedAt, int quantity, int eventId
});




}
/// @nodoc
class _$PrepaymentProductLinkCopyWithImpl<$Res>
    implements $PrepaymentProductLinkCopyWith<$Res> {
  _$PrepaymentProductLinkCopyWithImpl(this._self, this._then);

  final PrepaymentProductLink _self;
  final $Res Function(PrepaymentProductLink) _then;

/// Create a copy of PrepaymentProductLink
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? virtualProductId = null,Object? productId = null,Object? linkedAt = null,Object? quantity = null,Object? eventId = null,}) {
  return _then(_self.copyWith(
virtualProductId: null == virtualProductId ? _self.virtualProductId : virtualProductId // ignore: cast_nullable_to_non_nullable
as int,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as int,linkedAt: null == linkedAt ? _self.linkedAt : linkedAt // ignore: cast_nullable_to_non_nullable
as DateTime,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [PrepaymentProductLink].
extension PrepaymentProductLinkPatterns on PrepaymentProductLink {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PrepaymentProductLink value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PrepaymentProductLink() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PrepaymentProductLink value)  $default,){
final _that = this;
switch (_that) {
case _PrepaymentProductLink():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PrepaymentProductLink value)?  $default,){
final _that = this;
switch (_that) {
case _PrepaymentProductLink() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int virtualProductId,  int productId,  DateTime linkedAt,  int quantity,  int eventId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PrepaymentProductLink() when $default != null:
return $default(_that.virtualProductId,_that.productId,_that.linkedAt,_that.quantity,_that.eventId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int virtualProductId,  int productId,  DateTime linkedAt,  int quantity,  int eventId)  $default,) {final _that = this;
switch (_that) {
case _PrepaymentProductLink():
return $default(_that.virtualProductId,_that.productId,_that.linkedAt,_that.quantity,_that.eventId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int virtualProductId,  int productId,  DateTime linkedAt,  int quantity,  int eventId)?  $default,) {final _that = this;
switch (_that) {
case _PrepaymentProductLink() when $default != null:
return $default(_that.virtualProductId,_that.productId,_that.linkedAt,_that.quantity,_that.eventId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PrepaymentProductLink implements PrepaymentProductLink {
  const _PrepaymentProductLink({required this.virtualProductId, required this.productId, required this.linkedAt, this.quantity = 1, required this.eventId});
  factory _PrepaymentProductLink.fromJson(Map<String, dynamic> json) => _$PrepaymentProductLinkFromJson(json);

@override final  int virtualProductId;
@override final  int productId;
@override final  DateTime linkedAt;
@override@JsonKey() final  int quantity;
@override final  int eventId;

/// Create a copy of PrepaymentProductLink
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PrepaymentProductLinkCopyWith<_PrepaymentProductLink> get copyWith => __$PrepaymentProductLinkCopyWithImpl<_PrepaymentProductLink>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PrepaymentProductLinkToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PrepaymentProductLink&&(identical(other.virtualProductId, virtualProductId) || other.virtualProductId == virtualProductId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.linkedAt, linkedAt) || other.linkedAt == linkedAt)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,virtualProductId,productId,linkedAt,quantity,eventId);

@override
String toString() {
  return 'PrepaymentProductLink(virtualProductId: $virtualProductId, productId: $productId, linkedAt: $linkedAt, quantity: $quantity, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class _$PrepaymentProductLinkCopyWith<$Res> implements $PrepaymentProductLinkCopyWith<$Res> {
  factory _$PrepaymentProductLinkCopyWith(_PrepaymentProductLink value, $Res Function(_PrepaymentProductLink) _then) = __$PrepaymentProductLinkCopyWithImpl;
@override @useResult
$Res call({
 int virtualProductId, int productId, DateTime linkedAt, int quantity, int eventId
});




}
/// @nodoc
class __$PrepaymentProductLinkCopyWithImpl<$Res>
    implements _$PrepaymentProductLinkCopyWith<$Res> {
  __$PrepaymentProductLinkCopyWithImpl(this._self, this._then);

  final _PrepaymentProductLink _self;
  final $Res Function(_PrepaymentProductLink) _then;

/// Create a copy of PrepaymentProductLink
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? virtualProductId = null,Object? productId = null,Object? linkedAt = null,Object? quantity = null,Object? eventId = null,}) {
  return _then(_PrepaymentProductLink(
virtualProductId: null == virtualProductId ? _self.virtualProductId : virtualProductId // ignore: cast_nullable_to_non_nullable
as int,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as int,linkedAt: null == linkedAt ? _self.linkedAt : linkedAt // ignore: cast_nullable_to_non_nullable
as DateTime,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
