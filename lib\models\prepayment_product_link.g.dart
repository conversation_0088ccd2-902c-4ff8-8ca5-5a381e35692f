// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prepayment_product_link.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PrepaymentProductLink _$PrepaymentProductLinkFromJson(
  Map<String, dynamic> json,
) => _PrepaymentProductLink(
  virtualProductId: (json['virtualProductId'] as num).toInt(),
  productId: (json['productId'] as num).toInt(),
  linkedAt: DateTime.parse(json['linkedAt'] as String),
  quantity: (json['quantity'] as num?)?.toInt() ?? 1,
  eventId: (json['eventId'] as num).toInt(),
);

Map<String, dynamic> _$PrepaymentProductLinkToJson(
  _PrepaymentProductLink instance,
) => <String, dynamic>{
  'virtualProductId': instance.virtualProductId,
  'productId': instance.productId,
  'linkedAt': instance.linkedAt.toIso8601String(),
  'quantity': instance.quantity,
  'eventId': instance.eventId,
};
