enum PrepaymentSortOrder {
  writtenDateDesc('WRITTEN_DATE_DESC', '작성 날짜 내림차순'),
  written<PERSON>ate<PERSON><PERSON>('WRITTEN_DATE_ASC', '작성 날짜 오름차순'),
  buyerNameAsc('BUYER_NAME_ASC', '이름순'),
  buyerNameDesc('BUYER_NAME_DESC', '이름순'),
  amountAsc('AMOUNT_ASC', '금액 오름차순'),
  amountDesc('AMOUNT_DESC', '금액 내림차순');

  const PrepaymentSortOrder(this.value, this.displayName);

  final String value;
  final String displayName;

  // 이름순 정렬인지 확인
  bool get isNameSort => this == PrepaymentSortOrder.buyerNameAsc || this == PrepaymentSortOrder.buyerNameDesc;
  
  // 오름차순인지 확인
  bool get isAscending => this == PrepaymentSortOrder.writtenDateAsc || 
                         this == PrepaymentSortOrder.buyerNameAsc || 
                         this == PrepaymentSortOrder.amountAsc;

  // 이름순 정렬 토글
  PrepaymentSortOrder toggleNameSort() {
    if (this == PrepaymentSortOrder.buyerNameAsc) {
      return PrepaymentSortOrder.buyerNameDesc;
    } else if (this == PrepaymentSortOrder.buyerNameDesc) {
      return PrepaymentSortOrder.buyerNameAsc;
    }
    return PrepaymentSortOrder.buyerNameAsc; // 기본값
  }

  static List<String> get displayNames {
    return PrepaymentSortOrder.values.map((e) => e.displayName).toList();
  }

  static PrepaymentSortOrder? fromDisplayName(String displayName) {
    for (PrepaymentSortOrder order in PrepaymentSortOrder.values) {
      if (order.displayName == displayName) {
        return order;
      }
    }
    return null;
  }

  @override
  String toString() => value;
}

// 정렬 UI용 통합 옵션 (ProductSortType과 동일한 패턴)
enum PrepaymentSortType {
  registrationDate('등록 날짜로 정렬'),
  buyerName('구매자명으로 정렬'),
  amount('금액으로 정렬');

  const PrepaymentSortType(this.displayName);

  final String displayName;

  // 현재 정렬 방향을 반전시킨 옵션 반환
  PrepaymentSortOrder getOrder(bool isAscending) {
    switch (this) {
      case PrepaymentSortType.registrationDate:
        return isAscending
            ? PrepaymentSortOrder.writtenDateAsc
            : PrepaymentSortOrder.writtenDateDesc;
      case PrepaymentSortType.buyerName:
        return isAscending
            ? PrepaymentSortOrder.buyerNameAsc
            : PrepaymentSortOrder.buyerNameDesc;
      case PrepaymentSortType.amount:
        return isAscending
            ? PrepaymentSortOrder.amountAsc
            : PrepaymentSortOrder.amountDesc;
    }
  }

  // 현재 PrepaymentSortOrder에서 SortType과 방향을 추출
  static PrepaymentSortType? fromOrder(PrepaymentSortOrder order) {
    switch (order) {
      case PrepaymentSortOrder.writtenDateAsc:
      case PrepaymentSortOrder.writtenDateDesc:
        return PrepaymentSortType.registrationDate;
      case PrepaymentSortOrder.buyerNameAsc:
      case PrepaymentSortOrder.buyerNameDesc:
        return PrepaymentSortType.buyerName;
      case PrepaymentSortOrder.amountAsc:
      case PrepaymentSortOrder.amountDesc:
        return PrepaymentSortType.amount;
    }
  }

  static bool isAscending(PrepaymentSortOrder order) {
    switch (order) {
      case PrepaymentSortOrder.writtenDateAsc:
      case PrepaymentSortOrder.buyerNameAsc:
      case PrepaymentSortOrder.amountAsc:
        return true;
      case PrepaymentSortOrder.writtenDateDesc:
      case PrepaymentSortOrder.buyerNameDesc:
      case PrepaymentSortOrder.amountDesc:
        return false;
    }
  }
}
