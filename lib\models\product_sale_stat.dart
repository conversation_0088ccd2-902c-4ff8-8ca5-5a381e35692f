import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_sale_stat.freezed.dart';
part 'product_sale_stat.g.dart';

@freezed
abstract class ProductSaleStat with _$ProductSaleStat {
  const factory ProductSaleStat({
    required String productName,
    required int totalQuantitySold,
    required int totalRevenue,
    String? sellerName,
  }) = _ProductSaleStat;

  factory ProductSaleStat.fromJson(Map<String, dynamic> json) => _$ProductSaleStatFromJson(json);

  // SQLite 맵에서 직접 생성 (하위 호환성 유지)
  factory ProductSaleStat.fromMap(Map<String, dynamic> map) {
    return ProductSaleStat(
      productName: map['productName'] ?? '',
      totalQuantitySold: map['totalQuantitySold'] ?? map['soldCount'] ?? 0,
      totalRevenue: map['totalRevenue'] ?? map['totalSalesAmount'] ?? 0,
      sellerName: map['sellerName'],
    );
  }

  // create 팩토리 메서드 (편의성을 위해)
  factory ProductSaleStat.create({
    required String productName,
    required int totalQuantitySold,
    required int totalRevenue,
    String? sellerName,
  }) {
    return ProductSaleStat(
      productName: productName,
      totalQuantitySold: totalQuantitySold,
      totalRevenue: totalRevenue,
      sellerName: sellerName,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension ProductSaleStatMapper on ProductSaleStat {
  Map<String, dynamic> toMap() {
    return {
      'productName': productName,
      'totalQuantitySold': totalQuantitySold,
      'totalRevenue': totalRevenue,
      'sellerName': sellerName,
    };
  }
}
