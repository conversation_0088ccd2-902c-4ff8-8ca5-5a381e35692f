// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_sale_stat.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductSaleStat {

 String get productName; int get totalQuantitySold; int get totalRevenue; String? get sellerName;
/// Create a copy of ProductSaleStat
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductSaleStatCopyWith<ProductSaleStat> get copyWith => _$ProductSaleStatCopyWithImpl<ProductSaleStat>(this as ProductSaleStat, _$identity);

  /// Serializes this ProductSaleStat to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductSaleStat&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.totalQuantitySold, totalQuantitySold) || other.totalQuantitySold == totalQuantitySold)&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productName,totalQuantitySold,totalRevenue,sellerName);

@override
String toString() {
  return 'ProductSaleStat(productName: $productName, totalQuantitySold: $totalQuantitySold, totalRevenue: $totalRevenue, sellerName: $sellerName)';
}


}

/// @nodoc
abstract mixin class $ProductSaleStatCopyWith<$Res>  {
  factory $ProductSaleStatCopyWith(ProductSaleStat value, $Res Function(ProductSaleStat) _then) = _$ProductSaleStatCopyWithImpl;
@useResult
$Res call({
 String productName, int totalQuantitySold, int totalRevenue, String? sellerName
});




}
/// @nodoc
class _$ProductSaleStatCopyWithImpl<$Res>
    implements $ProductSaleStatCopyWith<$Res> {
  _$ProductSaleStatCopyWithImpl(this._self, this._then);

  final ProductSaleStat _self;
  final $Res Function(ProductSaleStat) _then;

/// Create a copy of ProductSaleStat
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productName = null,Object? totalQuantitySold = null,Object? totalRevenue = null,Object? sellerName = freezed,}) {
  return _then(_self.copyWith(
productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,totalQuantitySold: null == totalQuantitySold ? _self.totalQuantitySold : totalQuantitySold // ignore: cast_nullable_to_non_nullable
as int,totalRevenue: null == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as int,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductSaleStat].
extension ProductSaleStatPatterns on ProductSaleStat {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductSaleStat value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductSaleStat() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductSaleStat value)  $default,){
final _that = this;
switch (_that) {
case _ProductSaleStat():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductSaleStat value)?  $default,){
final _that = this;
switch (_that) {
case _ProductSaleStat() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String productName,  int totalQuantitySold,  int totalRevenue,  String? sellerName)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductSaleStat() when $default != null:
return $default(_that.productName,_that.totalQuantitySold,_that.totalRevenue,_that.sellerName);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String productName,  int totalQuantitySold,  int totalRevenue,  String? sellerName)  $default,) {final _that = this;
switch (_that) {
case _ProductSaleStat():
return $default(_that.productName,_that.totalQuantitySold,_that.totalRevenue,_that.sellerName);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String productName,  int totalQuantitySold,  int totalRevenue,  String? sellerName)?  $default,) {final _that = this;
switch (_that) {
case _ProductSaleStat() when $default != null:
return $default(_that.productName,_that.totalQuantitySold,_that.totalRevenue,_that.sellerName);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductSaleStat implements ProductSaleStat {
  const _ProductSaleStat({required this.productName, required this.totalQuantitySold, required this.totalRevenue, this.sellerName});
  factory _ProductSaleStat.fromJson(Map<String, dynamic> json) => _$ProductSaleStatFromJson(json);

@override final  String productName;
@override final  int totalQuantitySold;
@override final  int totalRevenue;
@override final  String? sellerName;

/// Create a copy of ProductSaleStat
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductSaleStatCopyWith<_ProductSaleStat> get copyWith => __$ProductSaleStatCopyWithImpl<_ProductSaleStat>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductSaleStatToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductSaleStat&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.totalQuantitySold, totalQuantitySold) || other.totalQuantitySold == totalQuantitySold)&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productName,totalQuantitySold,totalRevenue,sellerName);

@override
String toString() {
  return 'ProductSaleStat(productName: $productName, totalQuantitySold: $totalQuantitySold, totalRevenue: $totalRevenue, sellerName: $sellerName)';
}


}

/// @nodoc
abstract mixin class _$ProductSaleStatCopyWith<$Res> implements $ProductSaleStatCopyWith<$Res> {
  factory _$ProductSaleStatCopyWith(_ProductSaleStat value, $Res Function(_ProductSaleStat) _then) = __$ProductSaleStatCopyWithImpl;
@override @useResult
$Res call({
 String productName, int totalQuantitySold, int totalRevenue, String? sellerName
});




}
/// @nodoc
class __$ProductSaleStatCopyWithImpl<$Res>
    implements _$ProductSaleStatCopyWith<$Res> {
  __$ProductSaleStatCopyWithImpl(this._self, this._then);

  final _ProductSaleStat _self;
  final $Res Function(_ProductSaleStat) _then;

/// Create a copy of ProductSaleStat
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productName = null,Object? totalQuantitySold = null,Object? totalRevenue = null,Object? sellerName = freezed,}) {
  return _then(_ProductSaleStat(
productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,totalQuantitySold: null == totalQuantitySold ? _self.totalQuantitySold : totalQuantitySold // ignore: cast_nullable_to_non_nullable
as int,totalRevenue: null == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as int,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
