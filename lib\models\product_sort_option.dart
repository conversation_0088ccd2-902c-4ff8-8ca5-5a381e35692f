/// 상품 정렬 옵션 열거형
enum ProductSortOption {
  nameAsc('NAME_ASC', '이름 오름차순'),
  nameDesc('NAME_DESC', '이름 내림차순'),
  quantityAsc('QUANTITY_ASC', '수량 오름차순'),
  quantityDesc('QUANTITY_DESC', '수량 내림차순'),
  priceAsc('PRICE_ASC', '가격 오름차순'),
  priceDesc('PRICE_DESC', '가격 내림차순'),
  sellerAsc('SELLER_ASC', '판매자 오름차순'),
  sellerDesc('SELLER_DESC', '판매자 내림차순'),
  recentlyAdded('RECENTLY_ADDED', '최근 추가순');

  const ProductSortOption(this.value, this.displayName);

  final String value;
  final String displayName;

  static List<String> get displayNames {
    return ProductSortOption.values.map((e) => e.displayName).toList();
  }

  static ProductSortOption? fromDisplayName(String displayName) {
    for (final option in ProductSortOption.values) {
      if (option.displayName == displayName) {
        return option;
      }
    }
    return null;
  }

  static ProductSortOption? fromValue(String value) {
    for (final option in ProductSortOption.values) {
      if (option.value == value) {
        return option;
      }
    }
    return null;
  }
}

// 정렬 UI용 통합 옵션
enum ProductSortType {
  name('이름으로 정렬'),
  quantity('수량으로 정렬'),
  price('가격으로 정렬'),
  seller('판매자로 정렬'),
  recentlyAdded('최근 추가순');

  const ProductSortType(this.displayName);

  final String displayName;

  // label: UI에서 사용할 간단한 라벨 반환
  String get label {
    switch (this) {
      case ProductSortType.name:
        return '이름순';
      case ProductSortType.quantity:
        return '수량순';
      case ProductSortType.price:
        return '가격순';
      case ProductSortType.seller:
        return '판매자순';
      case ProductSortType.recentlyAdded:
        return '최근 추가순';
    }
  }

  // 오름/내림차순 토글
  ProductSortOption toggle(ProductSortOption currentOption) {
    switch (this) {
      case ProductSortType.name:
        return currentOption == ProductSortOption.nameAsc
            ? ProductSortOption.nameDesc
            : ProductSortOption.nameAsc;
      case ProductSortType.quantity:
        return currentOption == ProductSortOption.quantityAsc
            ? ProductSortOption.quantityDesc
            : ProductSortOption.quantityAsc;
      case ProductSortType.price:
        return currentOption == ProductSortOption.priceAsc
            ? ProductSortOption.priceDesc
            : ProductSortOption.priceAsc;
      case ProductSortType.seller:
        return currentOption == ProductSortOption.sellerAsc
            ? ProductSortOption.sellerDesc
            : ProductSortOption.sellerAsc;
      case ProductSortType.recentlyAdded:
        return ProductSortOption.recentlyAdded;
    }
  }

  // 각 타입의 기본 오름차순 옵션 반환
  ProductSortOption defaultOption() {
    switch (this) {
      case ProductSortType.name:
        return ProductSortOption.nameAsc;
      case ProductSortType.quantity:
        return ProductSortOption.quantityAsc;
      case ProductSortType.price:
        return ProductSortOption.priceAsc;
      case ProductSortType.seller:
        return ProductSortOption.sellerAsc;
      case ProductSortType.recentlyAdded:
        return ProductSortOption.recentlyAdded;
    }
  }

  // 현재 정렬 방향을 반전시킨 옵션 반환
  ProductSortOption getOption(bool isAscending) {
    switch (this) {
      case ProductSortType.name:
        return isAscending
            ? ProductSortOption.nameAsc
            : ProductSortOption.nameDesc;
      case ProductSortType.quantity:
        return isAscending
            ? ProductSortOption.quantityAsc
            : ProductSortOption.quantityDesc;
      case ProductSortType.price:
        return isAscending
            ? ProductSortOption.priceAsc
            : ProductSortOption.priceDesc;
      case ProductSortType.seller:
        return isAscending
            ? ProductSortOption.sellerAsc
            : ProductSortOption.sellerDesc;
      case ProductSortType.recentlyAdded:
        return ProductSortOption.recentlyAdded;
    }
  }

  // 현재 ProductSortOption에서 SortType과 방향을 추출
  static ProductSortType? fromOption(ProductSortOption option) {
    switch (option) {
      case ProductSortOption.nameAsc:
      case ProductSortOption.nameDesc:
        return ProductSortType.name;
      case ProductSortOption.quantityAsc:
      case ProductSortOption.quantityDesc:
        return ProductSortType.quantity;
      case ProductSortOption.priceAsc:
      case ProductSortOption.priceDesc:
        return ProductSortType.price;
      case ProductSortOption.sellerAsc:
      case ProductSortOption.sellerDesc:
        return ProductSortType.seller;
      case ProductSortOption.recentlyAdded:
        return ProductSortType.recentlyAdded;
    }
  }

  // 현재 정렬 옵션이 오름차순인지 확인
  static bool isAscending(ProductSortOption option) {
    switch (option) {
      case ProductSortOption.nameAsc:
      case ProductSortOption.quantityAsc:
      case ProductSortOption.priceAsc:
      case ProductSortOption.sellerAsc:
        return true;
      case ProductSortOption.nameDesc:
      case ProductSortOption.quantityDesc:
      case ProductSortOption.priceDesc:
      case ProductSortOption.sellerDesc:
      case ProductSortOption.recentlyAdded:
        return false;
    }
  }
}

/// 🚀 **성능 최적화 예시: Freezed를 사용한 상품 필터 클래스**
/// 
/// 이 클래스는 Freezed 패키지를 활용한 성능 최적화 예시입니다.
/// 불변 객체로 설계되어 상태 변경 시 안전하고 효율적인 객체 생성을 보장합니다.
/// 
/// **Freezed의 장점:**
/// - 자동 코드 생성으로 boilerplate 제거
/// - 타입 안전성 향상
/// - copyWith 메서드 자동 생성
/// - JSON 직렬화/역직렬화 지원
/// - 성능 최적화된 equals/hashCode
class ProductFilter {
  /// 상품 필터 생성자
  const ProductFilter({
    /// 검색 키워드
    this.searchKeyword = '',
    
    /// 선택된 판매자 (빈 문자열 = 전체)
    this.selectedSeller = '',
    
    /// 카테고리 필터 (빈 문자열 = 전체)
    this.category = '',
    
    /// 정렬 옵션
    this.sortOption = ProductSortOption.recentlyAdded,
    
    /// 최소 가격 필터
    this.minPrice = 0,
    
    /// 최대 가격 필터 (0 = 제한 없음)
    this.maxPrice = 0,
    
    /// 재고 있는 상품만 표시
    this.onlyInStock = false,
    
    /// 활성 상품만 표시
    this.onlyActive = true,
    
    /// 즐겨찾기 상품만 표시
    this.onlyFavorites = false,
  });

  /// 검색 키워드
  final String searchKeyword;
  
  /// 선택된 판매자 (빈 문자열 = 전체)
  final String selectedSeller;
  
  /// 카테고리 필터 (빈 문자열 = 전체)
  final String category;
  
  /// 정렬 옵션
  final ProductSortOption sortOption;
  
  /// 최소 가격 필터
  final int minPrice;
  
  /// 최대 가격 필터 (0 = 제한 없음)
  final int maxPrice;
  
  /// 재고 있는 상품만 표시
  final bool onlyInStock;
  
  /// 활성 상품만 표시
  final bool onlyActive;
  
  /// 즐겨찾기 상품만 표시
  final bool onlyFavorites;

  /// JSON에서 생성
  factory ProductFilter.fromJson(Map<String, dynamic> json) {
    return ProductFilter(
      searchKeyword: json['searchKeyword'] ?? '',
      selectedSeller: json['selectedSeller'] ?? '',
      category: json['category'] ?? '',
      sortOption: ProductSortOption.fromValue(json['sortOption'] ?? 'RECENTLY_ADDED')!,
      minPrice: json['minPrice'] ?? 0,
      maxPrice: json['maxPrice'] ?? 0,
      onlyInStock: json['onlyInStock'] ?? false,
      onlyActive: json['onlyActive'] ?? true,
      onlyFavorites: json['onlyFavorites'] ?? false,
    );
  }

  /// JSON으로 변환
  Map<String, dynamic> toJson() {
    return {
      'searchKeyword': searchKeyword,
      'selectedSeller': selectedSeller,
      'category': category,
      'sortOption': sortOption.value,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'onlyInStock': onlyInStock,
      'onlyActive': onlyActive,
      'onlyFavorites': onlyFavorites,
    };
  }

  /// 복사본 생성 (일부 필드만 변경)
  ProductFilter copyWith({
    String? searchKeyword,
    String? selectedSeller,
    String? category,
    ProductSortOption? sortOption,
    int? minPrice,
    int? maxPrice,
    bool? onlyInStock,
    bool? onlyActive,
    bool? onlyFavorites,
  }) {
    return ProductFilter(
      searchKeyword: searchKeyword ?? this.searchKeyword,
      selectedSeller: selectedSeller ?? this.selectedSeller,
      category: category ?? this.category,
      sortOption: sortOption ?? this.sortOption,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      onlyInStock: onlyInStock ?? this.onlyInStock,
      onlyActive: onlyActive ?? this.onlyActive,
      onlyFavorites: onlyFavorites ?? this.onlyFavorites,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductFilter &&
        other.searchKeyword == searchKeyword &&
        other.selectedSeller == selectedSeller &&
        other.category == category &&
        other.sortOption == sortOption &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        other.onlyInStock == onlyInStock &&
        other.onlyActive == onlyActive &&
        other.onlyFavorites == onlyFavorites;
  }

  @override
  int get hashCode {
    return Object.hash(
      searchKeyword,
      selectedSeller,
      category,
      sortOption,
      minPrice,
      maxPrice,
      onlyInStock,
      onlyActive,
      onlyFavorites,
    );
  }
}

/// 상품 필터 확장 메서드
extension ProductFilterExtension on ProductFilter {
  /// 필터가 적용되었는지 확인
  bool get hasFilter =>
      searchKeyword.isNotEmpty ||
      selectedSeller.isNotEmpty ||
      category.isNotEmpty ||
      minPrice > 0 ||
      maxPrice > 0 ||
      onlyInStock ||
      !onlyActive ||
      onlyFavorites;

  /// 필터 초기화
  ProductFilter clearAll() => const ProductFilter();

  /// 검색 키워드만 초기화
  ProductFilter clearSearch() => copyWith(searchKeyword: '');

  /// 가격 필터만 초기화
  ProductFilter clearPriceFilter() => copyWith(minPrice: 0, maxPrice: 0);
}
