import 'package:freezed_annotation/freezed_annotation.dart';

part 'purchased_product.freezed.dart';
part 'purchased_product.g.dart';

@freezed
abstract class PurchasedProduct with _$PurchasedProduct {
  const factory PurchasedProduct({
    required String name,
    required int quantity,
    @Default(0) double price,
    @Default('') String memo,
  }) = _PurchasedProduct;

  factory PurchasedProduct.fromJson(Map<String, dynamic> json) =>
      _$PurchasedProductFromJson(json);
}
