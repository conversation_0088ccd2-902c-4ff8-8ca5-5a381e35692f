// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchased_product.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PurchasedProduct {

 String get name; int get quantity; double get price; String get memo;
/// Create a copy of PurchasedProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PurchasedProductCopyWith<PurchasedProduct> get copyWith => _$PurchasedProductCopyWithImpl<PurchasedProduct>(this as PurchasedProduct, _$identity);

  /// Serializes this PurchasedProduct to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PurchasedProduct&&(identical(other.name, name) || other.name == name)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.memo, memo) || other.memo == memo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,quantity,price,memo);

@override
String toString() {
  return 'PurchasedProduct(name: $name, quantity: $quantity, price: $price, memo: $memo)';
}


}

/// @nodoc
abstract mixin class $PurchasedProductCopyWith<$Res>  {
  factory $PurchasedProductCopyWith(PurchasedProduct value, $Res Function(PurchasedProduct) _then) = _$PurchasedProductCopyWithImpl;
@useResult
$Res call({
 String name, int quantity, double price, String memo
});




}
/// @nodoc
class _$PurchasedProductCopyWithImpl<$Res>
    implements $PurchasedProductCopyWith<$Res> {
  _$PurchasedProductCopyWithImpl(this._self, this._then);

  final PurchasedProduct _self;
  final $Res Function(PurchasedProduct) _then;

/// Create a copy of PurchasedProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? quantity = null,Object? price = null,Object? memo = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,memo: null == memo ? _self.memo : memo // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PurchasedProduct].
extension PurchasedProductPatterns on PurchasedProduct {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PurchasedProduct value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PurchasedProduct() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PurchasedProduct value)  $default,){
final _that = this;
switch (_that) {
case _PurchasedProduct():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PurchasedProduct value)?  $default,){
final _that = this;
switch (_that) {
case _PurchasedProduct() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  int quantity,  double price,  String memo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PurchasedProduct() when $default != null:
return $default(_that.name,_that.quantity,_that.price,_that.memo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  int quantity,  double price,  String memo)  $default,) {final _that = this;
switch (_that) {
case _PurchasedProduct():
return $default(_that.name,_that.quantity,_that.price,_that.memo);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  int quantity,  double price,  String memo)?  $default,) {final _that = this;
switch (_that) {
case _PurchasedProduct() when $default != null:
return $default(_that.name,_that.quantity,_that.price,_that.memo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PurchasedProduct implements PurchasedProduct {
  const _PurchasedProduct({required this.name, required this.quantity, this.price = 0, this.memo = ''});
  factory _PurchasedProduct.fromJson(Map<String, dynamic> json) => _$PurchasedProductFromJson(json);

@override final  String name;
@override final  int quantity;
@override@JsonKey() final  double price;
@override@JsonKey() final  String memo;

/// Create a copy of PurchasedProduct
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PurchasedProductCopyWith<_PurchasedProduct> get copyWith => __$PurchasedProductCopyWithImpl<_PurchasedProduct>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PurchasedProductToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PurchasedProduct&&(identical(other.name, name) || other.name == name)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.memo, memo) || other.memo == memo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,quantity,price,memo);

@override
String toString() {
  return 'PurchasedProduct(name: $name, quantity: $quantity, price: $price, memo: $memo)';
}


}

/// @nodoc
abstract mixin class _$PurchasedProductCopyWith<$Res> implements $PurchasedProductCopyWith<$Res> {
  factory _$PurchasedProductCopyWith(_PurchasedProduct value, $Res Function(_PurchasedProduct) _then) = __$PurchasedProductCopyWithImpl;
@override @useResult
$Res call({
 String name, int quantity, double price, String memo
});




}
/// @nodoc
class __$PurchasedProductCopyWithImpl<$Res>
    implements _$PurchasedProductCopyWith<$Res> {
  __$PurchasedProductCopyWithImpl(this._self, this._then);

  final _PurchasedProduct _self;
  final $Res Function(_PurchasedProduct) _then;

/// Create a copy of PurchasedProduct
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? quantity = null,Object? price = null,Object? memo = null,}) {
  return _then(_PurchasedProduct(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,memo: null == memo ? _self.memo : memo // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
