/// 목표 수익 모델
///
/// 판매자별, 날짜별 목표 수익을 관리하는 모델입니다.
class RevenueGoal {
  /// 고유 ID
  final String? id;

  /// 행사 ID
  final int eventId;

  /// 판매자 ID (null이면 전체 목표)
  final String? sellerId;

  /// 목표 날짜 (YYYY-MM-DD 형식)
  final String date;

  /// 목표 금액
  final double targetAmount;

  /// 생성 시간
  final DateTime createdAt;

  /// 수정 시간
  final DateTime updatedAt;

  const RevenueGoal({
    this.id,
    required this.eventId,
    this.sellerId,
    required this.date,
    required this.targetAmount,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 새 목표 수익 생성
  factory RevenueGoal.create({
    required int eventId,
    String? sellerId,
    required String date,
    required double targetAmount,
  }) {
    final now = DateTime.now();
    return RevenueGoal(
      eventId: eventId,
      sellerId: sellerId,
      date: date,
      targetAmount: targetAmount,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// JSON에서 생성
  factory RevenueGoal.fromJson(Map<String, dynamic> json) {
    return RevenueGoal(
      id: json['id'] as String?,
      eventId: json['eventId'] as int,
      sellerId: json['sellerId'] as String?,
      date: json['date'] as String,
      targetAmount: (json['targetAmount'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// JSON으로 변환
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'eventId': eventId,
      'sellerId': sellerId,
      'date': date,
      'targetAmount': targetAmount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 복사본 생성
  RevenueGoal copyWith({
    String? id,
    int? eventId,
    String? sellerId,
    String? date,
    double? targetAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RevenueGoal(
      id: id ?? this.id,
      eventId: eventId ?? this.eventId,
      sellerId: sellerId ?? this.sellerId,
      date: date ?? this.date,
      targetAmount: targetAmount ?? this.targetAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RevenueGoal &&
        other.id == id &&
        other.eventId == eventId &&
        other.sellerId == sellerId &&
        other.date == date &&
        other.targetAmount == targetAmount &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      eventId,
      sellerId,
      date,
      targetAmount,
      createdAt,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'RevenueGoal(id: $id, eventId: $eventId, sellerId: $sellerId, date: $date, targetAmount: $targetAmount, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 목표 수익 상태 모델
class RevenueGoalState {
  final List<RevenueGoal> goals;
  final bool isLoading;
  final bool isUpdating;
  final String? errorMessage;

  const RevenueGoalState({
    this.goals = const [],
    this.isLoading = false,
    this.isUpdating = false,
    this.errorMessage,
  });

  RevenueGoalState copyWith({
    List<RevenueGoal>? goals,
    bool? isLoading,
    bool? isUpdating,
    String? errorMessage,
  }) {
    return RevenueGoalState(
      goals: goals ?? this.goals,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RevenueGoalState &&
        other.goals == goals &&
        other.isLoading == isLoading &&
        other.isUpdating == isUpdating &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(goals, isLoading, isUpdating, errorMessage);
  }
}

/// 목표 수익 통계 모델
class RevenueGoalStats {
  /// 목표 수익 총합
  final double totalTarget;

  /// 실제 수익 총합
  final double totalActual;

  /// 달성률 (0.0 ~ 1.0)
  final double achievementRate;

  /// 목표 대비 차이 (실제 - 목표)
  final double difference;

  const RevenueGoalStats({
    this.totalTarget = 0.0,
    this.totalActual = 0.0,
    this.achievementRate = 0.0,
    this.difference = 0.0,
  });

  RevenueGoalStats copyWith({
    double? totalTarget,
    double? totalActual,
    double? achievementRate,
    double? difference,
  }) {
    return RevenueGoalStats(
      totalTarget: totalTarget ?? this.totalTarget,
      totalActual: totalActual ?? this.totalActual,
      achievementRate: achievementRate ?? this.achievementRate,
      difference: difference ?? this.difference,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RevenueGoalStats &&
        other.totalTarget == totalTarget &&
        other.totalActual == totalActual &&
        other.achievementRate == achievementRate &&
        other.difference == difference;
  }

  @override
  int get hashCode {
    return Object.hash(totalTarget, totalActual, achievementRate, difference);
  }
}
