import 'package:freezed_annotation/freezed_annotation.dart';

part 'sale.freezed.dart';
part 'sale.g.dart';

/// 판매(거래) 정보를 표현하는 데이터 모델 클래스입니다.
/// - 판매명, 판매자, 이미지 등 다양한 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
@freezed
abstract class Sale with _$Sale {
  const factory Sale({
    int? id,
    required int productId,
    required int quantity,
    required int totalPrice,
    required DateTime saleTimestamp,
    String? name,
    String? sellerName,
    String? imagePath,
  }) = _Sale;

  factory Sale.fromJson(Map<String, dynamic> json) => _$SaleFromJson(json);

  // SQLite 맵에서 직접 생성
  factory Sale.fromMap(Map<String, dynamic> map) {
    return Sale(
      id: map['id'],
      productId: map['productId'] ?? 0,
      quantity: map['quantity'] ?? 0,
      totalPrice: map['totalPrice'] ?? 0,
      saleTimestamp: map['saleTimestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['saleTimestamp'])
          : DateTime.now(),
      name: map['name'],
      sellerName: map['sellerName'],
      imagePath: map['imagePath'],
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory Sale.create({
    int? id,
    required int productId,
    required int quantity,
    required int totalPrice,
    DateTime? saleTimestamp,
    String? name,
    String? sellerName,
    String? imagePath,
  }) {
    return Sale(
      id: id,
      productId: productId,
      quantity: quantity,
      totalPrice: totalPrice,
      saleTimestamp: saleTimestamp ?? DateTime.now(),
      name: name,
      sellerName: sellerName,
      imagePath: imagePath,
    );
  }
}

// 할인 금액 (할인 기능 제거로 항상 0 반환)
extension SaleDiscount on Sale {
  int get discountAmount => 0;
}

// SQLite 맵 변환을 위한 Extension
extension SaleMapper on Sale {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'quantity': quantity,
      'totalPrice': totalPrice,
      'saleTimestamp': saleTimestamp.millisecondsSinceEpoch,
      'name': name,
      'sellerName': sellerName,
      'imagePath': imagePath,
    };
  }
}
