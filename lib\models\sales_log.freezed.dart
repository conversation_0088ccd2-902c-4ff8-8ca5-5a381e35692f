// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sales_log.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesLog {

 int get id; int? get productId; String get productName; String? get sellerName; int get soldPrice; int get soldQuantity; int get totalAmount; int get saleTimestamp; TransactionType get transactionType; String? get batchSaleId; int get eventId;// 행사 ID 추가
 int get setDiscountAmount;// 세트 할인 금액
 String? get setDiscountNames;// 적용된 세트 할인 이름들 (JSON 문자열)
 int get manualDiscountAmount;// 수동 할인 금액
 String? get paymentMethod;
/// Create a copy of SalesLog
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesLogCopyWith<SalesLog> get copyWith => _$SalesLogCopyWithImpl<SalesLog>(this as SalesLog, _$identity);

  /// Serializes this SalesLog to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesLog&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.soldPrice, soldPrice) || other.soldPrice == soldPrice)&&(identical(other.soldQuantity, soldQuantity) || other.soldQuantity == soldQuantity)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.saleTimestamp, saleTimestamp) || other.saleTimestamp == saleTimestamp)&&(identical(other.transactionType, transactionType) || other.transactionType == transactionType)&&(identical(other.batchSaleId, batchSaleId) || other.batchSaleId == batchSaleId)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.setDiscountAmount, setDiscountAmount) || other.setDiscountAmount == setDiscountAmount)&&(identical(other.setDiscountNames, setDiscountNames) || other.setDiscountNames == setDiscountNames)&&(identical(other.manualDiscountAmount, manualDiscountAmount) || other.manualDiscountAmount == manualDiscountAmount)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,productName,sellerName,soldPrice,soldQuantity,totalAmount,saleTimestamp,transactionType,batchSaleId,eventId,setDiscountAmount,setDiscountNames,manualDiscountAmount,paymentMethod);

@override
String toString() {
  return 'SalesLog(id: $id, productId: $productId, productName: $productName, sellerName: $sellerName, soldPrice: $soldPrice, soldQuantity: $soldQuantity, totalAmount: $totalAmount, saleTimestamp: $saleTimestamp, transactionType: $transactionType, batchSaleId: $batchSaleId, eventId: $eventId, setDiscountAmount: $setDiscountAmount, setDiscountNames: $setDiscountNames, manualDiscountAmount: $manualDiscountAmount, paymentMethod: $paymentMethod)';
}


}

/// @nodoc
abstract mixin class $SalesLogCopyWith<$Res>  {
  factory $SalesLogCopyWith(SalesLog value, $Res Function(SalesLog) _then) = _$SalesLogCopyWithImpl;
@useResult
$Res call({
 int id, int? productId, String productName, String? sellerName, int soldPrice, int soldQuantity, int totalAmount, int saleTimestamp, TransactionType transactionType, String? batchSaleId, int eventId, int setDiscountAmount, String? setDiscountNames, int manualDiscountAmount, String? paymentMethod
});




}
/// @nodoc
class _$SalesLogCopyWithImpl<$Res>
    implements $SalesLogCopyWith<$Res> {
  _$SalesLogCopyWithImpl(this._self, this._then);

  final SalesLog _self;
  final $Res Function(SalesLog) _then;

/// Create a copy of SalesLog
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productId = freezed,Object? productName = null,Object? sellerName = freezed,Object? soldPrice = null,Object? soldQuantity = null,Object? totalAmount = null,Object? saleTimestamp = null,Object? transactionType = null,Object? batchSaleId = freezed,Object? eventId = null,Object? setDiscountAmount = null,Object? setDiscountNames = freezed,Object? manualDiscountAmount = null,Object? paymentMethod = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as int?,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,soldPrice: null == soldPrice ? _self.soldPrice : soldPrice // ignore: cast_nullable_to_non_nullable
as int,soldQuantity: null == soldQuantity ? _self.soldQuantity : soldQuantity // ignore: cast_nullable_to_non_nullable
as int,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as int,saleTimestamp: null == saleTimestamp ? _self.saleTimestamp : saleTimestamp // ignore: cast_nullable_to_non_nullable
as int,transactionType: null == transactionType ? _self.transactionType : transactionType // ignore: cast_nullable_to_non_nullable
as TransactionType,batchSaleId: freezed == batchSaleId ? _self.batchSaleId : batchSaleId // ignore: cast_nullable_to_non_nullable
as String?,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,setDiscountAmount: null == setDiscountAmount ? _self.setDiscountAmount : setDiscountAmount // ignore: cast_nullable_to_non_nullable
as int,setDiscountNames: freezed == setDiscountNames ? _self.setDiscountNames : setDiscountNames // ignore: cast_nullable_to_non_nullable
as String?,manualDiscountAmount: null == manualDiscountAmount ? _self.manualDiscountAmount : manualDiscountAmount // ignore: cast_nullable_to_non_nullable
as int,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [SalesLog].
extension SalesLogPatterns on SalesLog {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SalesLog value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SalesLog() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SalesLog value)  $default,){
final _that = this;
switch (_that) {
case _SalesLog():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SalesLog value)?  $default,){
final _that = this;
switch (_that) {
case _SalesLog() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int? productId,  String productName,  String? sellerName,  int soldPrice,  int soldQuantity,  int totalAmount,  int saleTimestamp,  TransactionType transactionType,  String? batchSaleId,  int eventId,  int setDiscountAmount,  String? setDiscountNames,  int manualDiscountAmount,  String? paymentMethod)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SalesLog() when $default != null:
return $default(_that.id,_that.productId,_that.productName,_that.sellerName,_that.soldPrice,_that.soldQuantity,_that.totalAmount,_that.saleTimestamp,_that.transactionType,_that.batchSaleId,_that.eventId,_that.setDiscountAmount,_that.setDiscountNames,_that.manualDiscountAmount,_that.paymentMethod);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int? productId,  String productName,  String? sellerName,  int soldPrice,  int soldQuantity,  int totalAmount,  int saleTimestamp,  TransactionType transactionType,  String? batchSaleId,  int eventId,  int setDiscountAmount,  String? setDiscountNames,  int manualDiscountAmount,  String? paymentMethod)  $default,) {final _that = this;
switch (_that) {
case _SalesLog():
return $default(_that.id,_that.productId,_that.productName,_that.sellerName,_that.soldPrice,_that.soldQuantity,_that.totalAmount,_that.saleTimestamp,_that.transactionType,_that.batchSaleId,_that.eventId,_that.setDiscountAmount,_that.setDiscountNames,_that.manualDiscountAmount,_that.paymentMethod);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int? productId,  String productName,  String? sellerName,  int soldPrice,  int soldQuantity,  int totalAmount,  int saleTimestamp,  TransactionType transactionType,  String? batchSaleId,  int eventId,  int setDiscountAmount,  String? setDiscountNames,  int manualDiscountAmount,  String? paymentMethod)?  $default,) {final _that = this;
switch (_that) {
case _SalesLog() when $default != null:
return $default(_that.id,_that.productId,_that.productName,_that.sellerName,_that.soldPrice,_that.soldQuantity,_that.totalAmount,_that.saleTimestamp,_that.transactionType,_that.batchSaleId,_that.eventId,_that.setDiscountAmount,_that.setDiscountNames,_that.manualDiscountAmount,_that.paymentMethod);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SalesLog implements SalesLog {
  const _SalesLog({required this.id, this.productId, required this.productName, this.sellerName, required this.soldPrice, required this.soldQuantity, required this.totalAmount, required this.saleTimestamp, this.transactionType = TransactionType.sale, this.batchSaleId, this.eventId = 1, this.setDiscountAmount = 0, this.setDiscountNames, this.manualDiscountAmount = 0, this.paymentMethod = null});
  factory _SalesLog.fromJson(Map<String, dynamic> json) => _$SalesLogFromJson(json);

@override final  int id;
@override final  int? productId;
@override final  String productName;
@override final  String? sellerName;
@override final  int soldPrice;
@override final  int soldQuantity;
@override final  int totalAmount;
@override final  int saleTimestamp;
@override@JsonKey() final  TransactionType transactionType;
@override final  String? batchSaleId;
@override@JsonKey() final  int eventId;
// 행사 ID 추가
@override@JsonKey() final  int setDiscountAmount;
// 세트 할인 금액
@override final  String? setDiscountNames;
// 적용된 세트 할인 이름들 (JSON 문자열)
@override@JsonKey() final  int manualDiscountAmount;
// 수동 할인 금액
@override@JsonKey() final  String? paymentMethod;

/// Create a copy of SalesLog
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesLogCopyWith<_SalesLog> get copyWith => __$SalesLogCopyWithImpl<_SalesLog>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SalesLogToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesLog&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.soldPrice, soldPrice) || other.soldPrice == soldPrice)&&(identical(other.soldQuantity, soldQuantity) || other.soldQuantity == soldQuantity)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.saleTimestamp, saleTimestamp) || other.saleTimestamp == saleTimestamp)&&(identical(other.transactionType, transactionType) || other.transactionType == transactionType)&&(identical(other.batchSaleId, batchSaleId) || other.batchSaleId == batchSaleId)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.setDiscountAmount, setDiscountAmount) || other.setDiscountAmount == setDiscountAmount)&&(identical(other.setDiscountNames, setDiscountNames) || other.setDiscountNames == setDiscountNames)&&(identical(other.manualDiscountAmount, manualDiscountAmount) || other.manualDiscountAmount == manualDiscountAmount)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,productName,sellerName,soldPrice,soldQuantity,totalAmount,saleTimestamp,transactionType,batchSaleId,eventId,setDiscountAmount,setDiscountNames,manualDiscountAmount,paymentMethod);

@override
String toString() {
  return 'SalesLog(id: $id, productId: $productId, productName: $productName, sellerName: $sellerName, soldPrice: $soldPrice, soldQuantity: $soldQuantity, totalAmount: $totalAmount, saleTimestamp: $saleTimestamp, transactionType: $transactionType, batchSaleId: $batchSaleId, eventId: $eventId, setDiscountAmount: $setDiscountAmount, setDiscountNames: $setDiscountNames, manualDiscountAmount: $manualDiscountAmount, paymentMethod: $paymentMethod)';
}


}

/// @nodoc
abstract mixin class _$SalesLogCopyWith<$Res> implements $SalesLogCopyWith<$Res> {
  factory _$SalesLogCopyWith(_SalesLog value, $Res Function(_SalesLog) _then) = __$SalesLogCopyWithImpl;
@override @useResult
$Res call({
 int id, int? productId, String productName, String? sellerName, int soldPrice, int soldQuantity, int totalAmount, int saleTimestamp, TransactionType transactionType, String? batchSaleId, int eventId, int setDiscountAmount, String? setDiscountNames, int manualDiscountAmount, String? paymentMethod
});




}
/// @nodoc
class __$SalesLogCopyWithImpl<$Res>
    implements _$SalesLogCopyWith<$Res> {
  __$SalesLogCopyWithImpl(this._self, this._then);

  final _SalesLog _self;
  final $Res Function(_SalesLog) _then;

/// Create a copy of SalesLog
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productId = freezed,Object? productName = null,Object? sellerName = freezed,Object? soldPrice = null,Object? soldQuantity = null,Object? totalAmount = null,Object? saleTimestamp = null,Object? transactionType = null,Object? batchSaleId = freezed,Object? eventId = null,Object? setDiscountAmount = null,Object? setDiscountNames = freezed,Object? manualDiscountAmount = null,Object? paymentMethod = freezed,}) {
  return _then(_SalesLog(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as int?,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,soldPrice: null == soldPrice ? _self.soldPrice : soldPrice // ignore: cast_nullable_to_non_nullable
as int,soldQuantity: null == soldQuantity ? _self.soldQuantity : soldQuantity // ignore: cast_nullable_to_non_nullable
as int,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as int,saleTimestamp: null == saleTimestamp ? _self.saleTimestamp : saleTimestamp // ignore: cast_nullable_to_non_nullable
as int,transactionType: null == transactionType ? _self.transactionType : transactionType // ignore: cast_nullable_to_non_nullable
as TransactionType,batchSaleId: freezed == batchSaleId ? _self.batchSaleId : batchSaleId // ignore: cast_nullable_to_non_nullable
as String?,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,setDiscountAmount: null == setDiscountAmount ? _self.setDiscountAmount : setDiscountAmount // ignore: cast_nullable_to_non_nullable
as int,setDiscountNames: freezed == setDiscountNames ? _self.setDiscountNames : setDiscountNames // ignore: cast_nullable_to_non_nullable
as String?,manualDiscountAmount: null == manualDiscountAmount ? _self.manualDiscountAmount : manualDiscountAmount // ignore: cast_nullable_to_non_nullable
as int,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
