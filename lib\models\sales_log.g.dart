// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SalesLog _$SalesLogFromJson(Map<String, dynamic> json) => _SalesLog(
  id: (json['id'] as num).toInt(),
  productId: (json['productId'] as num?)?.toInt(),
  productName: json['productName'] as String,
  sellerName: json['sellerName'] as String?,
  soldPrice: (json['soldPrice'] as num).toInt(),
  soldQuantity: (json['soldQuantity'] as num).toInt(),
  totalAmount: (json['totalAmount'] as num).toInt(),
  saleTimestamp: (json['saleTimestamp'] as num).toInt(),
  transactionType:
      $enumDecodeNullable(_$TransactionTypeEnumMap, json['transactionType']) ??
      TransactionType.sale,
  batchSaleId: json['batchSaleId'] as String?,
  eventId: (json['eventId'] as num?)?.toInt() ?? 1,
  setDiscountAmount: (json['setDiscountAmount'] as num?)?.toInt() ?? 0,
  setDiscountNames: json['setDiscountNames'] as String?,
  manualDiscountAmount: (json['manualDiscountAmount'] as num?)?.toInt() ?? 0,
  paymentMethod: json['paymentMethod'] as String? ?? null,
);

Map<String, dynamic> _$SalesLogToJson(_SalesLog instance) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'productName': instance.productName,
  'sellerName': instance.sellerName,
  'soldPrice': instance.soldPrice,
  'soldQuantity': instance.soldQuantity,
  'totalAmount': instance.totalAmount,
  'saleTimestamp': instance.saleTimestamp,
  'transactionType': _$TransactionTypeEnumMap[instance.transactionType]!,
  'batchSaleId': instance.batchSaleId,
  'eventId': instance.eventId,
  'setDiscountAmount': instance.setDiscountAmount,
  'setDiscountNames': instance.setDiscountNames,
  'manualDiscountAmount': instance.manualDiscountAmount,
  'paymentMethod': instance.paymentMethod,
};

const _$TransactionTypeEnumMap = {
  TransactionType.sale: 'sale',
  TransactionType.service: 'service',
  TransactionType.discount: 'discount',
  TransactionType.setDiscount: 'setDiscount',
};
