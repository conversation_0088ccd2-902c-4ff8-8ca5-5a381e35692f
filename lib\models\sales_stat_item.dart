import 'package:freezed_annotation/freezed_annotation.dart';

part 'sales_stat_item.freezed.dart';
part 'sales_stat_item.g.dart';

@freezed
abstract class SalesStatItem with _$SalesStatItem {
  const factory SalesStatItem({
    required String name,
    required int count,
    required double totalAmount,
    @Default(0.0) double totalDiscountAmount,
    @Default(0.0) double netSalesAmount,
  }) = _SalesStatItem;

  factory SalesStatItem.fromJson(Map<String, dynamic> json) => _$SalesStatItemFromJson(json);

  // SQLite 맵에서 직접 생성
  factory SalesStatItem.fromMap(Map<String, dynamic> map) {
    return SalesStatItem(
      name: map['name'] ?? map['sellerName'] ?? '',
      count: map['count'] ?? map['salesCount'] ?? 0,
      totalAmount: (map['totalAmount'] ?? map['totalSalesAmount'] ?? 0.0)
          .toDouble(),
      totalDiscountAmount: (map['totalDiscountAmount'] ?? 0.0).toDouble(),
      netSalesAmount: (map['netSalesAmount'] ?? 0.0).toDouble(),
    );
  }

  // create 팩토리 메서드 (편의성을 위해)
  factory SalesStatItem.create({
    required String name,
    required int count,
    required double totalAmount,
    double totalDiscountAmount = 0.0,
    double netSalesAmount = 0.0,
  }) {
    return SalesStatItem(
      name: name,
      count: count,
      totalAmount: totalAmount,
      totalDiscountAmount: totalDiscountAmount,
      netSalesAmount: netSalesAmount,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension SalesStatItemMapper on SalesStatItem {
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'count': count,
      'totalAmount': totalAmount,
      'totalDiscountAmount': totalDiscountAmount,
      'netSalesAmount': netSalesAmount,
    };
  }
}
