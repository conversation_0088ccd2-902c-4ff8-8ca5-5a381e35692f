// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sales_stat_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SalesStatItem {

 String get name; int get count; double get totalAmount; double get totalDiscountAmount; double get netSalesAmount;
/// Create a copy of SalesStatItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesStatItemCopyWith<SalesStatItem> get copyWith => _$SalesStatItemCopyWithImpl<SalesStatItem>(this as SalesStatItem, _$identity);

  /// Serializes this SalesStatItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesStatItem&&(identical(other.name, name) || other.name == name)&&(identical(other.count, count) || other.count == count)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.totalDiscountAmount, totalDiscountAmount) || other.totalDiscountAmount == totalDiscountAmount)&&(identical(other.netSalesAmount, netSalesAmount) || other.netSalesAmount == netSalesAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,count,totalAmount,totalDiscountAmount,netSalesAmount);

@override
String toString() {
  return 'SalesStatItem(name: $name, count: $count, totalAmount: $totalAmount, totalDiscountAmount: $totalDiscountAmount, netSalesAmount: $netSalesAmount)';
}


}

/// @nodoc
abstract mixin class $SalesStatItemCopyWith<$Res>  {
  factory $SalesStatItemCopyWith(SalesStatItem value, $Res Function(SalesStatItem) _then) = _$SalesStatItemCopyWithImpl;
@useResult
$Res call({
 String name, int count, double totalAmount, double totalDiscountAmount, double netSalesAmount
});




}
/// @nodoc
class _$SalesStatItemCopyWithImpl<$Res>
    implements $SalesStatItemCopyWith<$Res> {
  _$SalesStatItemCopyWithImpl(this._self, this._then);

  final SalesStatItem _self;
  final $Res Function(SalesStatItem) _then;

/// Create a copy of SalesStatItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? count = null,Object? totalAmount = null,Object? totalDiscountAmount = null,Object? netSalesAmount = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,totalDiscountAmount: null == totalDiscountAmount ? _self.totalDiscountAmount : totalDiscountAmount // ignore: cast_nullable_to_non_nullable
as double,netSalesAmount: null == netSalesAmount ? _self.netSalesAmount : netSalesAmount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [SalesStatItem].
extension SalesStatItemPatterns on SalesStatItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SalesStatItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SalesStatItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SalesStatItem value)  $default,){
final _that = this;
switch (_that) {
case _SalesStatItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SalesStatItem value)?  $default,){
final _that = this;
switch (_that) {
case _SalesStatItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  int count,  double totalAmount,  double totalDiscountAmount,  double netSalesAmount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SalesStatItem() when $default != null:
return $default(_that.name,_that.count,_that.totalAmount,_that.totalDiscountAmount,_that.netSalesAmount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  int count,  double totalAmount,  double totalDiscountAmount,  double netSalesAmount)  $default,) {final _that = this;
switch (_that) {
case _SalesStatItem():
return $default(_that.name,_that.count,_that.totalAmount,_that.totalDiscountAmount,_that.netSalesAmount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  int count,  double totalAmount,  double totalDiscountAmount,  double netSalesAmount)?  $default,) {final _that = this;
switch (_that) {
case _SalesStatItem() when $default != null:
return $default(_that.name,_that.count,_that.totalAmount,_that.totalDiscountAmount,_that.netSalesAmount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SalesStatItem implements SalesStatItem {
  const _SalesStatItem({required this.name, required this.count, required this.totalAmount, this.totalDiscountAmount = 0.0, this.netSalesAmount = 0.0});
  factory _SalesStatItem.fromJson(Map<String, dynamic> json) => _$SalesStatItemFromJson(json);

@override final  String name;
@override final  int count;
@override final  double totalAmount;
@override@JsonKey() final  double totalDiscountAmount;
@override@JsonKey() final  double netSalesAmount;

/// Create a copy of SalesStatItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesStatItemCopyWith<_SalesStatItem> get copyWith => __$SalesStatItemCopyWithImpl<_SalesStatItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SalesStatItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesStatItem&&(identical(other.name, name) || other.name == name)&&(identical(other.count, count) || other.count == count)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.totalDiscountAmount, totalDiscountAmount) || other.totalDiscountAmount == totalDiscountAmount)&&(identical(other.netSalesAmount, netSalesAmount) || other.netSalesAmount == netSalesAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,count,totalAmount,totalDiscountAmount,netSalesAmount);

@override
String toString() {
  return 'SalesStatItem(name: $name, count: $count, totalAmount: $totalAmount, totalDiscountAmount: $totalDiscountAmount, netSalesAmount: $netSalesAmount)';
}


}

/// @nodoc
abstract mixin class _$SalesStatItemCopyWith<$Res> implements $SalesStatItemCopyWith<$Res> {
  factory _$SalesStatItemCopyWith(_SalesStatItem value, $Res Function(_SalesStatItem) _then) = __$SalesStatItemCopyWithImpl;
@override @useResult
$Res call({
 String name, int count, double totalAmount, double totalDiscountAmount, double netSalesAmount
});




}
/// @nodoc
class __$SalesStatItemCopyWithImpl<$Res>
    implements _$SalesStatItemCopyWith<$Res> {
  __$SalesStatItemCopyWithImpl(this._self, this._then);

  final _SalesStatItem _self;
  final $Res Function(_SalesStatItem) _then;

/// Create a copy of SalesStatItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? count = null,Object? totalAmount = null,Object? totalDiscountAmount = null,Object? netSalesAmount = null,}) {
  return _then(_SalesStatItem(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,totalDiscountAmount: null == totalDiscountAmount ? _self.totalDiscountAmount : totalDiscountAmount // ignore: cast_nullable_to_non_nullable
as double,netSalesAmount: null == netSalesAmount ? _self.netSalesAmount : netSalesAmount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
