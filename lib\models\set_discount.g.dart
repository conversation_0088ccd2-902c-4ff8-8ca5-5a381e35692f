// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'set_discount.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SetDiscount _$SetDiscountFromJson(Map<String, dynamic> json) => _SetDiscount(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  discountAmount: (json['discountAmount'] as num).toInt(),
  conditionType:
      $enumDecodeNullable(
        _$SetDiscountConditionTypeEnumMap,
        json['conditionType'],
      ) ??
      SetDiscountConditionType.productCombination,
  productIds:
      (json['productIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      const [],
  minimumAmount: (json['minimumAmount'] as num?)?.toInt() ?? 0,
  categoryCondition: json['categoryCondition'] == null
      ? null
      : CategoryQuantityCondition.fromJson(
          json['categoryCondition'] as Map<String, dynamic>,
        ),
  productGroupCondition: json['productGroupCondition'] == null
      ? null
      : ProductGroupQuantityCondition.fromJson(
          json['productGroupCondition'] as Map<String, dynamic>,
        ),
  allowMultipleApplications:
      json['allowMultipleApplications'] as bool? ?? false,
  isActive: json['isActive'] as bool? ?? true,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  eventId: (json['eventId'] as num?)?.toInt() ?? 1,
);

Map<String, dynamic> _$SetDiscountToJson(
  _SetDiscount instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'discountAmount': instance.discountAmount,
  'conditionType': _$SetDiscountConditionTypeEnumMap[instance.conditionType]!,
  'productIds': instance.productIds,
  'minimumAmount': instance.minimumAmount,
  'categoryCondition': instance.categoryCondition,
  'productGroupCondition': instance.productGroupCondition,
  'allowMultipleApplications': instance.allowMultipleApplications,
  'isActive': instance.isActive,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'eventId': instance.eventId,
};

const _$SetDiscountConditionTypeEnumMap = {
  SetDiscountConditionType.productCombination: 'productCombination',
  SetDiscountConditionType.minimumAmount: 'minimumAmount',
  SetDiscountConditionType.categoryQuantity: 'categoryQuantity',
  SetDiscountConditionType.productGroupQuantity: 'productGroupQuantity',
};
