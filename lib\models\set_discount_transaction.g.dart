// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'set_discount_transaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SetDiscountTransaction _$SetDiscountTransactionFromJson(
  Map<String, dynamic> json,
) => _SetDiscountTransaction(
  id: (json['id'] as num?)?.toInt(),
  batchSaleId: json['batchSaleId'] as String,
  appliedDiscounts: (json['appliedDiscounts'] as List<dynamic>)
      .map((e) => AppliedSetDiscountData.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalDiscountAmount: (json['totalDiscountAmount'] as num).toInt(),
  appliedCount: (json['appliedCount'] as num).toInt(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  eventId: (json['eventId'] as num).toInt(),
);

Map<String, dynamic> _$SetDiscountTransactionToJson(
  _SetDiscountTransaction instance,
) => <String, dynamic>{
  'id': instance.id,
  'batchSaleId': instance.batchSaleId,
  'appliedDiscounts': instance.appliedDiscounts,
  'totalDiscountAmount': instance.totalDiscountAmount,
  'appliedCount': instance.appliedCount,
  'createdAt': instance.createdAt.toIso8601String(),
  'eventId': instance.eventId,
};

_AppliedSetDiscountData _$AppliedSetDiscountDataFromJson(
  Map<String, dynamic> json,
) => _AppliedSetDiscountData(
  setDiscountName: json['setDiscountName'] as String,
  discountAmount: (json['discountAmount'] as num).toInt(),
  appliedCount: (json['appliedCount'] as num).toInt(),
  conditionType: $enumDecode(
    _$SetDiscountConditionTypeEnumMap,
    json['conditionType'],
  ),
);

Map<String, dynamic> _$AppliedSetDiscountDataToJson(
  _AppliedSetDiscountData instance,
) => <String, dynamic>{
  'setDiscountName': instance.setDiscountName,
  'discountAmount': instance.discountAmount,
  'appliedCount': instance.appliedCount,
  'conditionType': _$SetDiscountConditionTypeEnumMap[instance.conditionType]!,
};

const _$SetDiscountConditionTypeEnumMap = {
  SetDiscountConditionType.productCombination: 'productCombination',
  SetDiscountConditionType.minimumAmount: 'minimumAmount',
  SetDiscountConditionType.categoryQuantity: 'categoryQuantity',
  SetDiscountConditionType.productGroupQuantity: 'productGroupQuantity',
};
