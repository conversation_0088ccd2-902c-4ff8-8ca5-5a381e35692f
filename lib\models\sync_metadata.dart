/// 실시간 동기화를 위한 메타데이터 모델
/// 
/// 모든 동기화 가능한 엔티티에 포함되어 충돌 해결, 버전 관리, 
/// 동기화 상태 추적 등을 담당합니다.
///
/// 작성자: Blue  
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'sync_metadata.freezed.dart';
part 'sync_metadata.g.dart';

/// 동기화 상태를 나타내는 열거형
enum SyncStatus {
  /// 동기화 완료됨
  synced,
  
  /// 동기화 대기 중 (오프라인 등)
  pending,
  
  /// 충돌 발생
  conflict,
  
  /// 로컬에만 존재 (신규 생성)
  localOnly,
  
  /// 삭제 예정
  pendingDelete
}

/// 동기화 메타데이터 모델
@freezed
abstract class SyncMetadata with _$SyncMetadata {
  const factory SyncMetadata({
    /// 마지막 수정 시간 (UTC)
    required DateTime lastModified,
    
    /// 버전 번호 (충돌 해결용)
    @Default(1) int version,
    
    /// 동기화 상태
    @Default(SyncStatus.synced) SyncStatus syncStatus,
    
    /// 마지막 수정한 디바이스 ID
    String? deviceId,
    
    /// 서버 타임스탬프 (서버에서 설정)
    DateTime? serverTimestamp,
    
    /// 체크섬 (데이터 무결성 확인용)
    String? checksum,
    
    /// 충돌 해결 메타데이터
    Map<String, dynamic>? conflictData,
  }) = _SyncMetadata;

  factory SyncMetadata.fromJson(Map<String, dynamic> json) => 
      _$SyncMetadataFromJson(json);

  /// 새로운 엔티티 생성시 기본 메타데이터
  factory SyncMetadata.create({String? deviceId}) {
    return SyncMetadata(
      lastModified: DateTime.now().toUtc(),
      version: 1,
      syncStatus: SyncStatus.localOnly,
      deviceId: deviceId,
    );
  }

  /// 업데이트시 메타데이터 갱신
  factory SyncMetadata.updated(
    SyncMetadata current, {
    String? deviceId,
    SyncStatus? newStatus,
  }) {
    return current.copyWith(
      lastModified: DateTime.now().toUtc(),
      version: current.version + 1,
      syncStatus: newStatus ?? SyncStatus.pending,
      deviceId: deviceId ?? current.deviceId,
    );
  }

  /// 서버에서 받은 데이터로 업데이트
  factory SyncMetadata.fromServer(
    Map<String, dynamic> serverData, {
    String? deviceId,
  }) {
    return SyncMetadata(
      lastModified: DateTime.parse(serverData['lastModified']),
      version: serverData['version'] ?? 1,
      syncStatus: SyncStatus.synced,
      deviceId: deviceId,
      serverTimestamp: serverData['serverTimestamp'] != null
          ? DateTime.parse(serverData['serverTimestamp'])
          : null,
      checksum: serverData['checksum'],
    );
  }
}

/// SQLite 맵 변환 확장
extension SyncMetadataMap on SyncMetadata {
  /// SQLite용 맵으로 변환
  Map<String, dynamic> toMap() {
    return {
      'lastModified': lastModified.toIso8601String(),
      'version': version,
      'syncStatus': syncStatus.name,
      'deviceId': deviceId,
      'serverTimestamp': serverTimestamp?.toIso8601String(),
      'checksum': checksum,
      'conflictData': conflictData != null 
          ? jsonEncode(conflictData!) 
          : null,
    };
  }

  /// SQLite 맵에서 생성
  static SyncMetadata fromMap(Map<String, dynamic> map) {
    return SyncMetadata(
      lastModified: DateTime.parse(map['lastModified']),
      version: map['version'] ?? 1,
      syncStatus: SyncStatus.values.firstWhere(
        (e) => e.name == map['syncStatus'],
        orElse: () => SyncStatus.synced,
      ),
      deviceId: map['deviceId'],
      serverTimestamp: map['serverTimestamp'] != null
          ? DateTime.parse(map['serverTimestamp'])
          : null,
      checksum: map['checksum'],
      conflictData: map['conflictData'] != null
          ? jsonDecode(map['conflictData'])
          : null,
    );
  }
}

/// 충돌 해결 결과
@freezed
abstract class ConflictResolution with _$ConflictResolution {
  const factory ConflictResolution({
    /// 해결 전략
    required ConflictStrategy strategy,
    
    /// 최종 선택된 데이터
    required Map<String, dynamic> resolvedData,
    
    /// 해결 시간
    required DateTime resolvedAt,
    
    /// 해결에 사용된 메타정보
    Map<String, dynamic>? metadata,
  }) = _ConflictResolution;

  factory ConflictResolution.fromJson(Map<String, dynamic> json) =>
      _$ConflictResolutionFromJson(json);
}

/// 충돌 해결 전략
enum ConflictStrategy {
  /// 서버 우선 (기본값)
  serverWins,
  
  /// 클라이언트 우선
  clientWins,
  
  /// 최신 시간 우선
  lastWriteWins,
  
  /// 필드별 병합
  fieldMerge,
  
  /// 사용자 선택
  manualResolve
}
