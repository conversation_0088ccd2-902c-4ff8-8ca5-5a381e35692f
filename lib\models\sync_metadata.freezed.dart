// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sync_metadata.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SyncMetadata {

/// 마지막 수정 시간 (UTC)
 DateTime get lastModified;/// 버전 번호 (충돌 해결용)
 int get version;/// 동기화 상태
 SyncStatus get syncStatus;/// 마지막 수정한 디바이스 ID
 String? get deviceId;/// 서버 타임스탬프 (서버에서 설정)
 DateTime? get serverTimestamp;/// 체크섬 (데이터 무결성 확인용)
 String? get checksum;/// 충돌 해결 메타데이터
 Map<String, dynamic>? get conflictData;
/// Create a copy of SyncMetadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<SyncMetadata> get copyWith => _$SyncMetadataCopyWithImpl<SyncMetadata>(this as SyncMetadata, _$identity);

  /// Serializes this SyncMetadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SyncMetadata&&(identical(other.lastModified, lastModified) || other.lastModified == lastModified)&&(identical(other.version, version) || other.version == version)&&(identical(other.syncStatus, syncStatus) || other.syncStatus == syncStatus)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId)&&(identical(other.serverTimestamp, serverTimestamp) || other.serverTimestamp == serverTimestamp)&&(identical(other.checksum, checksum) || other.checksum == checksum)&&const DeepCollectionEquality().equals(other.conflictData, conflictData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lastModified,version,syncStatus,deviceId,serverTimestamp,checksum,const DeepCollectionEquality().hash(conflictData));

@override
String toString() {
  return 'SyncMetadata(lastModified: $lastModified, version: $version, syncStatus: $syncStatus, deviceId: $deviceId, serverTimestamp: $serverTimestamp, checksum: $checksum, conflictData: $conflictData)';
}


}

/// @nodoc
abstract mixin class $SyncMetadataCopyWith<$Res>  {
  factory $SyncMetadataCopyWith(SyncMetadata value, $Res Function(SyncMetadata) _then) = _$SyncMetadataCopyWithImpl;
@useResult
$Res call({
 DateTime lastModified, int version, SyncStatus syncStatus, String? deviceId, DateTime? serverTimestamp, String? checksum, Map<String, dynamic>? conflictData
});




}
/// @nodoc
class _$SyncMetadataCopyWithImpl<$Res>
    implements $SyncMetadataCopyWith<$Res> {
  _$SyncMetadataCopyWithImpl(this._self, this._then);

  final SyncMetadata _self;
  final $Res Function(SyncMetadata) _then;

/// Create a copy of SyncMetadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lastModified = null,Object? version = null,Object? syncStatus = null,Object? deviceId = freezed,Object? serverTimestamp = freezed,Object? checksum = freezed,Object? conflictData = freezed,}) {
  return _then(_self.copyWith(
lastModified: null == lastModified ? _self.lastModified : lastModified // ignore: cast_nullable_to_non_nullable
as DateTime,version: null == version ? _self.version : version // ignore: cast_nullable_to_non_nullable
as int,syncStatus: null == syncStatus ? _self.syncStatus : syncStatus // ignore: cast_nullable_to_non_nullable
as SyncStatus,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,serverTimestamp: freezed == serverTimestamp ? _self.serverTimestamp : serverTimestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,checksum: freezed == checksum ? _self.checksum : checksum // ignore: cast_nullable_to_non_nullable
as String?,conflictData: freezed == conflictData ? _self.conflictData : conflictData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SyncMetadata].
extension SyncMetadataPatterns on SyncMetadata {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SyncMetadata value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SyncMetadata() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SyncMetadata value)  $default,){
final _that = this;
switch (_that) {
case _SyncMetadata():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SyncMetadata value)?  $default,){
final _that = this;
switch (_that) {
case _SyncMetadata() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime lastModified,  int version,  SyncStatus syncStatus,  String? deviceId,  DateTime? serverTimestamp,  String? checksum,  Map<String, dynamic>? conflictData)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SyncMetadata() when $default != null:
return $default(_that.lastModified,_that.version,_that.syncStatus,_that.deviceId,_that.serverTimestamp,_that.checksum,_that.conflictData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime lastModified,  int version,  SyncStatus syncStatus,  String? deviceId,  DateTime? serverTimestamp,  String? checksum,  Map<String, dynamic>? conflictData)  $default,) {final _that = this;
switch (_that) {
case _SyncMetadata():
return $default(_that.lastModified,_that.version,_that.syncStatus,_that.deviceId,_that.serverTimestamp,_that.checksum,_that.conflictData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime lastModified,  int version,  SyncStatus syncStatus,  String? deviceId,  DateTime? serverTimestamp,  String? checksum,  Map<String, dynamic>? conflictData)?  $default,) {final _that = this;
switch (_that) {
case _SyncMetadata() when $default != null:
return $default(_that.lastModified,_that.version,_that.syncStatus,_that.deviceId,_that.serverTimestamp,_that.checksum,_that.conflictData);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SyncMetadata implements SyncMetadata {
  const _SyncMetadata({required this.lastModified, this.version = 1, this.syncStatus = SyncStatus.synced, this.deviceId, this.serverTimestamp, this.checksum, final  Map<String, dynamic>? conflictData}): _conflictData = conflictData;
  factory _SyncMetadata.fromJson(Map<String, dynamic> json) => _$SyncMetadataFromJson(json);

/// 마지막 수정 시간 (UTC)
@override final  DateTime lastModified;
/// 버전 번호 (충돌 해결용)
@override@JsonKey() final  int version;
/// 동기화 상태
@override@JsonKey() final  SyncStatus syncStatus;
/// 마지막 수정한 디바이스 ID
@override final  String? deviceId;
/// 서버 타임스탬프 (서버에서 설정)
@override final  DateTime? serverTimestamp;
/// 체크섬 (데이터 무결성 확인용)
@override final  String? checksum;
/// 충돌 해결 메타데이터
 final  Map<String, dynamic>? _conflictData;
/// 충돌 해결 메타데이터
@override Map<String, dynamic>? get conflictData {
  final value = _conflictData;
  if (value == null) return null;
  if (_conflictData is EqualUnmodifiableMapView) return _conflictData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SyncMetadata
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SyncMetadataCopyWith<_SyncMetadata> get copyWith => __$SyncMetadataCopyWithImpl<_SyncMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SyncMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SyncMetadata&&(identical(other.lastModified, lastModified) || other.lastModified == lastModified)&&(identical(other.version, version) || other.version == version)&&(identical(other.syncStatus, syncStatus) || other.syncStatus == syncStatus)&&(identical(other.deviceId, deviceId) || other.deviceId == deviceId)&&(identical(other.serverTimestamp, serverTimestamp) || other.serverTimestamp == serverTimestamp)&&(identical(other.checksum, checksum) || other.checksum == checksum)&&const DeepCollectionEquality().equals(other._conflictData, _conflictData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lastModified,version,syncStatus,deviceId,serverTimestamp,checksum,const DeepCollectionEquality().hash(_conflictData));

@override
String toString() {
  return 'SyncMetadata(lastModified: $lastModified, version: $version, syncStatus: $syncStatus, deviceId: $deviceId, serverTimestamp: $serverTimestamp, checksum: $checksum, conflictData: $conflictData)';
}


}

/// @nodoc
abstract mixin class _$SyncMetadataCopyWith<$Res> implements $SyncMetadataCopyWith<$Res> {
  factory _$SyncMetadataCopyWith(_SyncMetadata value, $Res Function(_SyncMetadata) _then) = __$SyncMetadataCopyWithImpl;
@override @useResult
$Res call({
 DateTime lastModified, int version, SyncStatus syncStatus, String? deviceId, DateTime? serverTimestamp, String? checksum, Map<String, dynamic>? conflictData
});




}
/// @nodoc
class __$SyncMetadataCopyWithImpl<$Res>
    implements _$SyncMetadataCopyWith<$Res> {
  __$SyncMetadataCopyWithImpl(this._self, this._then);

  final _SyncMetadata _self;
  final $Res Function(_SyncMetadata) _then;

/// Create a copy of SyncMetadata
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lastModified = null,Object? version = null,Object? syncStatus = null,Object? deviceId = freezed,Object? serverTimestamp = freezed,Object? checksum = freezed,Object? conflictData = freezed,}) {
  return _then(_SyncMetadata(
lastModified: null == lastModified ? _self.lastModified : lastModified // ignore: cast_nullable_to_non_nullable
as DateTime,version: null == version ? _self.version : version // ignore: cast_nullable_to_non_nullable
as int,syncStatus: null == syncStatus ? _self.syncStatus : syncStatus // ignore: cast_nullable_to_non_nullable
as SyncStatus,deviceId: freezed == deviceId ? _self.deviceId : deviceId // ignore: cast_nullable_to_non_nullable
as String?,serverTimestamp: freezed == serverTimestamp ? _self.serverTimestamp : serverTimestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,checksum: freezed == checksum ? _self.checksum : checksum // ignore: cast_nullable_to_non_nullable
as String?,conflictData: freezed == conflictData ? _self._conflictData : conflictData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$ConflictResolution {

/// 해결 전략
 ConflictStrategy get strategy;/// 최종 선택된 데이터
 Map<String, dynamic> get resolvedData;/// 해결 시간
 DateTime get resolvedAt;/// 해결에 사용된 메타정보
 Map<String, dynamic>? get metadata;
/// Create a copy of ConflictResolution
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConflictResolutionCopyWith<ConflictResolution> get copyWith => _$ConflictResolutionCopyWithImpl<ConflictResolution>(this as ConflictResolution, _$identity);

  /// Serializes this ConflictResolution to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConflictResolution&&(identical(other.strategy, strategy) || other.strategy == strategy)&&const DeepCollectionEquality().equals(other.resolvedData, resolvedData)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,strategy,const DeepCollectionEquality().hash(resolvedData),resolvedAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'ConflictResolution(strategy: $strategy, resolvedData: $resolvedData, resolvedAt: $resolvedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $ConflictResolutionCopyWith<$Res>  {
  factory $ConflictResolutionCopyWith(ConflictResolution value, $Res Function(ConflictResolution) _then) = _$ConflictResolutionCopyWithImpl;
@useResult
$Res call({
 ConflictStrategy strategy, Map<String, dynamic> resolvedData, DateTime resolvedAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$ConflictResolutionCopyWithImpl<$Res>
    implements $ConflictResolutionCopyWith<$Res> {
  _$ConflictResolutionCopyWithImpl(this._self, this._then);

  final ConflictResolution _self;
  final $Res Function(ConflictResolution) _then;

/// Create a copy of ConflictResolution
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? strategy = null,Object? resolvedData = null,Object? resolvedAt = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
strategy: null == strategy ? _self.strategy : strategy // ignore: cast_nullable_to_non_nullable
as ConflictStrategy,resolvedData: null == resolvedData ? _self.resolvedData : resolvedData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,resolvedAt: null == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ConflictResolution].
extension ConflictResolutionPatterns on ConflictResolution {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConflictResolution value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConflictResolution() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConflictResolution value)  $default,){
final _that = this;
switch (_that) {
case _ConflictResolution():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConflictResolution value)?  $default,){
final _that = this;
switch (_that) {
case _ConflictResolution() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ConflictStrategy strategy,  Map<String, dynamic> resolvedData,  DateTime resolvedAt,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConflictResolution() when $default != null:
return $default(_that.strategy,_that.resolvedData,_that.resolvedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ConflictStrategy strategy,  Map<String, dynamic> resolvedData,  DateTime resolvedAt,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _ConflictResolution():
return $default(_that.strategy,_that.resolvedData,_that.resolvedAt,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ConflictStrategy strategy,  Map<String, dynamic> resolvedData,  DateTime resolvedAt,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _ConflictResolution() when $default != null:
return $default(_that.strategy,_that.resolvedData,_that.resolvedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ConflictResolution implements ConflictResolution {
  const _ConflictResolution({required this.strategy, required final  Map<String, dynamic> resolvedData, required this.resolvedAt, final  Map<String, dynamic>? metadata}): _resolvedData = resolvedData,_metadata = metadata;
  factory _ConflictResolution.fromJson(Map<String, dynamic> json) => _$ConflictResolutionFromJson(json);

/// 해결 전략
@override final  ConflictStrategy strategy;
/// 최종 선택된 데이터
 final  Map<String, dynamic> _resolvedData;
/// 최종 선택된 데이터
@override Map<String, dynamic> get resolvedData {
  if (_resolvedData is EqualUnmodifiableMapView) return _resolvedData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_resolvedData);
}

/// 해결 시간
@override final  DateTime resolvedAt;
/// 해결에 사용된 메타정보
 final  Map<String, dynamic>? _metadata;
/// 해결에 사용된 메타정보
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of ConflictResolution
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConflictResolutionCopyWith<_ConflictResolution> get copyWith => __$ConflictResolutionCopyWithImpl<_ConflictResolution>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConflictResolutionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConflictResolution&&(identical(other.strategy, strategy) || other.strategy == strategy)&&const DeepCollectionEquality().equals(other._resolvedData, _resolvedData)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,strategy,const DeepCollectionEquality().hash(_resolvedData),resolvedAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'ConflictResolution(strategy: $strategy, resolvedData: $resolvedData, resolvedAt: $resolvedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$ConflictResolutionCopyWith<$Res> implements $ConflictResolutionCopyWith<$Res> {
  factory _$ConflictResolutionCopyWith(_ConflictResolution value, $Res Function(_ConflictResolution) _then) = __$ConflictResolutionCopyWithImpl;
@override @useResult
$Res call({
 ConflictStrategy strategy, Map<String, dynamic> resolvedData, DateTime resolvedAt, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$ConflictResolutionCopyWithImpl<$Res>
    implements _$ConflictResolutionCopyWith<$Res> {
  __$ConflictResolutionCopyWithImpl(this._self, this._then);

  final _ConflictResolution _self;
  final $Res Function(_ConflictResolution) _then;

/// Create a copy of ConflictResolution
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? strategy = null,Object? resolvedData = null,Object? resolvedAt = null,Object? metadata = freezed,}) {
  return _then(_ConflictResolution(
strategy: null == strategy ? _self.strategy : strategy // ignore: cast_nullable_to_non_nullable
as ConflictStrategy,resolvedData: null == resolvedData ? _self._resolvedData : resolvedData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,resolvedAt: null == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
