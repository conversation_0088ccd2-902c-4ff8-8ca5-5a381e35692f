// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_metadata.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SyncMetadata _$SyncMetadataFromJson(Map<String, dynamic> json) =>
    _SyncMetadata(
      lastModified: DateTime.parse(json['lastModified'] as String),
      version: (json['version'] as num?)?.toInt() ?? 1,
      syncStatus:
          $enumDecodeNullable(_$SyncStatusEnumMap, json['syncStatus']) ??
          SyncStatus.synced,
      deviceId: json['deviceId'] as String?,
      serverTimestamp: json['serverTimestamp'] == null
          ? null
          : DateTime.parse(json['serverTimestamp'] as String),
      checksum: json['checksum'] as String?,
      conflictData: json['conflictData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SyncMetadataToJson(_SyncMetadata instance) =>
    <String, dynamic>{
      'lastModified': instance.lastModified.toIso8601String(),
      'version': instance.version,
      'syncStatus': _$SyncStatusEnumMap[instance.syncStatus]!,
      'deviceId': instance.deviceId,
      'serverTimestamp': instance.serverTimestamp?.toIso8601String(),
      'checksum': instance.checksum,
      'conflictData': instance.conflictData,
    };

const _$SyncStatusEnumMap = {
  SyncStatus.synced: 'synced',
  SyncStatus.pending: 'pending',
  SyncStatus.conflict: 'conflict',
  SyncStatus.localOnly: 'localOnly',
  SyncStatus.pendingDelete: 'pendingDelete',
};

_ConflictResolution _$ConflictResolutionFromJson(Map<String, dynamic> json) =>
    _ConflictResolution(
      strategy: $enumDecode(_$ConflictStrategyEnumMap, json['strategy']),
      resolvedData: json['resolvedData'] as Map<String, dynamic>,
      resolvedAt: DateTime.parse(json['resolvedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ConflictResolutionToJson(_ConflictResolution instance) =>
    <String, dynamic>{
      'strategy': _$ConflictStrategyEnumMap[instance.strategy]!,
      'resolvedData': instance.resolvedData,
      'resolvedAt': instance.resolvedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$ConflictStrategyEnumMap = {
  ConflictStrategy.serverWins: 'serverWins',
  ConflictStrategy.clientWins: 'clientWins',
  ConflictStrategy.lastWriteWins: 'lastWriteWins',
  ConflictStrategy.fieldMerge: 'fieldMerge',
  ConflictStrategy.manualResolve: 'manualResolve',
};
