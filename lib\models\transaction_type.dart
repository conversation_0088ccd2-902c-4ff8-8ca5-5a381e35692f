/// 거래 유형을 나타내는 열거형
enum TransactionType {
  sale('sale', '판매'),
  service('service', '서비스'),
  discount('discount', '할인'),
  setDiscount('setDiscount', '세트 할인');

  const TransactionType(this.value, this.displayName);

  final String value;
  final String displayName;

  /// 문자열 값으로부터 TransactionType을 생성
  static TransactionType? fromString(String? value) {
    if (value == null) return null;

    for (TransactionType type in TransactionType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }

  /// 문자열 값으로부터 TransactionType을 생성 (기본값 포함)
  static TransactionType fromStringWithDefault(
    String? value, [
    TransactionType defaultType = TransactionType.sale,
  ]) {
    return fromString(value) ?? defaultType;
  }

  @override
  String toString() => value;
}
