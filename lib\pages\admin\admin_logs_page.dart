import 'package:flutter/material.dart';
import '../../models/admin_models.dart';
import '../../services/admin_service.dart';

/// 관리자 로그 페이지
class AdminLogsPage extends StatefulWidget {
  const AdminLogsPage({super.key});

  @override
  State<AdminLogsPage> createState() => _AdminLogsPageState();
}

class _AdminLogsPageState extends State<AdminLogsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  List<SystemLog> _adminLogs = [];
  List<SystemLog> _autoPaymentLogs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadLogs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadLogs() async {
    setState(() => _isLoading = true);

    try {
      final futures = await Future.wait([
        AdminService.getAdminLogs(),
        AdminService.getAutoPaymentLogs(),
      ]);

      setState(() {
        _adminLogs = futures[0];
        _autoPaymentLogs = futures[1];
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('로그 로드 중 오류가 발생했습니다: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          '시스템 로그',
          style: TextStyle(
            color: Color(0xFF495057),
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        iconTheme: const IconThemeData(color: Color(0xFF495057)),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLogs,
            tooltip: '새로고침',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF495057),
          unselectedLabelColor: const Color(0xFF6C757D),
          indicatorColor: const Color(0xFF495057),
          tabs: const [
            Tab(text: '관리자 로그'),
            Tab(text: '시스템 로그'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF495057),
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildLogList(_adminLogs, '관리자 로그가 없습니다.'),
                _buildLogList(_autoPaymentLogs, '시스템 로그가 없습니다.'),
              ],
            ),
    );
  }

  Widget _buildLogList(List<SystemLog> logs, String emptyMessage) {
    if (logs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: logs.length,
      itemBuilder: (context, index) {
        final log = logs[index];
        return _buildLogItem(log);
      },
    );
  }

  Widget _buildLogItem(SystemLog log) {
    Color levelColor;
    IconData levelIcon;
    
    switch (log.level) {
      case 'SUCCESS':
        levelColor = Colors.green;
        levelIcon = Icons.check_circle;
        break;
      case 'ERROR':
        levelColor = Colors.red;
        levelIcon = Icons.error;
        break;
      case 'WARNING':
        levelColor = Colors.orange;
        levelIcon = Icons.warning;
        break;
      default:
        levelColor = const Color(0xFF6C757D);
        levelIcon = Icons.info;
    }

    // 한국 시간으로 변환하여 날짜와 시간 모두 표시
    final koreaTime = log.timestamp.toLocal();
    final dateTimeString = 
        '${koreaTime.year}-${koreaTime.month.toString().padLeft(2, '0')}-${koreaTime.day.toString().padLeft(2, '0')} '
        '${koreaTime.hour.toString().padLeft(2, '0')}:${koreaTime.minute.toString().padLeft(2, '0')}:${koreaTime.second.toString().padLeft(2, '0')}';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(color: levelColor, width: 4),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  levelIcon,
                  color: levelColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    log.message,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF495057),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: levelColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    log.level,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: levelColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              log.details ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF6C757D),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              dateTimeString,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF6C757D),
                fontFamily: 'monospace',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
