import 'package:flutter/material.dart';
import '../../../models/admin_models.dart';

/// 시스템 모니터링 위젯
class AdminSystemMonitor extends StatelessWidget {
  final List<SystemLog> logs;
  final SystemStats? stats;
  final AdminDashboardStats? dashboardStats;
  final VoidCallback onRefresh;

  const AdminSystemMonitor({
    super.key,
    required this.logs,
    required this.stats,
    this.dashboardStats,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: const BorderSide(color: Color(0xFFE9ECEF)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                const Icon(
                  Icons.monitor,
                  color: Color(0xFF495057),
                ),
                const SizedBox(width: 8),
                const Text(
                  '시스템 모니터링',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF495057),
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: onRefresh,
                  icon: const Icon(
                    Icons.refresh,
                    color: Color(0xFF495057),
                  ),
                  tooltip: '새로고침',
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 시스템 통계
            if (dashboardStats != null) ...[
              const Text(
                '시스템 통계',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF495057),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem('총 함수 호출', '${dashboardStats!.serverStats.totalFunctionCalls}'),
                  ),
                  Expanded(
                    child: _buildStatItem('평균 응답시간', '${dashboardStats!.serverStats.averageResponseTime}ms'),
                  ),
                  Expanded(
                    child: _buildStatItem('오류율', dashboardStats!.serverStats.errorRate),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem('일일 활성 사용자', '${dashboardStats!.userActivityStats.dailyActiveUsers}명'),
                  ),
                  Expanded(
                    child: _buildStatItem('주간 활성 사용자', '${dashboardStats!.userActivityStats.weeklyActiveUsers}명'),
                  ),
                  Expanded(
                    child: _buildStatItem('월간 활성 사용자', '${dashboardStats!.userActivityStats.monthlyActiveUsers}명'),
                  ),
                ],
              ),
              const SizedBox(height: 32),
            ] else if (stats != null) ...[
              const Text(
                '시스템 통계',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF495057),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem('총 함수 호출', stats!.totalFunctionCalls),
                  ),
                  Expanded(
                    child: _buildStatItem('성공률', stats!.successRate),
                  ),
                  Expanded(
                    child: _buildStatItem('마지막 실행', stats!.lastExecution),
                  ),
                ],
              ),
              const SizedBox(height: 32),
            ],

            // 시스템 상태 요약
            const Text(
              '시스템 상태',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF495057),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: const Color(0xFFE9ECEF)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '시스템 정상 작동 중',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF495057),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '자세한 로그는 우측 상단의 로그 버튼을 클릭하세요.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Color(0xFF495057),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6C757D),
            ),
          ),
        ],
      ),
    );
  }


}
