import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/admin_models.dart';
import '../../../services/admin_service.dart';

/// 구독 관리 다이얼로그
class SubscriptionManagementDialog extends StatefulWidget {
  final AdminUser user;
  final VoidCallback onSubscriptionChanged;

  const SubscriptionManagementDialog({
    super.key,
    required this.user,
    required this.onSubscriptionChanged,
  });

  @override
  State<SubscriptionManagementDialog> createState() => _SubscriptionManagementDialogState();
}

class _SubscriptionManagementDialogState extends State<SubscriptionManagementDialog> {
  bool _isLoading = false;
  final _daysController = TextEditingController();
  DateTime? _selectedEndDate;

  @override
  void dispose() {
    _daysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final subscription = widget.user.subscription;
    final isActive = subscription?.isActive ?? false;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                const Icon(Icons.manage_accounts, color: Color(0xFF495057)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '구독 관리',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF495057),
                        ),
                      ),
                      Text(
                        widget.user.email ?? 'N/A',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6C757D),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 현재 구독 상태
            _buildCurrentStatus(subscription),
            const SizedBox(height: 24),

            // 구독 관리 옵션들
            if (isActive) ...[
              _buildActiveSubscriptionOptions(),
            ] else ...[
              _buildInactiveSubscriptionOptions(),
            ],

            const SizedBox(height: 24),

            // 버튼들
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('취소'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentStatus(AdminSubscription? subscription) {
    final isActive = subscription?.isActive ?? false;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '현재 구독 상태',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF495057),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isActive ? const Color(0xFF10B981) : const Color(0xFF6C757D),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  isActive ? '프로 플랜' : '무료 플랜',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (isActive && subscription?.nextPaymentDate != null) ...[
                const SizedBox(width: 12),
                Text(
                  '다음 결제일: ${_formatDate(subscription!.nextPaymentDate!)}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6C757D),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActiveSubscriptionOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '구독 관리 옵션',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF495057),
          ),
        ),
        const SizedBox(height: 16),
        
        // 일수 추가
        _buildOptionCard(
          icon: Icons.add_circle_outline,
          title: '구독 기간 연장',
          description: '현재 구독에 추가 일수를 더합니다',
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: TextField(
                  controller: _daysController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  decoration: const InputDecoration(
                    hintText: '일수',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Text('일'),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _isLoading ? null : _addDays,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF10B981),
                  foregroundColor: Colors.white,
                ),
                child: const Text('추가'),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 구독 취소
        _buildOptionCard(
          icon: Icons.cancel_outlined,
          title: '구독 취소',
          description: '다음 결제일까지 구독을 유지하고 이후 자동 취소됩니다',
          child: ElevatedButton(
            onPressed: _isLoading ? null : _cancelSubscription,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFDC3545),
              foregroundColor: Colors.white,
            ),
            child: const Text('구독 취소'),
          ),
        ),
        const SizedBox(height: 16),

        // 강제 무료 전환
        _buildOptionCard(
          icon: Icons.block,
          title: '강제 무료 전환',
          description: '즉시 무료 플랜으로 강제 전환합니다 (주의: 되돌릴 수 없음)',
          child: ElevatedButton(
            onPressed: _isLoading ? null : _forceFreePlan,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C757D),
              foregroundColor: Colors.white,
            ),
            child: const Text('강제 무료 전환'),
          ),
        ),
      ],
    );
  }

  Widget _buildInactiveSubscriptionOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '구독 활성화',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF495057),
          ),
        ),
        const SizedBox(height: 16),

        // 날짜 선택으로 구독 활성화
        _buildOptionCard(
          icon: Icons.event,
          title: '구독 시작 (날짜 지정)',
          description: '지정한 날짜까지 구독을 활성화합니다',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFE9ECEF)),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _selectedEndDate != null 
                          ? _formatDate(_selectedEndDate!)
                          : '종료 날짜를 선택하세요',
                        style: TextStyle(
                          color: _selectedEndDate != null 
                            ? const Color(0xFF495057)
                            : const Color(0xFF6C757D),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _selectEndDate,
                    child: const Text('날짜 선택'),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _selectedEndDate != null && !_isLoading 
                    ? _activateSubscriptionWithDate 
                    : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('구독 활성화'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 일수로 구독 활성화
        _buildOptionCard(
          icon: Icons.timer,
          title: '구독 시작 (일수 지정)',
          description: '지정한 일수만큼 구독을 활성화합니다',
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: TextField(
                  controller: _daysController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  decoration: const InputDecoration(
                    hintText: '일수',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Text('일'),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _isLoading ? null : _activateSubscriptionWithDays,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366F1),
                  foregroundColor: Colors.white,
                ),
                child: const Text('구독 활성화'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOptionCard({
    required IconData icon,
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE9ECEF)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF495057)),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF495057),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6C757D),
            ),
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() {
        _selectedEndDate = date;
      });
    }
  }

  Future<void> _addDays() async {
    final days = int.tryParse(_daysController.text);
    if (days == null || days <= 0) {
      _showError('올바른 일수를 입력하세요.');
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      final success = await AdminService.addSubscriptionDays(widget.user.uid, days);
      if (success) {
        _showSuccess('구독 기간이 ${days}일 연장되었습니다.');
        widget.onSubscriptionChanged();
        Navigator.of(context).pop();
      } else {
        _showError('구독 기간 연장에 실패했습니다.');
      }
    } catch (e) {
      _showError('오류가 발생했습니다: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _cancelSubscription() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('구독 취소'),
        content: const Text('정말로 구독을 취소하시겠습니까?\n다음 결제일까지는 구독이 유지됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('확인'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);
    
    try {
      final success = await AdminService.cancelUserSubscription(widget.user.uid);
      if (success) {
        _showSuccess('구독이 취소되었습니다.');
        widget.onSubscriptionChanged();
        Navigator.of(context).pop();
      } else {
        _showError('구독 취소에 실패했습니다.');
      }
    } catch (e) {
      _showError('오류가 발생했습니다: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _activateSubscriptionWithDate() async {
    if (_selectedEndDate == null) return;

    setState(() => _isLoading = true);
    
    try {
      final success = await AdminService.activateSubscriptionUntilDate(
        widget.user.uid, 
        _selectedEndDate!,
      );
      if (success) {
        _showSuccess('구독이 활성화되었습니다.');
        widget.onSubscriptionChanged();
        Navigator.of(context).pop();
      } else {
        _showError('구독 활성화에 실패했습니다.');
      }
    } catch (e) {
      _showError('오류가 발생했습니다: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _activateSubscriptionWithDays() async {
    final days = int.tryParse(_daysController.text);
    if (days == null || days <= 0) {
      _showError('올바른 일수를 입력하세요.');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await AdminService.activateSubscriptionForDays(widget.user.uid, days);
      if (success) {
        _showSuccess('구독이 ${days}일간 활성화되었습니다.');
        widget.onSubscriptionChanged();
        Navigator.of(context).pop();
      } else {
        _showError('구독 활성화에 실패했습니다.');
      }
    } catch (e) {
      _showError('오류가 발생했습니다: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _forceFreePlan() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('강제 무료 전환'),
        content: const Text('정말로 이 사용자를 강제로 무료 플랜으로 전환하시겠습니까?\n\n주의: 이 작업은 되돌릴 수 없으며, 즉시 적용됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('강제 전환'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      final success = await AdminService.forceFreePlan(widget.user.uid);
      if (success) {
        _showSuccess('사용자가 강제로 무료 플랜으로 전환되었습니다.');
        widget.onSubscriptionChanged();
        Navigator.of(context).pop();
      } else {
        _showError('강제 무료 전환에 실패했습니다.');
      }
    } catch (e) {
      _showError('오류가 발생했습니다: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
