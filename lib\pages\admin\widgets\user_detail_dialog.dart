import 'package:flutter/material.dart';
import '../../../models/admin_models.dart';

class UserDetailDialog extends StatelessWidget {
  final AdminUser user;

  const UserDetailDialog({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 헤더
            Row(
              children: [
                const Icon(Icons.person, color: Color(0xFF495057)),
                const SizedBox(width: 8),
                const Text(
                  '사용자 상세 정보',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF495057),
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // 기본 정보
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection('기본 정보', [
                      _buildInfoRow('UID', user.uid),
                      _buildInfoRow('이메일', user.email ?? 'N/A'),
                      _buildInfoRow('닉네임', user.nickname ?? 'N/A'),
                      _buildInfoRow('전화번호', user.phone ?? 'N/A'),
                      _buildInfoRow('가입일', user.createdAt?.toString().split(' ')[0] ?? 'N/A'),
                    ]),
                    
                    const SizedBox(height: 24),
                    
                    // 구독 정보
                    _buildSection('구독 정보', [
                      _buildInfoRow('구독 상태', user.subscription?.isActive == true ? '프로 플랜' : '무료 플랜'),
                      if (user.subscription?.isActive == true) ...[
                        _buildInfoRow('구독 시작일', user.subscription?.startedAt?.toString().split(' ')[0] ?? 'N/A'),
                        _buildInfoRow('다음 결제일', user.subscription?.nextPaymentDate?.toString().split(' ')[0] ?? 'N/A'),
                        _buildInfoRow('결제 금액', '${user.subscription?.price ?? 0}원'),
                      ],
                    ]),
                    
                    const SizedBox(height: 24),
                    

                    
                    const SizedBox(height: 24),
                    
                    // 결제 이력 (간단 표시)
                    _buildSection('최근 결제 이력', [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F9FA),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xFFE9ECEF)),
                        ),
                        child: const Text(
                          '결제 이력 데이터를 불러오는 중...',
                          style: TextStyle(
                            color: Color(0xFF6C757D),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ]),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 액션 버튼들
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('닫기'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    // 사용자 관리 액션 (예: 구독 변경, 계정 정지 등)
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('관리'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: Color(0xFF495057),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE9ECEF)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF6C757D),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Color(0xFF495057),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
