// 바라 부스 매니저 - Provider 기본 구조 (분리형)
// 실제 구현은 아래 파일들에서 관리

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/provider_exception.dart';
import '../utils/cancellation_token.dart';
import 'base_state.dart';
export 'retry_policy.dart';
export 'base_async_notifier.dart';

/// 모든 Provider의 기본 클래스입니다.
/// - 상태 관리, 에러 처리, 로딩 상태 등 공통 기능 제공
abstract class BaseProvider<T extends BaseState> extends StateNotifier<AsyncValue<T>> {
  final Ref ref;

  BaseProvider(this.ref) : super(const AsyncValue.loading());

  void setLoading(bool isLoading) {
    if (state.value == null) return;
    state = AsyncValue.data(state.value!.copyWithBase(isLoading: isLoading) as T);
  }

  void updateError(ProviderException error, StackTrace? stackTrace) {
    state = AsyncValue.error(error, stackTrace ?? StackTrace.current);
  }

  void updateState(T Function(T currentState) update) {
    if (state.value == null) return;
    state = AsyncValue.data(update(state.value!));
  }

  Future<R> runCancellableOperation<R>({
    required String operationName,
    required Future<R> Function(CancellationToken) operation,
  }) async {
    final token = CancellationToken();
    try {
      return await operation(token);
    } catch (e) {
      if (e is CancelledException) {
        throw ProviderException.general(
          '작업이 취소되었습니다',
          code: 'OPERATION_CANCELLED',
        );
      }
      rethrow;
    }
  }
}
