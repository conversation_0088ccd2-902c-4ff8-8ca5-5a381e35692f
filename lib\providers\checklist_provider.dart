import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/checklist_template.dart';
import '../models/checklist_item.dart';
import '../repositories/checklist_repository.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';
import 'unified_workspace_provider.dart';

/// 체크리스트 상태 모델
class ChecklistState {
  final List<ChecklistTemplate> templates;
  final List<ChecklistItem> items;
  final bool isLoading;
  final String? error;

  const ChecklistState({
    this.templates = const [],
    this.items = const [],
    this.isLoading = false,
    this.error,
  });

  ChecklistState copyWith({
    List<ChecklistTemplate>? templates,
    List<ChecklistItem>? items,
    bool? isLoading,
    String? error,
  }) {
    return ChecklistState(
      templates: templates ?? this.templates,
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  bool getCheckState(int templateId, int eventId) {
    return items.any((item) =>
        item.templateId == templateId &&
        item.eventId == eventId &&
        item.isChecked);
  }
}

/// 체크리스트 Provider (로컬 전용 모드)
class ChecklistNotifier extends StateNotifier<ChecklistState> {
  final ChecklistRepository repository;
  final Ref ref;
  static const String _tag = 'ChecklistNotifier';

  // 중복 로드/무한 빌드 방지 플래그
  bool _isFetching = false;
  bool _hasLoadedOnce = false;

  ChecklistNotifier(this.repository, this.ref) : super(const ChecklistState()) {
    LoggerUtils.logInfo('로컬 전용 모드: 체크리스트 Provider 초기화', tag: _tag);
  }

  @override
  void dispose() {
    // 로컬 전용 모드: 실시간 동기화 구독 제거됨
    super.dispose();
  }

  /// 체크리스트 데이터 로드 (현재 행사별)
  Future<void> loadData({bool showLoading = true}) async {
    if (_isFetching) {
      LoggerUtils.logDebug('체크리스트 데이터 로드 중복 호출 무시', tag: _tag);
      return;
    }

    if (_hasLoadedOnce && !showLoading) {
      // 홈 대시보드 등에서의 반복 트리거 최소화
      LoggerUtils.logDebug('이미 한 번 로드됨 - 백그라운드 중복 호출 무시', tag: _tag);
      return;
    }

    _isFetching = true;
    if (showLoading) {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      LoggerUtils.logInfo('체크리스트 데이터 로드 시작 (현재 행사별)', tag: _tag);

      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 체크리스트 로드를 건너뜁니다', tag: _tag);
        state = state.copyWith(
          isLoading: false,
          templates: [],
          items: [],
        );
        return;
      }

      // 현재 행사의 템플릿과 아이템 로드
      final templates = await repository.getTemplatesByEventId(currentWorkspace.id);
      final items = await repository.getAllChecklistItems();

      state = state.copyWith(
        templates: templates,
        items: items,
        isLoading: false,
        error: null,
      );

      _hasLoadedOnce = true;
      LoggerUtils.logInfo('체크리스트 데이터 로드 완료 (eventId: ${currentWorkspace.id}) - 템플릿: ${templates.length}개, 아이템: ${items.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 데이터 로드 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    } finally {
      _isFetching = false;
    }
  }

  /// 체크리스트 템플릿 추가 (로컬 전용)
  Future<void> addTemplate(ChecklistTemplate template) async {
    try {

      LoggerUtils.logInfo('체크리스트 템플릿 등록 시작: ${template.title}', tag: _tag);

      await repository.insertTemplate(template);
      await loadData(showLoading: false);

      LoggerUtils.logInfo('체크리스트 템플릿 추가 완료: ${template.title}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 추가 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 템플릿 수정 (로컬 전용)
  Future<void> updateTemplate(ChecklistTemplate template) async {
    try {
      LoggerUtils.logInfo('체크리스트 템플릿 수정 시작: ${template.title}', tag: _tag);

      await repository.updateTemplate(template);
      await loadData(showLoading: false);

      LoggerUtils.logInfo('체크리스트 템플릿 수정 완료: ${template.title}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 수정 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 템플릿 삭제 (로컬 전용)
  Future<void> deleteTemplate(int templateId) async {
    try {
      LoggerUtils.logInfo('체크리스트 템플릿 삭제 시작: templateId=$templateId', tag: _tag);

      await repository.deleteTemplate(templateId);
      await repository.deleteItemsByTemplateId(templateId);
      await loadData(showLoading: false);

      LoggerUtils.logInfo('체크리스트 템플릿 삭제 완료: templateId=$templateId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 삭제 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 아이템 체크 토글 (즉시 저장)
  Future<void> toggleItemCheckLocal(int templateId) async {
    try {
      LoggerUtils.logInfo('체크리스트 아이템 체크 토글: templateId=$templateId', tag: _tag);

      // 현재 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 체크리스트 토글을 건너뜁니다', tag: _tag);
        return;
      }

      // 현재 상태 확인
      final currentState = state.items.any((item) =>
          item.templateId == templateId &&
          item.eventId == currentWorkspace.id &&
          item.isChecked);
      final newState = !currentState;

      // 즉시 로컬 DB에 저장
      await repository.setItemCheckState(templateId, currentWorkspace.id, newState);

      // 상태 새로고침
      await loadData(showLoading: false);

      LoggerUtils.logInfo('체크리스트 아이템 즉시 저장 완료: templateId=$templateId, newState=$newState', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 아이템 토글 실패', tag: _tag, error: e);
    }
  }

  /// 다른 행사의 체크리스트 템플릿을 현재 행사로 복사합니다.
  Future<void> copyTemplatesFromEvent(int sourceEventId) async {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 체크리스트 복사를 건너뜁니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('체크리스트 템플릿 복사 시작: sourceEventId=$sourceEventId, targetEventId=${currentWorkspace.id}', tag: _tag);

      await repository.copyTemplatesFromEvent(sourceEventId, currentWorkspace.id);
      await loadData(showLoading: false);

      LoggerUtils.logInfo('체크리스트 템플릿 복사 완료: sourceEventId=$sourceEventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 복사 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }


}

// Provider 정의
final checklistRepositoryProvider = Provider<ChecklistRepository>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  return ChecklistRepository(databaseService);
});

final checklistNotifierProvider = StateNotifierProvider<ChecklistNotifier, ChecklistState>((ref) {
  final repository = ref.read(checklistRepositoryProvider);
  return ChecklistNotifier(repository, ref);
});
