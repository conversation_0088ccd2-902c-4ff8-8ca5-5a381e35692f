import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';

/// 로그아웃 상태를 관리하는 Provider
/// 
/// 로그아웃 과정에서 AppWrapper가 NicknameScreen으로 이동하는 것을 방지하기 위해 사용
class LogoutStateNotifier extends StateNotifier<bool> {
  static const String _tag = 'LogoutStateProvider';
  
  LogoutStateNotifier() : super(false);

  /// 로그아웃 시작
  void startLogout() {
    LoggerUtils.logInfo('로그아웃 상태 시작', tag: _tag);
    state = true;
  }

  /// 로그아웃 완료 (Phoenix.rebirth() 후 자동으로 초기화됨)
  void completeLogout() {
    LoggerUtils.logInfo('로그아웃 상태 완료', tag: _tag);
    state = false;
  }

  /// 로그아웃 종료 (무한 로딩 방지)
  void endLogout() {
    LoggerUtils.logInfo('로그아웃 상태 종료', tag: _tag);
    state = false;
  }
}

/// 로그아웃 상태 Provider
final logoutStateProvider = StateNotifierProvider<LogoutStateNotifier, bool>((ref) {
  return LogoutStateNotifier();
});
