import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/nickname.dart';
import '../services/database_service.dart';

import '../utils/logger_utils.dart';
import '../utils/network_status.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';



import 'dart:io';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class NicknameNotifier extends StateNotifier<Nickname?> {
  final DatabaseService databaseService;

  
  // 프로필 이미지 동기화 캐시 (5분마다 체크)
  DateTime? _lastSyncCheck;
  static const Duration _syncCheckInterval = Duration(minutes: 5);
  
  // Firestore 실시간 리스너
  StreamSubscription<DocumentSnapshot>? _firestoreSubscription;
  
  NicknameNotifier(this.databaseService) : super(null);

  @override
  void dispose() {
    _firestoreSubscription?.cancel();
    super.dispose();
  }

  /// 로그인/앱 시작 시 닉네임 자동 로드 및 실시간 동기화 설정
  Future<void> loadNickname() async {
    // 자동 로그아웃으로 인한 닉네임 초기화 플래그 확인
    await _checkAndHandleNicknameResetFlag();

    final result = await databaseService.safeTransaction((txn) async {
      return await txn.query('nicknames', limit: 1);
    }, taskName: 'loadNickname');

    String? dbProfileImageUrl;
    String? dbProfileImagePath;
    String? name;
    int? sellerId;
    bool hasLocalNickname = false;

    if (result.isNotEmpty) {
      name = result.first['name'] as String?;
      sellerId = result.first['sellerId'] as int?;
      dbProfileImagePath = result.first['profileImagePath'] as String?;
      dbProfileImageUrl = result.first['profileImageUrl'] as String?;

      if (name != null) {
        hasLocalNickname = true;
        // 로컬 닉네임이 있으면 먼저 state에 설정 (UI 표시용)
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
        LoggerUtils.logInfo('로컬 닉네임 로드 완료: $name', tag: 'NicknameProvider');
      }

      if (name != null) {
        // 프로필 이미지 동기화 체크 제한 (5분마다만)
        final now = DateTime.now();
        final shouldCheckSync = _lastSyncCheck == null ||
            now.difference(_lastSyncCheck!) > _syncCheckInterval;

        if (!shouldCheckSync) {
          // 캐시된 상태 그대로 반환 (로그 없음)
          return;
        }

        _lastSyncCheck = now;
        
        // Firestore 실시간 리스너 설정 (한 번만)
        _setupRealtimeSync();
        
        // Firestore에서 최신 profileImageUrl 확인 (오류 시 무시)
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          try {
            final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
            final fsProfileImageUrl = doc.data()?['profileImageUrl'] as String?;

            // 로컬 파일 존재 여부 확인
            bool localFileExists = false;
            if (dbProfileImagePath != null && dbProfileImagePath.isNotEmpty) {
              localFileExists = await File(dbProfileImagePath).exists();
              if (!localFileExists) {
                LoggerUtils.logWarning('로컬 프로필 이미지 파일이 존재하지 않음: $dbProfileImagePath', tag: 'NicknameProvider');
                dbProfileImagePath = null; // 존재하지 않는 경로는 null로 처리
              }
            }

            // Firebase URL이 있고, (로컬 URL과 다르거나 로컬 파일이 없는 경우) 다운로드
            if (fsProfileImageUrl != null &&
                (fsProfileImageUrl != dbProfileImageUrl || !localFileExists)) {
              LoggerUtils.logInfo('프로필 이미지 다운로드 시작: $fsProfileImageUrl', tag: 'NicknameProvider');
              var url = fsProfileImageUrl + '?t=${DateTime.now().millisecondsSinceEpoch}';
              try {
                final dir = await getApplicationDocumentsDirectory();
                final timestamp = DateTime.now().millisecondsSinceEpoch;
                // 새로운 파일명 패턴: profile_{userId}_{timestamp}.jpg
                final file = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');
                final response = await http.get(Uri.parse(url));

                if (response.statusCode == 200) {
                  await file.writeAsBytes(response.bodyBytes);
                  dbProfileImagePath = file.path;

                  // 기존 프로필 이미지 파일들 정리 (최신 것만 유지)
                  final files = dir
                      .listSync()
                      .whereType<File>()
                      .where((f) => (f.path.contains('profile_image_${user.uid}_') || f.path.contains('profile_${user.uid}_')) && f.path.endsWith('.jpg'))
                      .toList();
                  // 수정시간 기준 내림차순 정렬
                  files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
                  // 최신 파일 외에는 삭제하되, 방금 저장한 파일은 절대 삭제하지 않음
                  for (int i = 1; i < files.length; i++) {
                    final f = files[i];
                    if (f.path == file.path) continue;
                    try { await f.delete(); } catch (_) {}
                  }

                  LoggerUtils.logInfo('프로필 이미지 다운로드 완료: ${file.path}', tag: 'NicknameProvider');
                } else {
                  LoggerUtils.logError('프로필 이미지 다운로드 실패: HTTP ${response.statusCode}', tag: 'NicknameProvider');
                }

                // DB에 최신 profileImageUrl, profileImagePath 저장 (실패해도 무시)
                try {
                  await databaseService.safeTransaction((txn) async {
                    await txn.execute('''
                      REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                      VALUES (?, ?, ?, ?)
                    ''', [name, sellerId, dbProfileImagePath, fsProfileImageUrl]);
                  });
                } catch (e) {
                  LoggerUtils.logWarning('닉네임 DB 업데이트 실패: $e', tag: 'NicknameProvider');
                }
                state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
                return;
              } catch (e) {
                LoggerUtils.logError('프로필 이미지 다운로드 실패: $e', tag: 'NicknameProvider');
                // 이미지 다운로드 실패해도 state는 유지
                state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
                return;
              }
            } else {
              // 동기화 불필요 (로그 완전 제거)
              state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
              return;
            }
          } catch (e) {
            // Firestore 접근 실패 시 로컬 데이터만 사용
            LoggerUtils.logWarning('Firestore 접근 실패, 로컬 데이터 사용: $e', tag: 'NicknameProvider');
            state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
            return;
          }
        }
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: dbProfileImagePath);
        return;
      }
    }
    // Firestore에서 닉네임 동기화(최초 등록 등) - 오류 시 무시
    try {
      // Firebase 초기화 확인
      await Firebase.initializeApp();
      final user = FirebaseAuth.instance.currentUser;
      if (user != null && user.uid.isNotEmpty) {
        try {
          final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
          if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
            name = doc.data()!['nickname'] as String;
            String? profileImagePath;
            String? profileImageUrl = doc.data()!['profileImageUrl'] as String?;
            if (profileImageUrl != null) {
              var url = profileImageUrl + '?t=${DateTime.now().millisecondsSinceEpoch}';
              try {
                final dir = await getApplicationDocumentsDirectory();
                final timestamp = DateTime.now().millisecondsSinceEpoch;
                // 새로운 파일명 패턴: profile_{userId}_{timestamp}.jpg
                final file = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');
                final response = await http.get(Uri.parse(url));
                await file.writeAsBytes(response.bodyBytes);
                profileImagePath = file.path;
                final files = dir
                    .listSync()
                    .whereType<File>()
                    .where((f) => (f.path.contains('profile_image_${user.uid}_') || f.path.contains('profile_${user.uid}_')) && f.path.endsWith('.jpg'))
                    .toList();
                // 수정시간 기준 내림차순 정렬
                files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
                // 최신 파일 외에는 삭제하되, 방금 저장한 파일은 절대 삭제하지 않음
                for (int i = 1; i < files.length; i++) {
                  final f = files[i];
                  if (f.path == file.path) continue;
                  try { await f.delete(); } catch (_) {}
                }
              } catch (_) {}
            }
            // 판매자 관련 로직은 제거 (순환 참조 방지)
            int? sellerId;
            // Firestore에서 닉네임을 불러온 경우 로컬 DB에도 저장 (실패해도 무시)
            try {
              await databaseService.safeTransaction((txn) async {
                await txn.execute('''
                  REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                  VALUES (?, ?, ?, ?)
                ''', [name, sellerId, profileImagePath, profileImageUrl]);
              });
            } catch (_) {}
            // ★ 파이어스토어에 닉네임이 있으면 무조건 state에 반영 (로컬DB 저장 실패해도)
            state = Nickname(name: name, sellerId: sellerId, profileImagePath: profileImagePath);
            return;
          }
        } catch (e) {
          // 네트워크 오류 등으로 파이어스토어 조회 실패 시에도 state를 null로 두지 않음 (로딩 상태는 별도 처리)
          LoggerUtils.logError(
            'Firestore 닉네임 조회 실패',
            error: e,
            tag: 'NicknameProvider',
          );
          // Firestore 권한 오류 시 조용히 처리
          if (e.toString().contains('PERMISSION_DENIED')) {
            LoggerUtils.logWarning('Firestore 권한 없음 - 로그인 후 다시 시도', tag: 'NicknameProvider');
            return; // 권한 없으면 더 이상 진행하지 않음
          }
        }
      }
    } catch (e) {
      // Firebase 초기화 실패 시 로컬 DB만 사용
      LoggerUtils.logError(
        'Firebase 초기화 실패',
        error: e,
        tag: 'NicknameProvider',
      );
    }

    // 로컬 닉네임이 없는 경우에만 state를 null로 설정
    if (!hasLocalNickname) {
      LoggerUtils.logInfo('로컬 및 서버에 닉네임이 없음 - 닉네임 설정 필요', tag: 'NicknameProvider');
      state = null;
    }
  }

  /// 로그아웃 시 닉네임 상태 초기화
  void clearNickname() {
    LoggerUtils.logInfo('NicknameProvider 상태 초기화', tag: 'NicknameProvider');
    _firestoreSubscription?.cancel();
    _firestoreSubscription = null;
    _lastSyncCheck = null;
    state = null;
  }

  /// 강제로 닉네임 복구 시도 (디버깅/복구 목적)
  Future<bool> forceRecoverNickname() async {
    try {
      LoggerUtils.logInfo('강제 닉네임 복구 시도', tag: 'NicknameProvider');

      // Firestore에서 닉네임 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
        if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
          final name = doc.data()!['nickname'] as String;
          LoggerUtils.logInfo('Firestore에서 닉네임 발견: $name', tag: 'NicknameProvider');

          // 판매자 관련 로직은 제거 (순환 참조 방지)
          int? sellerId;

          // 로컬 DB에 저장
          await databaseService.safeTransaction((txn) async {
            await txn.execute('''
              REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
              VALUES (?, ?, ?, ?)
            ''', [name, sellerId, null, null]);
          });

          // 상태 복구
          state = Nickname(name: name, sellerId: sellerId, profileImagePath: null);
          LoggerUtils.logInfo('닉네임 강제 복구 성공: $name', tag: 'NicknameProvider');
          return true;
        }
      }

      LoggerUtils.logWarning('Firestore에 닉네임이 없어 복구 실패', tag: 'NicknameProvider');
      return false;
    } catch (e) {
      LoggerUtils.logError('강제 닉네임 복구 실패', tag: 'NicknameProvider', error: e);
      return false;
    }
  }

  Future<void> setNickname(String newName) async {
    // 1. 온라인 상태 체크 (필수)
    if (!NetworkStatusUtil.isOnline) {
      throw Exception('닉네임 변경은 인터넷 연결이 필요합니다.');
    }

    // 2. 사용자 인증 확인
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('로그인이 필요합니다.');
    }

    final db = await databaseService.database;

    // 3. 기존 닉네임 정보 로드
    final result = await db.query('nicknames', limit: 1);
    int? sellerId;
    String? profileImagePath;

    if (result.isNotEmpty) {
      sellerId = result.first['sellerId'] as int?;
      profileImagePath = result.first['profileImagePath'] as String?;
    }

    try {
      // 4. 서버에 먼저 저장 (서버가 기준)
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'nickname': newName,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      LoggerUtils.logInfo('서버에 닉네임 저장 완료: $newName', tag: 'NicknameProvider');

      // 5. 서버 저장 성공 시 로컬에 저장
      LoggerUtils.logInfo('로컬 닉네임 저장 시작: $newName (sellerId: $sellerId, profileImagePath: $profileImagePath)', tag: 'NicknameProvider');

      await databaseService.safeTransaction((txn) async {
        LoggerUtils.logDebug('트랜잭션 시작 - 닉네임 저장', tag: 'NicknameProvider');

        // 저장 전 기존 데이터 확인
        final existingData = await txn.query('nicknames', limit: 1);
        LoggerUtils.logDebug('저장 전 기존 닉네임 데이터: ${existingData.isNotEmpty ? existingData.first : "없음"}', tag: 'NicknameProvider');

        // 안전한 방법: 기존 데이터 삭제 후 새로 삽입
        LoggerUtils.logDebug('기존 닉네임 데이터 삭제 후 새로 삽입', tag: 'NicknameProvider');

        // 1. 기존 데이터 모두 삭제
        await txn.execute('DELETE FROM nicknames');
        LoggerUtils.logDebug('기존 닉네임 데이터 삭제 완료', tag: 'NicknameProvider');

        // 2. 새 데이터 삽입
        await txn.execute('''
          INSERT INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
          VALUES (?, ?, ?, ?)
        ''', [newName, sellerId, profileImagePath, null]);
        LoggerUtils.logDebug('새 닉네임 데이터 삽입 완료', tag: 'NicknameProvider');

        LoggerUtils.logDebug('REPLACE 쿼리 실행 완료', tag: 'NicknameProvider');

        // 저장 후 검증
        final savedData = await txn.query('nicknames', limit: 1);
        if (savedData.isNotEmpty) {
          final savedName = savedData.first['name'] as String?;
          LoggerUtils.logInfo('저장 후 검증 - DB에서 읽은 닉네임: $savedName', tag: 'NicknameProvider');
          if (savedName != newName) {
            LoggerUtils.logError('저장 검증 실패! 저장하려던 이름: $newName, 실제 저장된 이름: $savedName', tag: 'NicknameProvider');
          }
        } else {
          LoggerUtils.logError('저장 검증 실패! 저장 후에도 데이터가 없음', tag: 'NicknameProvider');
        }
      }, taskName: 'setNickname_localSave');

      LoggerUtils.logInfo('로컬에 닉네임 저장 완료: $newName', tag: 'NicknameProvider');

      // 6. 상태 업데이트
      state = Nickname(name: newName, sellerId: sellerId, profileImagePath: profileImagePath);

      // 7. 판매자 이름 업데이트는 다른 곳에서 처리 (순환 참조 방지)
      if (sellerId == null) {
        LoggerUtils.logInfo('최초 닉네임 등록 - 판매자 생성은 행사 생성 시점으로 연기', tag: 'NicknameProvider');
      } else {
        LoggerUtils.logInfo('닉네임 변경 완료 - 판매자 이름 업데이트는 별도 처리됨', tag: 'NicknameProvider');
      }

    } catch (e) {
      LoggerUtils.logError('닉네임 변경 실패', error: e, tag: 'NicknameProvider');
      rethrow;
    }
  }

  /// 프로필 이미지 업데이트 (업로드 후 즉시 상태 반영)
  Future<void> updateProfileImage(String localImagePath, String downloadUrl) async {
    try {
      final db = await databaseService.database;
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // 현재 닉네임 정보 가져오기
      final result = await db.query('nicknames', limit: 1);
      if (result.isEmpty) {
        // 닉네임이 없으면 loadNickname 호출
        await loadNickname();
        return;
      }

      final currentData = result.first;
      final name = currentData['name'] as String;
      final sellerId = currentData['sellerId'] as int?;

      // REPLACE를 사용하여 UNIQUE 제약 문제 해결
      await databaseService.safeTransaction((txn) async {
        await txn.execute('''
          REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
          VALUES (?, ?, ?, ?)
        ''', [name, sellerId, localImagePath, downloadUrl]);
      });

      // 상태 즉시 업데이트 (새로운 인스턴스로 생성하여 위젯 리빌드 보장)
      final newNickname = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);
      state = newNickname;

      LoggerUtils.logDebug(
        'NicknameProvider 프로필 이미지 상태 업데이트 완료: $localImagePath',
        tag: 'NicknameProvider',
      );

      // 상태 변경 알림을 위해 약간의 지연 후 다시 한번 상태 업데이트
      await Future.delayed(const Duration(milliseconds: 100));
      if (state?.profileImagePath == localImagePath) {
        // 동일한 데이터로 새 인스턴스 생성하여 리빌드 강제 트리거
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);

        // 추가 리빌드 보장을 위한 한번 더 업데이트
        await Future.delayed(const Duration(milliseconds: 50));
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);
      }
    } catch (e) {
      LoggerUtils.logError(
        'NicknameProvider 프로필 이미지 업데이트 실패',
        error: e,
        tag: 'NicknameProvider',
      );
      // 실패해도 loadNickname으로 복구 시도
      await loadNickname();
    }
  }

  /// 프로필 이미지 로컬 전용 업데이트 (Firebase 없이)
  Future<void> updateProfileImageLocal(String localImagePath) async {
    try {
      final db = await databaseService.database;
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // 현재 닉네임 정보 가져오기
      final result = await db.query('nicknames', limit: 1);
      if (result.isEmpty) {
        // 닉네임이 없으면 loadNickname 호출
        await loadNickname();
        return;
      }

      final currentData = result.first;
      final name = currentData['name'] as String;
      final sellerId = currentData['sellerId'] as int?;

      // REPLACE를 사용하여 UNIQUE 제약 문제 해결 (URL은 null로 설정)
      await databaseService.safeTransaction((txn) async {
        await txn.execute('''
          REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
          VALUES (?, ?, ?, ?)
        ''', [name, sellerId, localImagePath, null]);
      });

      // 상태 즉시 업데이트 (새로운 인스턴스로 생성하여 위젯 리빌드 보장)
      final newNickname = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);
      state = newNickname;

      LoggerUtils.logDebug(
        'NicknameProvider 프로필 이미지 로컬 상태 업데이트 완료: $localImagePath',
        tag: 'NicknameProvider',
      );

      // 상태 변경 알림을 위해 약간의 지연 후 다시 한번 상태 업데이트
      await Future.delayed(const Duration(milliseconds: 100));
      if (state?.profileImagePath == localImagePath) {
        // 동일한 데이터로 새 인스턴스 생성하여 리빌드 강제 트리거
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);

        // 추가 리빌드 보장을 위한 한번 더 업데이트
        await Future.delayed(const Duration(milliseconds: 50));
        state = Nickname(name: name, sellerId: sellerId, profileImagePath: localImagePath);
      }
    } catch (e) {
      LoggerUtils.logError(
        'NicknameProvider 프로필 이미지 로컬 업데이트 실패',
        error: e,
        tag: 'NicknameProvider',
      );
      // 실패해도 loadNickname으로 복구 시도
      await loadNickname();
    }
  }





  /// 닉네임만 별도로 동기화 (플랜에 관계없이 항상 실행)
  Future<void> syncNicknameOnly() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되어 있지 않아 닉네임 동기화를 건너뜁니다', tag: 'NicknameProvider');
        return;
      }

      LoggerUtils.logInfo('닉네임 전용 동기화 시작', tag: 'NicknameProvider');

      // Firestore에서 닉네임 확인
      final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
        final serverNickname = doc.data()!['nickname'] as String;

        // 현재 로컬 닉네임과 비교
        final currentNickname = state;
        LoggerUtils.logInfo('닉네임 동기화 비교 - 서버: "$serverNickname", 로컬: "${currentNickname?.name ?? "null"}"', tag: 'NicknameProvider');

        if (currentNickname == null || currentNickname.name != serverNickname) {
          LoggerUtils.logInfo('서버 닉네임과 로컬 닉네임이 다름 - 서버 우선으로 동기화: $serverNickname', tag: 'NicknameProvider');

          // 서버 닉네임으로 로컬 업데이트
          await _syncNicknameFromServer(serverNickname);
        } else {
          LoggerUtils.logInfo('서버와 로컬 닉네임이 일치함: $serverNickname', tag: 'NicknameProvider');
        }
      } else {
        // 서버에 닉네임이 없고 로컬에 있으면 서버로 업로드
        final currentNickname = state;
        if (currentNickname != null) {
          LoggerUtils.logInfo('서버에 닉네임이 없어 로컬 닉네임을 서버로 업로드: ${currentNickname.name}', tag: 'NicknameProvider');
          await _uploadNicknameToServer(currentNickname.name);
        }
      }

      LoggerUtils.logInfo('닉네임 전용 동기화 완료', tag: 'NicknameProvider');
    } catch (e) {
      LoggerUtils.logError('닉네임 전용 동기화 실패', tag: 'NicknameProvider', error: e);
    }
  }

  /// 서버 닉네임을 로컬로 동기화
  Future<void> _syncNicknameFromServer(String serverNickname) async {
    try {
      LoggerUtils.logInfo('서버 닉네임 로컬 동기화 시작: $serverNickname', tag: 'NicknameProvider');

      // 기존 로컬 데이터 확인
      final result = await databaseService.safeTransaction((txn) async {
        return await txn.query('nicknames', limit: 1);
      }, taskName: 'syncFromServer_checkExisting');

      int? sellerId;
      String? profileImagePath;

      if (result.isNotEmpty) {
        sellerId = result.first['sellerId'] as int?;
        profileImagePath = result.first['profileImagePath'] as String?;
        LoggerUtils.logInfo('기존 로컬 데이터 유지 - sellerId: $sellerId, profileImagePath: $profileImagePath', tag: 'NicknameProvider');
      }

      // 로컬 DB에 저장
      await databaseService.safeTransaction((txn) async {
        LoggerUtils.logDebug('서버 동기화 트랜잭션 시작', tag: 'NicknameProvider');

        await txn.execute('''
          REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
          VALUES (?, ?, ?, ?)
        ''', [serverNickname, sellerId, profileImagePath, null]);

        // 저장 후 검증
        final savedData = await txn.query('nicknames', limit: 1);
        if (savedData.isNotEmpty) {
          final savedName = savedData.first['name'] as String?;
          LoggerUtils.logInfo('서버 동기화 저장 검증 - DB에서 읽은 닉네임: $savedName', tag: 'NicknameProvider');
        }
      }, taskName: 'syncFromServer_save');

      // 상태 업데이트
      state = Nickname(name: serverNickname, sellerId: sellerId, profileImagePath: profileImagePath);
      LoggerUtils.logInfo('서버 닉네임 로컬 동기화 완료: $serverNickname', tag: 'NicknameProvider');
    } catch (e) {
      LoggerUtils.logError('서버 닉네임 로컬 동기화 실패', tag: 'NicknameProvider', error: e);
    }
  }

  /// 로컬 닉네임을 서버로 업로드
  Future<void> _uploadNicknameToServer(String nickname) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .set({'nickname': nickname}, SetOptions(merge: true));

      LoggerUtils.logInfo('로컬 닉네임 서버 업로드 완료: $nickname', tag: 'NicknameProvider');
    } catch (e) {
      LoggerUtils.logError('로컬 닉네임 서버 업로드 실패', tag: 'NicknameProvider', error: e);
    }
  }

  /// Firestore 실시간 동기화 설정 (읽기 사용량 최적화를 위해 비활성화)
  void _setupRealtimeSync() {
    LoggerUtils.logInfo('프로필 실시간 구독 비활성화 (읽기 사용량 최적화)', tag: 'NicknameProvider');

    // 기존 구독이 있다면 해제
    _firestoreSubscription?.cancel();
    _firestoreSubscription = null;

    // 필요할 때만 프로필 정보를 읽도록 변경
    // 프로필 업데이트 시에만 _loadProfileFromFirestore() 호출
  }

  // 실시간 프로필 업데이트 메서드는 읽기 사용량 최적화를 위해 제거됨
  // 필요할 때만 _loadProfileFromFirestore()를 호출하도록 변경

  /// 자동 로그아웃으로 인한 닉네임 초기화 플래그 확인 및 처리
  Future<void> _checkAndHandleNicknameResetFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final needsReset = prefs.getBool('nickname_reset_required') ?? false;

      if (needsReset) {
        LoggerUtils.logInfo('닉네임 초기화 플래그 감지 - Firestore에서 닉네임 복구 시도', tag: 'NicknameProvider');

        // Firestore에서 닉네임 복구 시도
        bool recovered = false;
        try {
          final user = FirebaseAuth.instance.currentUser;
          if (user != null) {
            final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
            if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
              final name = doc.data()!['nickname'] as String;
              LoggerUtils.logInfo('Firestore에서 닉네임 복구 성공: $name', tag: 'NicknameProvider');

              // 판매자 관련 로직은 제거 (순환 참조 방지)
              int? sellerId;

              // 로컬 DB에 저장
              await databaseService.safeTransaction((txn) async {
                await txn.execute('''
                  REPLACE INTO nicknames (name, sellerId, profileImagePath, profileImageUrl)
                  VALUES (?, ?, ?, ?)
                ''', [name, sellerId, null, null]);
              });

              // 상태 복구
              state = Nickname(name: name, sellerId: sellerId, profileImagePath: null);
              recovered = true;
            }
          }
        } catch (e) {
          LoggerUtils.logWarning('Firestore 닉네임 복구 실패: $e', tag: 'NicknameProvider');
        }

        if (!recovered) {
          // 복구 실패 시에만 상태 초기화
          LoggerUtils.logInfo('닉네임 복구 실패 - 상태 초기화', tag: 'NicknameProvider');
          state = null;
        }

        // 플래그 제거
        await prefs.remove('nickname_reset_required');

        LoggerUtils.logInfo('닉네임 초기화 플래그 처리 완료 (복구: $recovered)', tag: 'NicknameProvider');
      }
    } catch (e) {
      LoggerUtils.logError('닉네임 초기화 플래그 처리 실패', tag: 'NicknameProvider', error: e);
    }
  }
}

final nicknameProvider = StateNotifierProvider<NicknameNotifier, Nickname?>((ref) {
  final db = ref.watch(databaseServiceProvider);
  return NicknameNotifier(db);
});
