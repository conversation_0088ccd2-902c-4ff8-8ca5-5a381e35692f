import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prepayment_product_link.dart';
import '../repositories/prepayment_product_link_repository.dart';
import '../services/database_service.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨
// 로컬 전용 모드: 구독 서비스 제거됨
import '../models/event_workspace.dart';
import '../utils/logger_utils.dart';
// 로컬 전용 모드: 구독 관련 import 제거됨
import 'unified_workspace_provider.dart';

final prepaymentProductLinkRepositoryProvider = Provider<PrepaymentProductLinkRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return PrepaymentProductLinkRepository(database: databaseService);
});

class PrepaymentProductLinkNotifier extends StateNotifier<List<PrepaymentProductLink>> {
  final PrepaymentProductLinkRepository repository;
  final Ref ref;
  String? errorMessage;
  // 로컬 전용 모드: 실시간 동기화 제거됨

  // 무한 루프 방지를 위한 최근 추가한 연동 캐시
  final Set<String> _recentlyAddedLinks = <String>{};

  PrepaymentProductLinkNotifier(this.repository, this.ref) : super([]) {
    _setupRealtimeSync();
  }

  /// 로컬 전용 모드: 실시간 동기화 제거됨
  void _setupRealtimeSync() {
    // 로컬 전용 모드에서는 실시간 동기화를 사용하지 않음
    LoggerUtils.logInfo('로컬 전용 모드: 선입금-상품 연동 실시간 동기화 건너뜀', tag: 'PrepaymentProductLinkNotifier');
  }

  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> loadLinks({bool showLoading = true}) async {
    // 현재 행사 워크스페이스 확인
    EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

    if (currentWorkspace == null) {
      state = [];
      errorMessage = '워크스페이스를 선택해주세요';
      return;
    }

    int retry = 0;
    while (retry < 3) {
      try {
        final links = await repository.getAllLinksByEventId(currentWorkspace.id);
        state = links;
        errorMessage = null;
        return;
      } catch (e) {
        final msg = e.toString();
        if (msg.contains('no such table') || msg.contains('database is not open')) {
          retry++;
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        } else {
          errorMessage = msg;
          return;
        }
      }
    }
    errorMessage = '데이터베이스 초기화 중입니다. 잠시 후 다시 시도해주세요.';
  }

  Future<void> addLink(PrepaymentProductLink link) async {
    await repository.insertLink(link);

    // 최근 추가한 연동으로 캐시 (무한 루프 방지용)
    final linkId = '${link.virtualProductId}_${link.productId}';
    _recentlyAddedLinks.add(linkId);
    // 5초 후 캐시에서 제거
    Future.delayed(const Duration(seconds: 5), () {
      _recentlyAddedLinks.remove(linkId);
    });

    // 로컬 전용 모드: Firebase 업로드 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: 선입금-상품 연동 Firebase 업로드 건너뜀', tag: 'PrepaymentProductLinkNotifier');

    await loadLinks(showLoading: false);
  }

  Future<void> removeLink(int virtualProductId, int productId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    await repository.deleteLink(virtualProductId, productId, currentWorkspace.id);

    // 로컬 전용 모드: Firebase 삭제 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: 선입금-상품 연동 Firebase 삭제 건너뜀: $virtualProductId -> $productId', tag: 'PrepaymentProductLinkNotifier');

    await loadLinks(showLoading: false);
  }

  /// 가상 상품 삭제 시 해당 가상상품에 연결된 모든 연동 제거
  Future<void> removeLinksByVirtualProductIds(List<int> virtualProductIds) async {
    if (virtualProductIds.isEmpty) return;
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;
  await repository.batchDeleteByVirtualProductIds(virtualProductIds, currentWorkspace.id);
    // 로컬 전용 모드: Firebase 연동 삭제 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: Firebase 연동 삭제 건너뜀: ${virtualProductIds.length}개', tag: 'PrepaymentProductLinkNotifier');
    await loadLinks(showLoading: false);
  }

  /// 상품 삭제 시 해당 상품과 연결된 모든 연동 제거
  Future<void> removeLinksByProductIds(List<int> productIds) async {
    if (productIds.isEmpty) return;
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;
  await repository.batchDeleteByProductIds(productIds, currentWorkspace.id);
    // 로컬 전용 모드: Firebase 연동 삭제 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: Firebase 연동 삭제 건너뜀: ${productIds.length}개', tag: 'PrepaymentProductLinkNotifier');
    await loadLinks(showLoading: false);
  }

  Future<void> batchAddLinks(List<PrepaymentProductLink> links) async {
    if (links.isEmpty) return;
    await repository.batchInsertLinks(links);

    // 로컬 전용 모드: Firebase 배치 업로드 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: 선입금-상품 연동 Firebase 일괄 추가 건너뜀: ${links.length}개', tag: 'PrepaymentProductLinkNotifier');

    await loadLinks(showLoading: false);
  }

  Future<void> batchRemoveLinks(List<PrepaymentProductLink> links) async {
    if (links.isEmpty) return;
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;
    final pairs = links.map((l) => {'virtualProductId': l.virtualProductId, 'productId': l.productId}).toList();
    await repository.batchDeleteLinksByPairs(pairs, currentWorkspace.id);
    // 로컬 전용 모드: Firebase 연동 배치 삭제 제거됨
    LoggerUtils.logInfo('로컬 전용 모드: Firebase 연동 배치 삭제 건너뜀: ${links.length}개', tag: 'PrepaymentProductLinkNotifier');
    await loadLinks(showLoading: false);
  }

  Future<void> loadLinksByVirtualProduct(int virtualProductId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      state = [];
      return;
    }

    final links = await repository.getLinksByVirtualProductId(virtualProductId, currentWorkspace.id);
    state = links;
  }

  Future<void> loadLinksByProduct(int productId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      state = [];
      return;
    }

    final links = await repository.getLinksByProductId(productId, currentWorkspace.id);
    state = links;
  }
}

final prepaymentProductLinkNotifierProvider = StateNotifierProvider<PrepaymentProductLinkNotifier, List<PrepaymentProductLink>>((ref) {
  final repository = ref.watch(prepaymentProductLinkRepositoryProvider);
  return PrepaymentProductLinkNotifier(repository, ref);
});
