import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/revenue_goal.dart';
import '../models/event_workspace.dart';
import '../models/event.dart';
import '../repositories/revenue_goal_repository.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/sales_log_provider.dart';
// 로컬 전용 모드: 실시간 동기화 및 구독 관련 import 제거됨


import '../utils/logger_utils.dart';


/// 목표 수익 Provider
final revenueGoalNotifierProvider = StateNotifierProvider<RevenueGoalNotifier, RevenueGoalState>((ref) {
  return RevenueGoalNotifier(ref);
});

/// 목표 수익 통계 Provider (필터링된 데이터 기반)
final revenueGoalStatsProvider = Provider<RevenueGoalStats>((ref) {
  final goals = ref.watch(revenueGoalNotifierProvider).goals;
  final salesLogs = ref.watch(salesLogNotifierProvider).salesLogs;
  final currentWorkspace = ref.watch(currentWorkspaceProvider);

  if (currentWorkspace == null) {
    return const RevenueGoalStats();
  }

  // 현재 행사의 목표 수익만 필터링하고, 선택된 모드에 따라 추가 필터링
  List<RevenueGoal> eventGoals = goals.where((goal) => goal.eventId == currentWorkspace.id).toList();

  // 목표 관리 모드에 따라 필터링
  if (currentWorkspace.revenueGoalMode == RevenueGoalMode.overall) {
    // 전체 모드: sellerId가 null인 목표만
    eventGoals = eventGoals.where((goal) => goal.sellerId == null).toList();
  } else {
    // 판매자별 모드: sellerId가 null이 아닌 목표만
    eventGoals = eventGoals.where((goal) => goal.sellerId != null).toList();
  }

  // 목표 수익 총합 계산
  final totalTarget = eventGoals.fold<double>(0.0, (sum, goal) => sum + goal.targetAmount);

  // 실제 수익 총합 계산 (현재 행사의 판매 로그 기반, 할인 적용 후 실제 금액)
  final eventSalesLogs = salesLogs.where((log) => log.eventId == currentWorkspace.id).toList();
  final totalActual = eventSalesLogs.fold<double>(0.0, (sum, log) => sum + (log.totalAmount - log.setDiscountAmount - log.manualDiscountAmount).toDouble());

  // 달성률 계산
  final achievementRate = totalTarget > 0 ? (totalActual / totalTarget).clamp(0.0, double.infinity) : 0.0;
  
  // 목표 대비 차이
  final difference = totalActual - totalTarget;
  
  return RevenueGoalStats(
    totalTarget: totalTarget,
    totalActual: totalActual,
    achievementRate: achievementRate,
    difference: difference,
  );
});

/// 목표 수익 Notifier
class RevenueGoalNotifier extends StateNotifier<RevenueGoalState> {
  static const String _tag = 'RevenueGoalNotifier';
  
  final Ref ref;
  late final RevenueGoalRepository _repository;
  // 로컬 전용 모드: 실시간 동기화 제거됨
  final Set<String> _recentlyAddedGoals = <String>{};

  RevenueGoalNotifier(this.ref) : super(const RevenueGoalState()) {
    _repository = ref.read(revenueGoalRepositoryProvider);
    _watchCurrentEvent();
    _loadInitialData();
    // 로컬 전용 모드: 실시간 동기화 제거됨
  }

  @override
  void dispose() {
    super.dispose();
  }



  // 로컬 전용 모드: 실시간 동기화 관련 메서드들 제거됨

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - RevenueGoalNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: _tag);

        if (next != null) {
          loadGoals();
        } else {
          // 현재 행사 워크스페이스가 null이 되면 목표 수익 목록 클리어
          state = state.copyWith(
            goals: [],
            errorMessage: '행사 워크스페이스를 선택해주세요.',
          );
        }
      }
    });
  }



  /// 초기 데이터 로드
  Future<void> _loadInitialData() async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace != null) {
      await loadGoals();
    }
  }

  /// 목표 수익 목록 로드
  Future<void> loadGoals({bool showLoading = true}) async {
    try {
      if (showLoading) {
        state = state.copyWith(isLoading: true, errorMessage: null);
      }

      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        state = state.copyWith(
          goals: [],
          isLoading: false,
          errorMessage: '현재 선택된 행사가 없습니다.',
        );
        return;
      }

      final goals = await _repository.getGoalsByEventId(currentWorkspace.id);
      
      state = state.copyWith(
        goals: goals,
        isLoading: false,
        errorMessage: null,
      );

      LoggerUtils.logInfo('목표 수익 목록 로드 완료: ${goals.length}개', tag: _tag);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      LoggerUtils.logError('목표 수익 목록 로드 실패', tag: _tag, error: e);
    }
  }

  /// 목표 수익 추가
  Future<void> addGoal(RevenueGoal goal) async {
    if (state.isUpdating) return;

    try {
      state = state.copyWith(isUpdating: true, errorMessage: null);

      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        throw Exception('현재 선택된 행사가 없습니다.');
      }

      // 행사 ID 설정
      final goalWithEventId = goal.copyWith(eventId: currentWorkspace.id);
      
      // 1. 로컬 DB에 저장
      final goalId = await _repository.addGoal(goalWithEventId);
      // 로컬 전용 모드: savedGoal 변수 불필요

      // 무한 루프 방지: 최근 추가한 목표로 등록
      _recentlyAddedGoals.add(goalId);

      // 2. Firebase에 즉시 업로드 (실시간 동기화)
      try {
        // 로컬 전용 모드: 서버 업로드 불필요
        LoggerUtils.logInfo('목표 수익 Firebase 업로드 성공: ID $goalId', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('목표 수익 Firebase 업로드 실패 (로컬 저장은 성공): ID $goalId', tag: _tag, error: e);
      }

      // 일정 시간 후 최근 추가 목록에서 제거 (무한 루프 방지 해제)
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyAddedGoals.remove(goalId);
      });

      // 3. 상태 업데이트
      await loadGoals(showLoading: false);

    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
      LoggerUtils.logError('목표 수익 추가 실패', tag: _tag, error: e);
    } finally {
      state = state.copyWith(isUpdating: false);
    }
  }

  /// 목표 수익 수정
  Future<void> updateGoal(RevenueGoal goal) async {
    if (state.isUpdating) return;

    try {
      state = state.copyWith(isUpdating: true, errorMessage: null);

      // 1. 로컬 DB 수정
      await _repository.updateGoal(goal);

      // 무한 루프 방지: 최근 수정한 목표로 등록
      if (goal.id != null) {
        _recentlyAddedGoals.add(goal.id!);
      }

      // 로컬 전용 모드: Firebase 업로드 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 목표 수익 수정 서버 업로드 건너뜀: ID ${goal.id}', tag: _tag);

      // 일정 시간 후 최근 수정 목록에서 제거
      if (goal.id != null) {
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyAddedGoals.remove(goal.id!);
        });
      }

      // 3. 상태 업데이트
      await loadGoals(showLoading: false);

    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
      LoggerUtils.logError('목표 수익 수정 실패', tag: _tag, error: e);
    } finally {
      state = state.copyWith(isUpdating: false);
    }
  }

  /// 목표 수익 삭제
  Future<void> deleteGoal(String goalId) async {
    if (state.isUpdating) return;

    try {
      state = state.copyWith(isUpdating: true, errorMessage: null);

      // 1. 로컬 DB에서 삭제
      await _repository.deleteGoal(goalId);

      // 로컬 전용 모드: Firebase 삭제 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 목표 수익 삭제 서버 동기화 건너뜀: ID $goalId', tag: _tag);

      // 3. 상태 업데이트
      await loadGoals(showLoading: false);

    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
      LoggerUtils.logError('목표 수익 삭제 실패', tag: _tag, error: e);
    } finally {
      state = state.copyWith(isUpdating: false);
    }
  }
}
