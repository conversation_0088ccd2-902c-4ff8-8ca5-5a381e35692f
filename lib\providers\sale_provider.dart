import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/sale.dart';
import '../models/product.dart';
import '../repositories/sale_repository.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';
import 'sale_state.dart';
import 'product_provider.dart';
// sale_business_logic.dart는 나중에 구현 예정

// SalePosState는 SaleState로 통합되었습니다.

/// POS 판매 상태를 관리하는 Notifier 클래스입니다.
class SaleNotifier extends StateNotifier<SaleState> {
  static const String _tag = 'SaleNotifier';

  final Ref ref;
  bool _isPaused = false;

  SaleNotifier(this.ref) : super(const SaleState());

  void pause() {
    if (!_isPaused) {
      _isPaused = true;
      LoggerUtils.logDebug('SaleNotifier paused', tag: _tag);
    }
  }

  void resume([Duration? delay]) {
    if (_isPaused) {
      _isPaused = false;
      LoggerUtils.logDebug('SaleNotifier resumed', tag: _tag);
    }
  }

  void addProduct(Product product, [int quantity = 1]) {
    if (product.id == null) return;
    final quantities = Map<int, int>.from(state.productQuantities);
    quantities[product.id!] = (quantities[product.id!] ?? 0) + quantity;
    state = state.copyWith(productQuantities: quantities);
  }

  void removeProduct(Product product) {
    if (product.id == null) return;
    final quantities = Map<int, int>.from(state.productQuantities);
    final currentQuantity = quantities[product.id!] ?? 0;
    if (currentQuantity > 0) {
      quantities[product.id!] = currentQuantity - 1;
      if (quantities[product.id!] == 0) {
        quantities.remove(product.id!);
      }
    }
    state = state.copyWith(productQuantities: quantities);
  }

  void addSale(Sale sale, [int quantity = 1]) {
    final quantities = Map<int, int>.from(state.saleQuantities);
    quantities[sale.productId] = (quantities[sale.productId] ?? 0) + quantity;
    state = state.copyWith(saleQuantities: quantities);
  }

  void removeSale(Sale sale) {
    final quantities = Map<int, int>.from(state.saleQuantities);
    quantities.remove(sale.productId);
    state = state.copyWith(saleQuantities: quantities);
  }

  void clearAll() {
    state = const SaleState();
  }

  void updateProductQuantity(int productId, int quantity) {
    final quantities = Map<int, int>.from(state.productQuantities);
    if (quantity <= 0) {
      quantities.remove(productId);
    } else {
      quantities[productId] = quantity;
    }
    state = state.copyWith(productQuantities: quantities);
  }

  void updateSaleQuantity(int productId, int quantity) {
    final quantities = Map<int, int>.from(state.saleQuantities);
    if (quantity <= 0) {
      quantities.remove(productId);
    } else {
      quantities[productId] = quantity;
    }
    state = state.copyWith(saleQuantities: quantities);
  }

  void updateTotalAmount(int amount) {
    state = state.copyWith(totalAmount: amount);
  }

  Future<void> processSale() async {
    LoggerUtils.methodStart('processSale', tag: 'SaleNotifier');

    if (state.isProcessing) {
      LoggerUtils.logWarning('Sale processing already in progress', tag: 'SaleNotifier');
      return;
    }

    state = state.copyWith(isProcessing: true);

    try {
      final productState = ref.read(productNotifierProvider);
      final databaseService = ref.read(databaseServiceProvider);

      // 1. 재고 확인
      for (final entry in state.productQuantities.entries) {
        final productIndex = productState.products.indexWhere((p) => p.id == entry.key);

        if (productIndex == -1) {
          throw Exception('상품을 찾을 수 없습니다: ID ${entry.key}');
        }

        final product = productState.products[productIndex];

        if (product.quantity < entry.value) {
          throw Exception('재고 부족: ${product.name} (필요: ${entry.value}, 보유: ${product.quantity})');
        }
      }

      // 2. 판매 처리 데이터 준비
      final sales = <Sale>[];
      for (final entry in state.productQuantities.entries) {
        final product = productState.products.firstWhere(
          (p) => p.id == entry.key,
          orElse: () => Product(
            id: entry.key,
            name: 'Unknown Product',
            price: 0,
            quantity: 0,
            sellerName: 'Unknown',
          ),
        );
        final sale = Sale(
          productId: entry.key,
          quantity: entry.value,
          totalPrice: product.price * entry.value,
          saleTimestamp: DateTime.now(),
          name: product.name,
          sellerName: product.sellerName,
        );
        sales.add(sale);
      }

      // 3. 트랜잭션으로 판매 기록 저장 및 재고 업데이트를 원자적으로 처리
      await databaseService.safeTransaction((txn) async {
        // 3-1. 판매 기록 저장
        for (final sale in sales) {
          await txn.insert('sales', sale.toMap());
        }

        // 3-2. 재고 업데이트
        for (final entry in state.productQuantities.entries) {
          await txn.update(
            'products',
            {'quantity': 'quantity - ?'},
            where: 'id = ?',
            whereArgs: [entry.value, entry.key],
          );
        }
      }, taskName: 'processSale');

      // 4. 상태 초기화
      state = state.copyWith(
        productQuantities: {},
        saleQuantities: {},
        totalAmount: 0,
        isProcessing: false,
      );

      LoggerUtils.logInfo('Sale processed successfully with transaction', tag: 'SaleNotifier');
    } catch (e) {
      LoggerUtils.logError('Sale processing failed', tag: 'SaleNotifier', error: e);
      state = state.copyWith(
        isProcessing: false,
        errorMessage: e.toString(),
      );
    } finally {
      LoggerUtils.methodEnd('processSale', tag: 'SaleNotifier');
    }
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> loadSales({bool showLoading = true}) async {
    final repository = ref.read(saleRepositoryProvider);
    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }
    try {
      final sales = await repository.getAllSales();
      state = state.copyWith(sales: sales, isLoading: false, errorMessage: null);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      // 기존 sales 데이터는 유지
    }
  }
}

// Provider 정의
final saleNotifierProvider = StateNotifierProvider<SaleNotifier, SaleState>((ref) {
  return SaleNotifier(ref);
});

/// 총 금액 Provider
final saleTotalAmountProvider = Provider<int>((ref) {
  return ref.watch(saleNotifierProvider).totalAmount;
});

/// 판매 처리 중 상태 Provider
final saleProcessingProvider = Provider<bool>((ref) {
  return ref.watch(saleNotifierProvider).isProcessing;
});

/// 상품 수량 Provider
final saleProductQuantitiesProvider = Provider<Map<int, int>>((ref) {
  return ref.watch(saleNotifierProvider).productQuantities;
});

/// 판매 수량 Provider
final saleSaleQuantitiesProvider = Provider<Map<int, int>>((ref) {
  return ref.watch(saleNotifierProvider).saleQuantities;
});
