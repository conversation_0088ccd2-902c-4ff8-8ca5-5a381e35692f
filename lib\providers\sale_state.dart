import '../models/sale.dart';
import 'base_state.dart';

enum SaleSortOption {
  recentlyAdded,
  nameAsc,
  nameDesc,
  priceAsc,
  priceDesc,
}

class SaleState extends BaseState {
  final List<Sale> sales;
  final List<Sale> filteredSales;
  final SaleSortOption currentSortOption;
  final String selectedSellerFilter;
  final String searchQuery;
  final bool isFiltering;
  // POS 관련 필드들
  final Map<int, int> productQuantities;
  final Map<int, int> saleQuantities;
  final int totalAmount;
  final bool isProcessing;

  const SaleState({
    this.sales = const [],
    this.filteredSales = const [],
    this.currentSortOption = SaleSortOption.recentlyAdded,
    this.selectedSellerFilter = '',
    this.searchQuery = '',
    this.isFiltering = false,
    this.productQuantities = const {},
    this.saleQuantities = const {},
    this.totalAmount = 0,
    this.isProcessing = false,
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  SaleState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  SaleState copyWith({
    List<Sale>? sales,
    List<Sale>? filteredSales,
    SaleSortOption? currentSortOption,
    String? selectedSellerFilter,
    String? searchQuery,
    bool? isFiltering,
    Map<int, int>? productQuantities,
    Map<int, int>? saleQuantities,
    int? totalAmount,
    bool? isProcessing,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return SaleState(
      sales: sales ?? this.sales,
      filteredSales: filteredSales ?? this.filteredSales,
      currentSortOption: currentSortOption ?? this.currentSortOption,
      selectedSellerFilter: selectedSellerFilter ?? this.selectedSellerFilter,
      searchQuery: searchQuery ?? this.searchQuery,
      isFiltering: isFiltering ?? this.isFiltering,
      productQuantities: productQuantities ?? this.productQuantities,
      saleQuantities: saleQuantities ?? this.saleQuantities,
      totalAmount: totalAmount ?? this.totalAmount,
      isProcessing: isProcessing ?? this.isProcessing,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    sales,
    filteredSales,
    currentSortOption,
    selectedSellerFilter,
    searchQuery,
    isFiltering,
    productQuantities,
    saleQuantities,
    totalAmount,
    isProcessing,
  ];

  /// 초기 상태 반환
  static SaleState initialState() {
    return const SaleState();
  }
} 
