import 'package:riverpod/riverpod.dart';
import '../../models/sales_log.dart';
import '../../repositories/sales_log_repository.dart' as repo;
import '../../utils/logger_utils.dart';
import '../../utils/cancellation_token.dart';
import '../../utils/provider_exception.dart';
import '../sales_log_provider.dart';
import 'sales_log_state.dart';

/// 판매 기록 배치 처리 작업을 담당하는 클래스
///
/// 주요 기능:
/// - 대용량 판매 기록 일괄 삽입/수정/삭제
/// - 배치 처리 진행률 관리
/// - 취소 토큰 관리
/// - 오류 처리 및 로깅
class SalesLogBatchOperations {
  static const String _tag = 'SalesLogBatchOperations';
  static const String _domain = 'SLG_BATCH';

  final dynamic Function(SalesLogState) _updateState;
  final repo.SalesLogRepository _salesLogRepository;

  SalesLogBatchOperations({
    required repo.SalesLogRepository salesLogRepository,
    required dynamic Function(SalesLogState) updateState,
  })  : _salesLogRepository = salesLogRepository,
        _updateState = updateState;

  /// 배치 처리 취소 토큰
  CancellationToken? _cancellationToken;

  /// 배치 처리 취소
  void cancelBatchOperation() {
    _cancellationToken?.cancel();
    _cancellationToken = null;
  }

  /// 배치 처리 진행률 업데이트
  void _updateBatchProgress(double progress, String operation) {
    _updateState(SalesLogState(
      batchProgress: progress,
      batchOperation: operation,
      isBatchProcessing: progress < 1.0,
    ));
  }

  /// 배치 처리 오류 처리
  void _handleBatchError(SalesLog item, dynamic error) {
    LoggerUtils.error(
      '배치 처리 중 항목 오류',
      error: error is Exception ? error : Exception(error.toString()),
      tag: _tag,
      data: {'item': item.toString()},
    );
  }

  /// 대용량 판매 기록 일괄 삽입
  Future<void> insertSalesLogsBatch(List<SalesLog> logs) async {
    LoggerUtils.methodStart('insertSalesLogsBatch', tag: _tag);

    try {
      _updateState(SalesLogState(
        isBatchProcessing: true,
        batchOperation: '판매 기록 일괄 등록',
        batchProgress: 0.0,
      ));

      _cancellationToken = CancellationToken();

      await _salesLogRepository.insertSalesLogsBatch(
        logs,
        onProgress: (progress) =>
            _updateBatchProgress(progress, '판매 기록 일괄 등록'),
        onItemError: _handleBatchError,
        cancellationToken: _cancellationToken,
      );

      // 상태 갱신 및 리스너 알림
      // invalidate는 반드시 UI/상위 Provider에서만 호출해야 함

      LoggerUtils.methodEnd('insertSalesLogsBatch', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매 기록 일괄 삽입 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매 기록 일괄 삽입 중 오류가 발생했습니다',
        code: '${_domain}_INSERT_BATCH_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    }
  }

  /// 대용량 판매 기록 일괄 업데이트
  Future<void> updateSalesLogsBatch(List<SalesLog> logs) async {
    LoggerUtils.methodStart('updateSalesLogsBatch', tag: _tag);

    try {
      _updateState(SalesLogState(
        isBatchProcessing: true,
        batchOperation: '판매 기록 일괄 수정',
        batchProgress: 0.0,
      ));

      _cancellationToken = CancellationToken();

      await _salesLogRepository.updateSalesLogsBatch(
        logs,
        onProgress: (progress) =>
            _updateBatchProgress(progress, '판매 기록 일괄 수정'),
        onItemError: _handleBatchError,
        cancellationToken: _cancellationToken,
      );

      // 상태 갱신 및 리스너 알림
      // invalidate는 반드시 UI/상위 Provider에서만 호출해야 함

      LoggerUtils.methodEnd('updateSalesLogsBatch', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매 기록 일괄 수정 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매 기록 일괄 수정 중 오류가 발생했습니다',
        code: '${_domain}_UPDATE_BATCH_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    }
  }

  /// 대용량 판매 기록 일괄 삭제
  Future<void> deleteSalesLogsBatch(List<SalesLog> logs) async {
    LoggerUtils.methodStart('deleteSalesLogsBatch', tag: _tag);

    try {
      _updateState(SalesLogState(
        isBatchProcessing: true,
        batchOperation: '판매 기록 일괄 삭제',
        batchProgress: 0.0,
      ));

      _cancellationToken = CancellationToken();

      await _salesLogRepository.deleteSalesLogsBatch(
        logs,
        onProgress: (progress) =>
            _updateBatchProgress(progress, '판매 기록 일괄 삭제'),
        onItemError: _handleBatchError,
        cancellationToken: _cancellationToken,
      );

      // 상태 갱신 및 리스너 알림
      // invalidate는 반드시 UI/상위 Provider에서만 호출해야 함

      LoggerUtils.methodEnd('deleteSalesLogsBatch', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매 기록 일괄 삭제 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매 기록 일괄 삭제 중 오류가 발생했습니다',
        code: '${_domain}_DELETE_BATCH_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    }
  }

  /// 판매 기록 일괄 처리 (일반적인 배치 작업)
  Future<void> processBatchOperation({
    required List<String> logIds,
    required String operation,
  }) async {
    LoggerUtils.methodStart('processBatchOperation', tag: _tag);

    try {
      _updateState(SalesLogState(
        isLoading: true,
        isBatchProcessing: true,
        batchOperation: operation,
      ));

      _cancellationToken = CancellationToken();

      await _salesLogRepository.processBatch(
        logIds: logIds,
        operation: operation,
        onProgress: (progress) {
          _cancellationToken?.throwIfCancelled();
          _updateState(SalesLogState(batchProgress: progress));
        },
      );

      _cancellationToken?.throwIfCancelled();

      // 작업 완료 후 목록 새로고침 및 리스너 알림
      // invalidate는 반드시 UI/상위 Provider에서만 호출해야 함

      LoggerUtils.methodEnd('processBatchOperation', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '일괄 처리 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );

      if (e is CancelledException) {
        _updateState(SalesLogState(
          isLoading: false,
          isBatchProcessing: false,
          batchProgress: null,
          batchOperation: null,
          isCancelled: true,
          errorMessage: e.message,
        ));
      } else {
        throw ProviderException(
          message: '일괄 처리 중 오류가 발생했습니다',
          code: '${_domain}_PROCESS_BATCH_ERROR',
          error: e is Exception ? e : Exception(e.toString()),
          stackTrace: stackTrace,
          severity: ProviderExceptionSeverity.error,
        );
      }
    }
  }
}

final salesLogBatchOperationsProvider = StateProvider<SalesLogBatchOperations?>((ref) {
  final repository = ref.watch(repo.salesLogRepositoryProvider);
  return SalesLogBatchOperations(
    salesLogRepository: repository,
    updateState: (state) => ref.read(salesLogNotifierProvider.notifier).updateState((_) => state),
  );
}); 
