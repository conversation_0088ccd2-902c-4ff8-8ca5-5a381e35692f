import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import '../../utils/logger_utils.dart';
import 'sales_log_state.dart';

/// 판매 기록 검색, 필터링, 정렬 기능을 담당하는 클래스
///
/// 주요 기능:
/// - 검색 기능
/// - 필터링 최적화
/// - 정렬 기능
/// - 필터 조건 관리
class SalesLogFilterSort {
  static const String _tag = 'SalesLogFilterSort';

  final dynamic Function(SalesLogState) _updateState;
  final Function(List<SalesLog>) _createDisplayItems;

  SalesLogFilterSort({
    required dynamic Function(SalesLogState) updateState,
    required Function(List<SalesLog>) createDisplayItems,
  })  : _updateState = updateState,
        _createDisplayItems = createDisplayItems;

  /// 검색 기능
  void searchSalesLogs(String query, List<SalesLog> salesLogs) {
    LoggerUtils.logInfo('Search sales logs with query: $query', tag: _tag);
    _performSearch(query, salesLogs);
  }

  /// 실제 검색 수행
  void _performSearch(String query, List<SalesLog> salesLogs) {
    LoggerUtils.methodStart('_performSearch', tag: _tag);

    try {
      final normalizedQuery = query.trim().toLowerCase();

      if (normalizedQuery.isEmpty) {
        // 빈 검색어 시 전체 데이터 표시
        _updateState(SalesLogState(
          filteredSalesLogs: salesLogs,
          displayItems: _createDisplayItems(salesLogs),
        ));
        return;
      }

      final filteredLogs = salesLogs.where(
        (salesLog) => _matchesSearchQuery(salesLog, normalizedQuery),
      ).toList();

      _updateState(SalesLogState(
        filteredSalesLogs: filteredLogs,
        displayItems: _createDisplayItems(filteredLogs),
      ));
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in _performSearch',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
    } finally {
      LoggerUtils.methodEnd('_performSearch', tag: _tag);
    }
  }

  /// 판매 기록이 검색어와 매치되는지 확인
  bool _matchesSearchQuery(SalesLog salesLog, String normalizedQuery) {
    // 상품명으로 검색
    final productName = salesLog.productName;
    if (productName.toLowerCase().contains(normalizedQuery)) {
      return true;
    }

    // 판매자명으로 검색
    final sellerName = salesLog.sellerName;
    if (sellerName != null && sellerName.toLowerCase().contains(normalizedQuery)) {
      return true;
    }

    // 거래타입으로 검색
    if (salesLog.transactionType.displayName.toLowerCase().contains(normalizedQuery)) {
      return true;
    }

    // 가격으로 검색 (숫자 문자열 매칭)
    if (salesLog.totalAmount.toString().contains(normalizedQuery)) {
      return true;
    }

    return false;
  }

  /// 필터링 기능
  void filterSalesLogs({
    required List<SalesLog> salesLogs,
    String? sellerName,
    TransactionType? transactionType,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    LoggerUtils.logInfo('Filter sales logs', tag: _tag);

    final filters = <String, dynamic>{
      if (sellerName != null) 'sellerName': sellerName,
      if (transactionType != null) 'transactionType': transactionType,
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
    };

    _performFilter(filters, salesLogs);
  }

  /// 실제 필터링 수행
  void _performFilter(
    Map<String, dynamic> filters,
    List<SalesLog> salesLogs,
  ) {
    LoggerUtils.methodStart('_performFilter', tag: _tag);

    try {
      final filteredLogs = salesLogs.where(
        (salesLog) => _matchesFilters(salesLog, filters),
      ).toList();

      _updateState(SalesLogState(
        filteredSalesLogs: filteredLogs,
        displayItems: _createDisplayItems(filteredLogs),
        selectedSellerFilter: filters['sellerName'] ?? '',
        selectedTypeFilter: filters['transactionType'],
      ));
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in _performFilter',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
    } finally {
      LoggerUtils.methodEnd('_performFilter', tag: _tag);
    }
  }

  /// 판매 기록이 필터 조건과 매치되는지 확인
  bool _matchesFilters(SalesLog salesLog, Map<String, dynamic> filters) {
    // 판매자 필터
    if (filters.containsKey('sellerName')) {
      final sellerName = filters['sellerName'] as String;
      if (sellerName.isNotEmpty && salesLog.sellerName != sellerName) {
        return false;
      }
    }

    // 거래타입 필터
    if (filters.containsKey('transactionType')) {
      final transactionType = filters['transactionType'] as TransactionType;
      if (salesLog.transactionType != transactionType) {
        return false;
      }
    }

    // 날짜 범위 필터
    if (filters.containsKey('startDate') || filters.containsKey('endDate')) {
      final saleDate = DateTime.fromMillisecondsSinceEpoch(
        salesLog.saleTimestamp,
      );

      if (filters.containsKey('startDate')) {
        final startDate = filters['startDate'] as DateTime;
        if (saleDate.isBefore(startDate)) {
          return false;
        }
      }

      if (filters.containsKey('endDate')) {
        final endDate = filters['endDate'] as DateTime;
        if (saleDate.isAfter(endDate)) {
          return false;
        }
      }
    }

    return true;
  }

  /// 정렬 기능
  void sortSalesLogs(String sortBy, bool ascending, List<SalesLog> salesLogs) {
    LoggerUtils.logInfo(
      'Sort sales logs by $sortBy (ascending: $ascending)',
      tag: _tag,
    );

    _performSort(sortBy, ascending, salesLogs);
  }

  /// 실제 정렬 수행
  void _performSort(
    String sortBy,
    bool ascending,
    List<SalesLog> salesLogs,
  ) {
    LoggerUtils.methodStart('_performSort', tag: _tag);

    try {
      final sortedLogs = List<SalesLog>.from(salesLogs)
        ..sort((a, b) => _compareSalesLogs(a, b, sortBy, ascending));

      _updateState(SalesLogState(
        filteredSalesLogs: sortedLogs,
        displayItems: _createDisplayItems(sortedLogs),
      ));
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in _performSort',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
    } finally {
      LoggerUtils.methodEnd('_performSort', tag: _tag);
    }
  }

  /// 판매 기록 비교 함수
  int _compareSalesLogs(SalesLog a, SalesLog b, String sortBy, bool ascending) {
    int result = 0;

    switch (sortBy.toLowerCase()) {
      case 'date':
      case 'timestamp':
        result = a.saleTimestamp.compareTo(b.saleTimestamp);
        break;
      case 'product':
      case 'productname':
        result = a.productName.compareTo(b.productName);
        break;
      case 'seller':
      case 'sellername':
        result = (a.sellerName ?? '').compareTo(b.sellerName ?? '');
        break;
      case 'price':
      case 'totalprice':
      case 'totalamount':
        result = a.totalAmount.compareTo(b.totalAmount);
        break;
      case 'quantity':
      case 'soldquantity':
        result = a.soldQuantity.compareTo(b.soldQuantity);
        break;
      case 'type':
      case 'transactiontype':
        result = a.transactionType.index.compareTo(b.transactionType.index);
        break;
      default:
        result = a.saleTimestamp.compareTo(b.saleTimestamp);
    }

    return ascending ? result : -result;
  }

  /// 필터 초기화
  void clearFilters(List<SalesLog> salesLogs) {
    LoggerUtils.methodStart('clearFilters', tag: _tag);

    final filteredSalesLogs = List<SalesLog>.from(salesLogs);
    _updateState(SalesLogState(
      selectedTypeFilter: null,
      selectedSellerFilter: '',
      filteredSalesLogs: filteredSalesLogs,
      displayItems: _createDisplayItems(filteredSalesLogs),
    ));

    LoggerUtils.methodEnd('clearFilters', tag: _tag);
  }

  /// 거래 타입 필터 설정
  void setTypeFilter(TransactionType? transactionType, List<SalesLog> salesLogs) {
    LoggerUtils.methodStart('setTypeFilter', tag: _tag);

    final filteredSalesLogs = List<SalesLog>.from(salesLogs);
    _updateState(SalesLogState(
      selectedTypeFilter: transactionType,
      filteredSalesLogs: filteredSalesLogs,
      displayItems: _createDisplayItems(filteredSalesLogs),
    ));

    LoggerUtils.methodEnd('setTypeFilter', tag: _tag);
  }

  /// 판매자 필터 설정
  void setSellerFilter(String sellerName, List<SalesLog> salesLogs) {
    LoggerUtils.methodStart('setSellerFilter', tag: _tag);

    final filteredSalesLogs = List<SalesLog>.from(salesLogs);
    _updateState(SalesLogState(
      selectedSellerFilter: sellerName,
      filteredSalesLogs: filteredSalesLogs,
      displayItems: _createDisplayItems(filteredSalesLogs),
    ));

    LoggerUtils.methodEnd('setSellerFilter', tag: _tag);
  }
} 
