import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../models/set_discount.dart';
import '../repositories/set_discount_repository.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';
import 'unified_workspace_provider.dart';


part 'set_discount_provider.freezed.dart';

/// 세트 할인 상태를 관리하는 State 클래스
@freezed
abstract class SetDiscountState with _$SetDiscountState {
  const factory SetDiscountState({
    @Default([]) List<SetDiscount> setDiscounts,
    @Default([]) List<SetDiscount> filteredSetDiscounts,
    @Default([]) List<SetDiscount> activeSetDiscounts, // POS에서 사용할 활성 세트 할인 목록
    @Default(false) bool isLoading,
    @Default(false) bool isUpdating,
    @Default(false) bool isLoadingActive, // 활성 세트 할인 로딩 상태
    String? errorMessage,
    @Default('') String searchQuery,
    @Default(true) bool showActiveOnly,
  }) = _SetDiscountState;
}

/// 세트 할인 Repository Provider
final setDiscountRepositoryProvider = Provider<SetDiscountRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SetDiscountRepository(database: databaseService);
});

/// 세트 할인 상태를 관리하는 StateNotifier
class SetDiscountNotifier extends StateNotifier<SetDiscountState> {
  final Ref ref;

  SetDiscountNotifier(this.ref) : super(const SetDiscountState());

  /// 세트 할인 목록 로드
  Future<void> loadSetDiscounts({bool showLoading = true}) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    final currentEventId = ref.read(currentWorkspaceIdProvider);

    if (currentEventId == null) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: '현재 행사 워크스페이스가 선택되지 않았습니다.',
      );
      return;
    }
    
    if (showLoading) {
      state = state.copyWith(isLoading: true, errorMessage: null);
    }

    try {
      var setDiscounts = await repository.getAllSetDiscounts(eventId: currentEventId);

      // 로컬 전용 모드: 서버 다운로드 불필요
      if (setDiscounts.isEmpty && showLoading) {
        LoggerUtils.logInfo('로컬 전용 모드: 세트 할인 서버 다운로드 건너뜀', tag: 'SetDiscountProvider');
      }

      state = state.copyWith(
        setDiscounts: setDiscounts,
        isLoading: false,
        errorMessage: null,
      );
      _applyFilters();

      LoggerUtils.logInfo('Loaded ${setDiscounts.length} set discounts', tag: 'SetDiscountProvider');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      LoggerUtils.logError('Failed to load set discounts', error: e, tag: 'SetDiscountProvider');
    }
  }

  /// 활성화된 세트 할인만 로드 (내부용)
  Future<List<SetDiscount>> _getActiveSetDiscounts() async {
    final repository = ref.read(setDiscountRepositoryProvider);
    final currentEventId = ref.read(currentWorkspaceIdProvider);

    if (currentEventId == null) {
      LoggerUtils.logError('현재 워크스페이스가 선택되지 않음', tag: 'SetDiscountProvider');
      return [];
    }

    try {
      var activeSetDiscounts = await repository.getActiveSetDiscounts(eventId: currentEventId);

      // 로컬 전용 모드: 서버 다운로드 불필요
      if (activeSetDiscounts.isEmpty) {
        LoggerUtils.logInfo('로컬 전용 모드: 활성 세트 할인 서버 다운로드 건너뜀', tag: 'SetDiscountProvider');
      }

      return activeSetDiscounts;
    } catch (e) {
      LoggerUtils.logError('Failed to get active set discounts', error: e, tag: 'SetDiscountProvider');
      return [];
    }
  }

  /// 활성화된 세트 할인 로드 및 상태 업데이트
  Future<void> loadActiveSetDiscounts() async {
    state = state.copyWith(isLoadingActive: true);
    try {
      final activeDiscounts = await _getActiveSetDiscounts();
      state = state.copyWith(
        activeSetDiscounts: activeDiscounts,
        isLoadingActive: false,
      );
      LoggerUtils.logInfo('활성 세트 할인 로드 완료: ${activeDiscounts.length}개', tag: 'SetDiscountProvider');
    } catch (e) {
      state = state.copyWith(
        isLoadingActive: false,
        errorMessage: '활성 세트 할인 로드 실패',
      );
      LoggerUtils.logError('Failed to load active set discounts', error: e, tag: 'SetDiscountProvider');
    }
  }

  /// 세트 할인 추가
  Future<bool> addSetDiscount(SetDiscount setDiscount) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final id = await repository.insertSetDiscount(setDiscount);

      if (id != null) {
        // ID가 추가된 세트 할인 객체 생성
        final addedSetDiscount = setDiscount.copyWith(id: id);

        // 로컬 전용 모드: 실시간 동기화 불필요
        LoggerUtils.logInfo('로컬 전용 모드: 세트 할인 서버 업로드 건너뜀: ${addedSetDiscount.name}', tag: 'SetDiscountProvider');

        await loadSetDiscounts(showLoading: false);
        // 활성 세트 할인도 함께 갱신
        await loadActiveSetDiscounts();
        state = state.copyWith(isUpdating: false);
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 추가에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 추가 중 오류가 발생했습니다.');
      LoggerUtils.logError('Failed to add set discount', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 세트 할인 수정
  Future<bool> updateSetDiscount(SetDiscount setDiscount) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final success = await repository.updateSetDiscount(setDiscount);
      if (success) {
        // 로컬 전용 모드: 실시간 동기화 불필요
        LoggerUtils.logInfo('로컬 전용 모드: 세트 할인 수정 서버 업로드 건너뜀: ${setDiscount.name}', tag: 'SetDiscountProvider');

        await loadSetDiscounts(showLoading: false);
        // 활성 세트 할인도 함께 갱신
        await loadActiveSetDiscounts();
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount updated successfully: ${setDiscount.id}', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 수정에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to update set discount', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 세트 할인 삭제
  Future<bool> deleteSetDiscount(int id) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      // 삭제 전 세트 할인 정보 가져오기 (실시간 동기화용)
      final setDiscountToDelete = await repository.getSetDiscountById(id);

      final success = await repository.deleteSetDiscount(id);
      if (success && setDiscountToDelete != null) {
        // 로컬 전용 모드: 실시간 동기화 불필요
        LoggerUtils.logInfo('로컬 전용 모드: 세트 할인 삭제 서버 동기화 건너뜀: ${setDiscountToDelete.name}', tag: 'SetDiscountProvider');

        await loadSetDiscounts(showLoading: false);
        // activeSetDiscountsProvider 무효화하여 새로운 데이터 로드
        ref.invalidate(activeSetDiscountsProvider);
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount deleted successfully: $id', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 삭제에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to delete set discount', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 세트 할인 활성화/비활성화
  Future<bool> toggleSetDiscountActive(int id, bool isActive) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final success = await repository.toggleSetDiscountActive(id, isActive);
      if (success) {
        await loadSetDiscounts(showLoading: false);
        // 활성 세트 할인도 함께 갱신
        await loadActiveSetDiscounts();
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount active status changed: $id -> $isActive', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 상태 변경에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to toggle set discount active', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 검색어 설정
  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _applyFilters();
  }

  /// 활성화 필터 토글
  void toggleShowActiveOnly() {
    state = state.copyWith(showActiveOnly: !state.showActiveOnly);
    _applyFilters();
  }

  /// 필터 적용
  void _applyFilters() {
    var filtered = state.setDiscounts;

    // 활성화 필터
    if (state.showActiveOnly) {
      filtered = filtered.where((discount) => discount.isActive).toList();
    }

    // 검색 필터
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((discount) =>
        discount.name.toLowerCase().contains(query)
      ).toList();
    }

    state = state.copyWith(filteredSetDiscounts: filtered);
  }

  /// 세트 할인 이름 중복 확인
  Future<bool> isNameExists(String name, {int? excludeId}) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    final currentEventId = ref.read(currentWorkspaceIdProvider);

    if (currentEventId == null) {
      LoggerUtils.logError('현재 워크스페이스가 선택되지 않음', tag: 'SetDiscountProvider');
      return false;
    }

    try {
      return await repository.isNameExists(name, eventId: currentEventId, excludeId: excludeId);
    } catch (e) {
      LoggerUtils.logError('Failed to check name exists', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 에러 메시지 클리어
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

/// 세트 할인 Provider
final setDiscountNotifierProvider = StateNotifierProvider<SetDiscountNotifier, SetDiscountState>((ref) {
  return SetDiscountNotifier(ref);
});

/// 필터링된 세트 할인 목록 Provider
final filteredSetDiscountsProvider = Provider<List<SetDiscount>>((ref) {
  return ref.watch(setDiscountNotifierProvider).filteredSetDiscounts;
});

/// 활성화된 세트 할인 목록 Provider (판매 시 사용)
final activeSetDiscountsProvider = Provider<List<SetDiscount>>((ref) {
  return ref.watch(setDiscountNotifierProvider).activeSetDiscounts;
});

/// 활성화된 세트 할인 로딩 상태 Provider
final activeSetDiscountsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(setDiscountNotifierProvider).isLoadingActive;
});

/// 로딩 상태 Provider
final setDiscountLoadingProvider = Provider<bool>((ref) {
  return ref.watch(setDiscountNotifierProvider).isLoading;
});

/// 업데이트 상태 Provider
final setDiscountUpdatingProvider = Provider<bool>((ref) {
  return ref.watch(setDiscountNotifierProvider).isUpdating;
});

/// 에러 메시지 Provider
final setDiscountErrorProvider = Provider<String?>((ref) {
  return ref.watch(setDiscountNotifierProvider).errorMessage;
});
