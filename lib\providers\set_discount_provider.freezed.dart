// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_discount_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SetDiscountState {

 List<SetDiscount> get setDiscounts; List<SetDiscount> get filteredSetDiscounts; List<SetDiscount> get activeSetDiscounts;// POS에서 사용할 활성 세트 할인 목록
 bool get isLoading; bool get isUpdating; bool get isLoadingActive;// 활성 세트 할인 로딩 상태
 String? get errorMessage; String get searchQuery; bool get showActiveOnly;
/// Create a copy of SetDiscountState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SetDiscountStateCopyWith<SetDiscountState> get copyWith => _$SetDiscountStateCopyWithImpl<SetDiscountState>(this as SetDiscountState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SetDiscountState&&const DeepCollectionEquality().equals(other.setDiscounts, setDiscounts)&&const DeepCollectionEquality().equals(other.filteredSetDiscounts, filteredSetDiscounts)&&const DeepCollectionEquality().equals(other.activeSetDiscounts, activeSetDiscounts)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isUpdating, isUpdating) || other.isUpdating == isUpdating)&&(identical(other.isLoadingActive, isLoadingActive) || other.isLoadingActive == isLoadingActive)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.showActiveOnly, showActiveOnly) || other.showActiveOnly == showActiveOnly));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(setDiscounts),const DeepCollectionEquality().hash(filteredSetDiscounts),const DeepCollectionEquality().hash(activeSetDiscounts),isLoading,isUpdating,isLoadingActive,errorMessage,searchQuery,showActiveOnly);

@override
String toString() {
  return 'SetDiscountState(setDiscounts: $setDiscounts, filteredSetDiscounts: $filteredSetDiscounts, activeSetDiscounts: $activeSetDiscounts, isLoading: $isLoading, isUpdating: $isUpdating, isLoadingActive: $isLoadingActive, errorMessage: $errorMessage, searchQuery: $searchQuery, showActiveOnly: $showActiveOnly)';
}


}

/// @nodoc
abstract mixin class $SetDiscountStateCopyWith<$Res>  {
  factory $SetDiscountStateCopyWith(SetDiscountState value, $Res Function(SetDiscountState) _then) = _$SetDiscountStateCopyWithImpl;
@useResult
$Res call({
 List<SetDiscount> setDiscounts, List<SetDiscount> filteredSetDiscounts, List<SetDiscount> activeSetDiscounts, bool isLoading, bool isUpdating, bool isLoadingActive, String? errorMessage, String searchQuery, bool showActiveOnly
});




}
/// @nodoc
class _$SetDiscountStateCopyWithImpl<$Res>
    implements $SetDiscountStateCopyWith<$Res> {
  _$SetDiscountStateCopyWithImpl(this._self, this._then);

  final SetDiscountState _self;
  final $Res Function(SetDiscountState) _then;

/// Create a copy of SetDiscountState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? setDiscounts = null,Object? filteredSetDiscounts = null,Object? activeSetDiscounts = null,Object? isLoading = null,Object? isUpdating = null,Object? isLoadingActive = null,Object? errorMessage = freezed,Object? searchQuery = null,Object? showActiveOnly = null,}) {
  return _then(_self.copyWith(
setDiscounts: null == setDiscounts ? _self.setDiscounts : setDiscounts // ignore: cast_nullable_to_non_nullable
as List<SetDiscount>,filteredSetDiscounts: null == filteredSetDiscounts ? _self.filteredSetDiscounts : filteredSetDiscounts // ignore: cast_nullable_to_non_nullable
as List<SetDiscount>,activeSetDiscounts: null == activeSetDiscounts ? _self.activeSetDiscounts : activeSetDiscounts // ignore: cast_nullable_to_non_nullable
as List<SetDiscount>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isUpdating: null == isUpdating ? _self.isUpdating : isUpdating // ignore: cast_nullable_to_non_nullable
as bool,isLoadingActive: null == isLoadingActive ? _self.isLoadingActive : isLoadingActive // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,searchQuery: null == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String,showActiveOnly: null == showActiveOnly ? _self.showActiveOnly : showActiveOnly // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SetDiscountState].
extension SetDiscountStatePatterns on SetDiscountState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SetDiscountState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SetDiscountState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SetDiscountState value)  $default,){
final _that = this;
switch (_that) {
case _SetDiscountState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SetDiscountState value)?  $default,){
final _that = this;
switch (_that) {
case _SetDiscountState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<SetDiscount> setDiscounts,  List<SetDiscount> filteredSetDiscounts,  List<SetDiscount> activeSetDiscounts,  bool isLoading,  bool isUpdating,  bool isLoadingActive,  String? errorMessage,  String searchQuery,  bool showActiveOnly)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SetDiscountState() when $default != null:
return $default(_that.setDiscounts,_that.filteredSetDiscounts,_that.activeSetDiscounts,_that.isLoading,_that.isUpdating,_that.isLoadingActive,_that.errorMessage,_that.searchQuery,_that.showActiveOnly);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<SetDiscount> setDiscounts,  List<SetDiscount> filteredSetDiscounts,  List<SetDiscount> activeSetDiscounts,  bool isLoading,  bool isUpdating,  bool isLoadingActive,  String? errorMessage,  String searchQuery,  bool showActiveOnly)  $default,) {final _that = this;
switch (_that) {
case _SetDiscountState():
return $default(_that.setDiscounts,_that.filteredSetDiscounts,_that.activeSetDiscounts,_that.isLoading,_that.isUpdating,_that.isLoadingActive,_that.errorMessage,_that.searchQuery,_that.showActiveOnly);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<SetDiscount> setDiscounts,  List<SetDiscount> filteredSetDiscounts,  List<SetDiscount> activeSetDiscounts,  bool isLoading,  bool isUpdating,  bool isLoadingActive,  String? errorMessage,  String searchQuery,  bool showActiveOnly)?  $default,) {final _that = this;
switch (_that) {
case _SetDiscountState() when $default != null:
return $default(_that.setDiscounts,_that.filteredSetDiscounts,_that.activeSetDiscounts,_that.isLoading,_that.isUpdating,_that.isLoadingActive,_that.errorMessage,_that.searchQuery,_that.showActiveOnly);case _:
  return null;

}
}

}

/// @nodoc


class _SetDiscountState implements SetDiscountState {
  const _SetDiscountState({final  List<SetDiscount> setDiscounts = const [], final  List<SetDiscount> filteredSetDiscounts = const [], final  List<SetDiscount> activeSetDiscounts = const [], this.isLoading = false, this.isUpdating = false, this.isLoadingActive = false, this.errorMessage, this.searchQuery = '', this.showActiveOnly = true}): _setDiscounts = setDiscounts,_filteredSetDiscounts = filteredSetDiscounts,_activeSetDiscounts = activeSetDiscounts;
  

 final  List<SetDiscount> _setDiscounts;
@override@JsonKey() List<SetDiscount> get setDiscounts {
  if (_setDiscounts is EqualUnmodifiableListView) return _setDiscounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_setDiscounts);
}

 final  List<SetDiscount> _filteredSetDiscounts;
@override@JsonKey() List<SetDiscount> get filteredSetDiscounts {
  if (_filteredSetDiscounts is EqualUnmodifiableListView) return _filteredSetDiscounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_filteredSetDiscounts);
}

 final  List<SetDiscount> _activeSetDiscounts;
@override@JsonKey() List<SetDiscount> get activeSetDiscounts {
  if (_activeSetDiscounts is EqualUnmodifiableListView) return _activeSetDiscounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_activeSetDiscounts);
}

// POS에서 사용할 활성 세트 할인 목록
@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isUpdating;
@override@JsonKey() final  bool isLoadingActive;
// 활성 세트 할인 로딩 상태
@override final  String? errorMessage;
@override@JsonKey() final  String searchQuery;
@override@JsonKey() final  bool showActiveOnly;

/// Create a copy of SetDiscountState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetDiscountStateCopyWith<_SetDiscountState> get copyWith => __$SetDiscountStateCopyWithImpl<_SetDiscountState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetDiscountState&&const DeepCollectionEquality().equals(other._setDiscounts, _setDiscounts)&&const DeepCollectionEquality().equals(other._filteredSetDiscounts, _filteredSetDiscounts)&&const DeepCollectionEquality().equals(other._activeSetDiscounts, _activeSetDiscounts)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isUpdating, isUpdating) || other.isUpdating == isUpdating)&&(identical(other.isLoadingActive, isLoadingActive) || other.isLoadingActive == isLoadingActive)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.searchQuery, searchQuery) || other.searchQuery == searchQuery)&&(identical(other.showActiveOnly, showActiveOnly) || other.showActiveOnly == showActiveOnly));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_setDiscounts),const DeepCollectionEquality().hash(_filteredSetDiscounts),const DeepCollectionEquality().hash(_activeSetDiscounts),isLoading,isUpdating,isLoadingActive,errorMessage,searchQuery,showActiveOnly);

@override
String toString() {
  return 'SetDiscountState(setDiscounts: $setDiscounts, filteredSetDiscounts: $filteredSetDiscounts, activeSetDiscounts: $activeSetDiscounts, isLoading: $isLoading, isUpdating: $isUpdating, isLoadingActive: $isLoadingActive, errorMessage: $errorMessage, searchQuery: $searchQuery, showActiveOnly: $showActiveOnly)';
}


}

/// @nodoc
abstract mixin class _$SetDiscountStateCopyWith<$Res> implements $SetDiscountStateCopyWith<$Res> {
  factory _$SetDiscountStateCopyWith(_SetDiscountState value, $Res Function(_SetDiscountState) _then) = __$SetDiscountStateCopyWithImpl;
@override @useResult
$Res call({
 List<SetDiscount> setDiscounts, List<SetDiscount> filteredSetDiscounts, List<SetDiscount> activeSetDiscounts, bool isLoading, bool isUpdating, bool isLoadingActive, String? errorMessage, String searchQuery, bool showActiveOnly
});




}
/// @nodoc
class __$SetDiscountStateCopyWithImpl<$Res>
    implements _$SetDiscountStateCopyWith<$Res> {
  __$SetDiscountStateCopyWithImpl(this._self, this._then);

  final _SetDiscountState _self;
  final $Res Function(_SetDiscountState) _then;

/// Create a copy of SetDiscountState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? setDiscounts = null,Object? filteredSetDiscounts = null,Object? activeSetDiscounts = null,Object? isLoading = null,Object? isUpdating = null,Object? isLoadingActive = null,Object? errorMessage = freezed,Object? searchQuery = null,Object? showActiveOnly = null,}) {
  return _then(_SetDiscountState(
setDiscounts: null == setDiscounts ? _self._setDiscounts : setDiscounts // ignore: cast_nullable_to_non_nullable
as List<SetDiscount>,filteredSetDiscounts: null == filteredSetDiscounts ? _self._filteredSetDiscounts : filteredSetDiscounts // ignore: cast_nullable_to_non_nullable
as List<SetDiscount>,activeSetDiscounts: null == activeSetDiscounts ? _self._activeSetDiscounts : activeSetDiscounts // ignore: cast_nullable_to_non_nullable
as List<SetDiscount>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isUpdating: null == isUpdating ? _self.isUpdating : isUpdating // ignore: cast_nullable_to_non_nullable
as bool,isLoadingActive: null == isLoadingActive ? _self.isLoadingActive : isLoadingActive // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,searchQuery: null == searchQuery ? _self.searchQuery : searchQuery // ignore: cast_nullable_to_non_nullable
as String,showActiveOnly: null == showActiveOnly ? _self.showActiveOnly : showActiveOnly // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
