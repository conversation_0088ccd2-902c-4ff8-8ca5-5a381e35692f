/// 바라 부스 매니저 - 구독 상태 관리 Provider
///
/// 사용자의 구독 상태를 관리하고 기능 제한을 처리하는 Provider입니다.
/// - 구독 플랜 상태 관리
/// - 기능 제한 확인
/// - 실시간 구독 상태 업데이트
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/subscription_plan.dart';
import '../services/subscription_service.dart';
import '../utils/logger_utils.dart';
// 로컬 전용 모드: 실시간 동기화 관련 import 제거됨

/// 구독 서비스 Provider
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  return SubscriptionService();
});

/// 현재 구독 정보 Provider
final currentSubscriptionProvider = FutureProvider<UserSubscription?>((ref) async {
  final service = ref.read(subscriptionServiceProvider);
  return await service.getCurrentSubscription();
});

/// 현재 구독 플랜 타입 Provider (서버 우선 - 관리자 변경사항 즉시 반영)
final currentPlanTypeProvider = FutureProvider<SubscriptionPlanType>((ref) async {
  final service = ref.read(subscriptionServiceProvider);

  // 구독 정보는 항상 서버 우선 (관리자 대시보드 변경사항 즉시 반영)
  return await service.getCurrentPlanType();
});

/// 현재 구독 플랜 정보 Provider
final currentPlanProvider = FutureProvider<SubscriptionPlan>((ref) async {
  final service = ref.read(subscriptionServiceProvider);
  return await service.getCurrentPlan();
});

/// 실시간 구독 상태 Provider (읽기 사용량 최적화를 위해 비활성화)
/// 필요할 때만 currentSubscriptionProvider를 사용하도록 변경
// final subscriptionStreamProvider = StreamProvider<UserSubscription?>((ref) {
//   final service = ref.read(subscriptionServiceProvider);
//   return service.watchSubscription();
// });

/// 플러스 플랜 사용자 여부 Provider
final isPlusUserProvider = FutureProvider<bool>((ref) async {
  final planType = await ref.read(currentPlanTypeProvider.future);
  return planType == SubscriptionPlanType.plus;
});

/// 무료 플랜 사용자 여부 Provider
final isFreeUserProvider = FutureProvider<bool>((ref) async {
  final planType = await ref.read(currentPlanTypeProvider.future);
  return planType == SubscriptionPlanType.free;
});

/// 특정 기능 사용 가능 여부 Provider
final featureAccessProvider = FutureProvider.family<bool, String>((ref, featureName) async {
  final service = ref.read(subscriptionServiceProvider);
  return await service.hasFeature(featureName);
});

/// 세트 할인 기능 사용 가능 여부 Provider
final hasSetDiscountFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('setDiscount').future);
});

/// 서비스 기능 사용 가능 여부 Provider
final hasServiceFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('service').future);
});

/// 서버 연동 기능 사용 가능 여부 Provider
final hasServerSyncFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('serverSync').future);
});

/// 판매자별 관리 기능 사용 가능 여부 Provider
final hasSellerManagementFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('sellerManagement').future);
});

/// 엑셀 내보내기 기능 사용 가능 여부 Provider
final hasExcelExportFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('excelExport').future);
});

/// PDF 내보내기 기능 사용 가능 여부 Provider
final hasPdfExportFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('pdfExport').future);
});

/// PDF/엑셀 내보내기 기능 사용 가능 여부 Provider (기존 호환성)
final hasExportFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('export').future);
});

/// 고급 통계 기능 사용 가능 여부 Provider
final hasAdvancedStatsFeatureProvider = FutureProvider<bool>((ref) async {
  return await ref.read(featureAccessProvider('advancedStats').future);
});

/// 행사 등록 가능 여부 Provider
final canCreateEventProvider = FutureProvider.family<bool, int>((ref, currentEventCount) async {
  final service = ref.read(subscriptionServiceProvider);
  return await service.canCreateEvent(currentEventCount);
});

/// 상품 등록 가능 여부 Provider
final canCreateProductProvider = FutureProvider.family<bool, int>((ref, currentProductCount) async {
  final service = ref.read(subscriptionServiceProvider);
  return await service.canCreateProduct(currentProductCount);
});

/// 구독 관리 Notifier
class SubscriptionNotifier extends StateNotifier<AsyncValue<SubscriptionPlanType>> {
  static const String _tag = 'SubscriptionNotifier';
  
  final SubscriptionService _service;
  final Ref _ref;

  SubscriptionNotifier(this._service, this._ref) : super(const AsyncValue.loading()) {
    _loadCurrentPlan();
  }

  /// 현재 구독 플랜 로드
  Future<void> _loadCurrentPlan() async {
    try {
      state = const AsyncValue.loading();
      final planType = await _service.getCurrentPlanType();
      state = AsyncValue.data(planType);
      LoggerUtils.logInfo('구독 플랜 로드 완료: $planType', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('구독 플랜 로드 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// 구독 플랜 변경
  Future<void> updatePlan(SubscriptionPlanType planType) async {
    try {
      state = const AsyncValue.loading();
      await _service.updateSubscriptionPlan(planType);
      state = AsyncValue.data(planType);

      // 관련 Provider들 무효화하여 새로고침
      _ref.invalidate(currentSubscriptionProvider);
      _ref.invalidate(currentPlanTypeProvider);
      _ref.invalidate(currentPlanProvider);
      _ref.invalidate(isPlusUserProvider);
      _ref.invalidate(isFreeUserProvider);

      LoggerUtils.logInfo('구독 플랜 변경 완료: $planType', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('구독 플랜 변경 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// 구독 상태 새로고침
  Future<void> refresh() async {
    await _loadCurrentPlan();
  }

  /// 구독 만료 확인 및 처리
  Future<void> checkExpiration() async {
    try {
      await _service.checkAndHandleExpiredSubscription();
      await _loadCurrentPlan(); // 상태 새로고침
    } catch (e) {
      LoggerUtils.logError('구독 만료 확인 실패', tag: _tag, error: e);
    }
  }
}

/// 구독 관리 Notifier Provider
final subscriptionNotifierProvider = StateNotifierProvider<SubscriptionNotifier, AsyncValue<SubscriptionPlanType>>((ref) {
  final service = ref.read(subscriptionServiceProvider);
  return SubscriptionNotifier(service, ref);
});

/// 구독 플랜 토글 Provider (임시 개발용)
final subscriptionToggleProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    try {
      final notifier = ref.read(subscriptionNotifierProvider.notifier);
      final currentState = ref.read(subscriptionNotifierProvider);

      if (currentState is AsyncData<SubscriptionPlanType>) {
        final currentPlan = currentState.value;
        SubscriptionPlanType newPlan;

        // Free -> Plus -> Free 순환
        switch (currentPlan) {
          case SubscriptionPlanType.free:
            newPlan = SubscriptionPlanType.plus;
            break;
          case SubscriptionPlanType.plus:
            newPlan = SubscriptionPlanType.free;
            break;
        }

        await notifier.updatePlan(newPlan);
        LoggerUtils.logInfo('구독 플랜 토글 완료: $currentPlan -> $newPlan', tag: 'SubscriptionToggle');
      }
    } catch (e) {
      LoggerUtils.logError('구독 플랜 토글 실패', tag: 'SubscriptionToggle', error: e);
      rethrow;
    }
  };
});

/// 구독 상태 요약 정보 Provider
final subscriptionSummaryProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  try {
    final subscription = await ref.read(currentSubscriptionProvider.future);
    final plan = await ref.read(currentPlanProvider.future);
    
    if (subscription == null) {
      return {
        'planType': SubscriptionPlanType.free,
        'planName': PredefinedPlans.free.name,
        'isValid': false,
        'daysUntilExpiry': null,
        'features': <String, bool>{},
      };
    }
    
    return {
      'planType': subscription.planType,
      'planName': plan.name,
      'isValid': subscription.isValid,
      'daysUntilExpiry': subscription.daysUntilExpiry,
      'features': {
        'setDiscount': plan.hasSetDiscountFeature,
        'service': plan.hasServiceFeature,
        'serverSync': plan.hasServerSyncFeature,
        'sellerManagement': plan.hasSellerManagementFeature,
        'excelExport': plan.hasExcelExportFeature,
        'pdfExport': plan.hasPdfExportFeature,
        'export': plan.hasExcelExportFeature || plan.hasPdfExportFeature, // 기존 호환성
        'advancedStats': plan.hasAdvancedStatsFeature,
        'googleDriveBackup': plan.hasGoogleDriveBackupFeature,
      },
    };
  } catch (e) {
    LoggerUtils.logError('구독 상태 요약 정보 조회 실패', tag: 'SubscriptionSummary', error: e);
    return {
      'planType': SubscriptionPlanType.free,
      'planName': PredefinedPlans.free.name,
      'isValid': false,
      'daysUntilExpiry': null,
      'features': <String, bool>{},
    };
  }
});
