// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'category_repository.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CategoryRepositoryState {

 List<Category> get categories; bool get isLoading; String? get error;
/// Create a copy of CategoryRepositoryState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryRepositoryStateCopyWith<CategoryRepositoryState> get copyWith => _$CategoryRepositoryStateCopyWithImpl<CategoryRepositoryState>(this as CategoryRepositoryState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryRepositoryState&&const DeepCollectionEquality().equals(other.categories, categories)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(categories),isLoading,error);

@override
String toString() {
  return 'CategoryRepositoryState(categories: $categories, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class $CategoryRepositoryStateCopyWith<$Res>  {
  factory $CategoryRepositoryStateCopyWith(CategoryRepositoryState value, $Res Function(CategoryRepositoryState) _then) = _$CategoryRepositoryStateCopyWithImpl;
@useResult
$Res call({
 List<Category> categories, bool isLoading, String? error
});




}
/// @nodoc
class _$CategoryRepositoryStateCopyWithImpl<$Res>
    implements $CategoryRepositoryStateCopyWith<$Res> {
  _$CategoryRepositoryStateCopyWithImpl(this._self, this._then);

  final CategoryRepositoryState _self;
  final $Res Function(CategoryRepositoryState) _then;

/// Create a copy of CategoryRepositoryState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categories = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as List<Category>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CategoryRepositoryState].
extension CategoryRepositoryStatePatterns on CategoryRepositoryState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryRepositoryState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryRepositoryState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryRepositoryState value)  $default,){
final _that = this;
switch (_that) {
case _CategoryRepositoryState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryRepositoryState value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryRepositoryState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<Category> categories,  bool isLoading,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryRepositoryState() when $default != null:
return $default(_that.categories,_that.isLoading,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<Category> categories,  bool isLoading,  String? error)  $default,) {final _that = this;
switch (_that) {
case _CategoryRepositoryState():
return $default(_that.categories,_that.isLoading,_that.error);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<Category> categories,  bool isLoading,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _CategoryRepositoryState() when $default != null:
return $default(_that.categories,_that.isLoading,_that.error);case _:
  return null;

}
}

}

/// @nodoc


class _CategoryRepositoryState implements CategoryRepositoryState {
  const _CategoryRepositoryState({final  List<Category> categories = const [], this.isLoading = false, this.error}): _categories = categories;
  

 final  List<Category> _categories;
@override@JsonKey() List<Category> get categories {
  if (_categories is EqualUnmodifiableListView) return _categories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_categories);
}

@override@JsonKey() final  bool isLoading;
@override final  String? error;

/// Create a copy of CategoryRepositoryState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryRepositoryStateCopyWith<_CategoryRepositoryState> get copyWith => __$CategoryRepositoryStateCopyWithImpl<_CategoryRepositoryState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryRepositoryState&&const DeepCollectionEquality().equals(other._categories, _categories)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_categories),isLoading,error);

@override
String toString() {
  return 'CategoryRepositoryState(categories: $categories, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class _$CategoryRepositoryStateCopyWith<$Res> implements $CategoryRepositoryStateCopyWith<$Res> {
  factory _$CategoryRepositoryStateCopyWith(_CategoryRepositoryState value, $Res Function(_CategoryRepositoryState) _then) = __$CategoryRepositoryStateCopyWithImpl;
@override @useResult
$Res call({
 List<Category> categories, bool isLoading, String? error
});




}
/// @nodoc
class __$CategoryRepositoryStateCopyWithImpl<$Res>
    implements _$CategoryRepositoryStateCopyWith<$Res> {
  __$CategoryRepositoryStateCopyWithImpl(this._self, this._then);

  final _CategoryRepositoryState _self;
  final $Res Function(_CategoryRepositoryState) _then;

/// Create a copy of CategoryRepositoryState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categories = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_CategoryRepositoryState(
categories: null == categories ? _self._categories : categories // ignore: cast_nullable_to_non_nullable
as List<Category>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
