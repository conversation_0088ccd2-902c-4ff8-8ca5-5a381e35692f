import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/sale.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';


/// 판매 데이터베이스 접근을 위한 Repository Provider입니다.
final saleRepositoryProvider = Provider<SaleRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SaleRepository(database: databaseService);
});

/// 판매 데이터베이스 접근을 위한 Repository 클래스입니다.
class SaleRepository {
  final DatabaseService database;
  static const String _tag = 'SaleRepository';
  static const String salesTable = 'sales';

  SaleRepository({required this.database});

  Future<void> insertSale(Sale sale) async {
    LoggerUtils.methodStart('insertSale', tag: _tag);
    final db = await database.database;
    await db.insert(salesTable, sale.toMap());
    LoggerUtils.methodEnd('insertSale', tag: _tag);
  }

  Future<void> updateSale(Sale sale) async {
    LoggerUtils.methodStart('updateSale', tag: _tag);
    final db = await database.database;
    await db.update(
      salesTable,
      sale.toMap(),
      where: 'id = ?',
      whereArgs: [sale.id],
    );
    LoggerUtils.methodEnd('updateSale', tag: _tag);
  }

  /// [sale]: 삭제할 Sale 객체
  /// 반환값: 삭제된 row 개수
  /// 예외: DB 오류 등
  Future<int> deleteSale(Sale sale) async {
    final db = await database.database;
    if (sale.id == null) {
      throw Exception('판매 ID가 null입니다. 삭제할 수 없습니다.');
    }
    final deletedRows = await db.delete(
      salesTable,
      where: 'id = ?',
      whereArgs: [sale.id!],
    );
    LoggerUtils.logDebug('[삭제] 판매 id=${sale.id}, deletedRows=$deletedRows', tag: 'SaleRepository');
    return deletedRows;
  }

  Future<List<Sale>> getAllSales() async {
    LoggerUtils.methodStart('getAllSales', tag: _tag);
    final db = await database.database;
    final List<Map<String, dynamic>> maps = await db.query('sales');
    LoggerUtils.methodEnd('getAllSales', tag: _tag);
    return List.generate(maps.length, (i) => Sale.fromMap(maps[i]));
  }

  Future<Sale?> getSaleById(int id) async {
    LoggerUtils.methodStart('getSaleById', tag: _tag);
    final db = await database.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sales',
      where: 'id = ?',
      whereArgs: [id],
    );
    LoggerUtils.methodEnd('getSaleById', tag: _tag);
    if (maps.isEmpty) return null;
    return Sale.fromMap(maps.first);
  }
}
