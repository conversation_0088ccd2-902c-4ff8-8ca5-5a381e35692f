import 'package:sqflite/sqflite.dart';
import '../models/sales_log.dart';
import '../models/transaction_type.dart';
import '../services/database_service.dart';
import '../services/event_workspace_manager.dart';



/// SalesLog의 기본 CRUD 작업을 관리하는 클래스
class SalesLogCrud {
  final DatabaseService _databaseService;
  
  SalesLogCrud({required DatabaseService database})
    : _databaseService = database;

  Future<Database> get _database => _databaseService.database;

  // 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getAllSalesLogs() async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: currentEventId != null ? 'eventId = ?' : null,
      whereArgs: currentEventId != null ? [currentEventId] : null,
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 특정 행사의 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsByEventId(int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'eventId = ?',
      whereArgs: [eventId],
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 특정 타입의 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getAllSalesLogsByType(
    TransactionType transactionType,
  ) async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['transactionType = ?'];
    final whereArgs = <Object>[transactionType.value];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 판매자별 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsBySeller(String sellerName) async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['sellerName = ?'];
    final whereArgs = <Object>[sellerName];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 판매자별 특정 타입의 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsBySellerAndType(
    String sellerName,
    TransactionType transactionType,
  ) async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['sellerName = ? AND transactionType = ?'];
    final whereArgs = <Object>[sellerName, transactionType.value];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 모든 고유한 판매자 이름 목록 조회
  Future<List<String>> getAllDistinctSellerNames() async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['sellerName IS NOT NULL AND sellerName != ""'];
    final whereArgs = <Object>[];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      columns: ['DISTINCT sellerName'],
      where: whereParts.join(' AND '),
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'sellerName ASC',
    );

    return maps
        .map((map) => map['sellerName'] as String)
        .where((name) => name.isNotEmpty)
        .toList();
  }

  // 날짜 범위별 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['saleTimestamp BETWEEN ? AND ?'];
    final whereArgs = <Object>[startTimestamp, endTimestamp];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'saleTimestamp DESC',
    );

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 배치 ID별 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByBatchId(String batchId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'batchSaleId = ?',
      whereArgs: [batchId],
      orderBy: 'saleTimestamp DESC',
    );

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // ID로 판매 기록 조회
  Future<SalesLog?> getLogById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return SalesLog.fromMap(maps.first);
    }
    return null;
  }

  // 판매 기록 삽입
  Future<int> insertLog(SalesLog log) async {

    final db = await _database;
    final data = Map<String, dynamic>.from(log.toMap());
    try {
      return await db.insert(
        DatabaseServiceImpl.salesLogTable,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      final msg = e.toString();
      // 컬럼 미존재 호환 처리 (기존 DB에 paymentMethod 컬럼이 없을 수 있음)
      if (msg.contains('no such column: paymentMethod') || msg.contains('has no column named paymentMethod')) {
        data.remove('paymentMethod');
        return await db.insert(
          DatabaseServiceImpl.salesLogTable,
          data,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      rethrow;
    }
  }

  // 판매 기록 업데이트
  Future<int> updateLog(SalesLog log) async {
    final db = await _database;
    return await db.update(
      DatabaseServiceImpl.salesLogTable,
      log.toMap(),
      where: 'id = ?',
      whereArgs: [log.id],
    );
  }

  // 판매 기록 삭제
  Future<int> deleteLog(SalesLog log) async {
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'id = ?',
      whereArgs: [log.id],
    );
  }

  // 모든 판매 기록 삭제
  Future<int> deleteAllLogs() async {
    final db = await _database;
    return await db.delete(DatabaseServiceImpl.salesLogTable);
  }

  // 판매자별 판매 기록 삭제
  Future<int> deleteLogsBySeller(String sellerName) async {
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'sellerName = ?',
      whereArgs: [sellerName],
    );
  }

  // 날짜 범위별 판매 기록 삭제
  Future<int> deleteLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'saleTimestamp BETWEEN ? AND ?',
      whereArgs: [startTimestamp, endTimestamp],
    );
  }

  // 배치 ID별 판매 기록 삭제
  Future<int> deleteLogsByBatchId(String batchId) async {
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'batchSaleId = ?',
      whereArgs: [batchId],
    );
  }

  // 판매 기록 추가
  Future<SalesLog> addSalesLog(SalesLog salesLog) async {
    final id = await insertLog(salesLog);
    return salesLog.copyWith(id: id);
  }

  // 판매 기록 업데이트
  Future<int> updateSalesLog(SalesLog salesLog) async {
    return await updateLog(salesLog);
  }

  // 판매 기록 삭제
  Future<void> deleteSalesLog(int id) async {
    final db = await _database;
    await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
} 
