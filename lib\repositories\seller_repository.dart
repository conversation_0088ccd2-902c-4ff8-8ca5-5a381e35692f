import 'package:sqflite/sqflite.dart';
import '../models/seller.dart';
import '../services/database_service.dart';

import '../utils/batch_processor.dart';
import '../utils/logger_utils.dart';

/// 판매자 데이터의 CRUD, 검색, 중복/정렬/보안 등 관리 기능을 제공하는 Repository 클래스입니다.
/// - 로컬 DB(SQLite)와 연동하며, 오프라인 작업 큐/동기화 구조를 지원합니다.
/// - SQL injection 방지, 이름 중복/정렬/검색 등 다양한 필터링 지원
class SellerRepository {
  final DatabaseService _databaseService;
  late final Future<Database> _database;

  SellerRepository({required DatabaseService database})
    : _databaseService = database {
    _database = _databaseService.database;
  }

  /// 리소스 정리
  void dispose() {
    // Repository는 데이터베이스 연결을 소유하지 않으므로
    // 특별한 정리 작업은 필요하지 않음
    LoggerUtils.logDebug('SellerRepository disposed', tag: 'SellerRepository');
  }

  // 모든 판매자 목록 조회
  Future<List<Seller>> getAllSellers() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.sellersTable,
        orderBy: 'name ASC',
      );
      return maps.map((map) => Seller.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get all sellers', error: e, tag: 'SellerRepository');
      // 오류 발생 시 빈 리스트 반환
      return [];
    }
  }

  // 특정 행사의 판매자 목록 조회
  Future<List<Seller>> getSellersByEventId(int eventId) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.sellersTable,
        where: 'eventId = ?',
        whereArgs: [eventId],
        orderBy: 'sortOrder ASC, id ASC', // sortOrder 우선, 그 다음 id 순서
      );
      return maps.map((map) => Seller.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get sellers by event ID', error: e, tag: 'SellerRepository');
      // 오류 발생 시 빈 리스트 반환
      return [];
    }
  }

  // ID로 특정 판매자 조회
  Future<Seller?> getSellerById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.sellersTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Seller.fromMap(maps.first);
    }
    return null;
  }

  // 판매자명으로 특정 판매자 조회
  Future<Seller?> getSellerByName(String name) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.sellersTable,
      where: 'name = ?',
      whereArgs: [name],
    );

    if (maps.isNotEmpty) {
      return Seller.fromMap(maps.first);
    }
    return null;
  }

  // 판매자 삽입
  Future<int> insertSeller(Seller seller) async {
    final db = await _database;
    final insertedId = await db.insert(
      DatabaseServiceImpl.sellersTable,
      seller.toMap(),
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );



    // 실시간 동기화는 SellerNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
    // Firebase 업로드는 SellerNotifier에서 dataSyncService.uploadSingleSeller()로 처리됨

    return insertedId;
  }

  // 판매자 업데이트
  Future<int> updateSeller(Seller seller) async {
    if (seller.id == null) {
      throw Exception('판매자 ID가 null입니다. 업데이트할 수 없습니다.');
    }

    final db = await _database;
    final result = await db.update(
      DatabaseServiceImpl.sellersTable,
      seller.toMap(),
      where: 'id = ?',
      whereArgs: [seller.id],
    );



    // 실시간 동기화는 SellerNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
    // Firebase 업로드는 SellerNotifier에서 dataSyncService.uploadSingleSeller()로 처리됨

    return result;
  }

  // 판매자 삭제
  Future<int> deleteSeller(int sellerId) async {
    final db = await _database;
    final result = await db.delete(
      DatabaseServiceImpl.sellersTable,
      where: 'id = ?',
      whereArgs: [sellerId],
    );



    // 실시간 동기화는 SellerNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
    // Firebase 삭제는 SellerNotifier에서 처리됨

    return result;
  }

  // 판매자명으로 검색
  Future<List<Seller>> searchSellersByName(String searchQuery) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.sellersTable,
      where: 'name LIKE ?',
      whereArgs: ['%$searchQuery%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Seller.fromMap(map)).toList();
  }

  // 모든 판매자 삭제
  Future<int> deleteAllSellers() async {
    final db = await _database;
    return await db.delete(DatabaseServiceImpl.sellersTable);
  }

  // 판매자 순서 업데이트
  Future<void> updateSellerOrder(int sellerId, int newSortOrder) async {
    final db = await _database;
    await db.update(
      DatabaseServiceImpl.sellersTable,
      {'sortOrder': newSortOrder},
      where: 'id = ?',
      whereArgs: [sellerId],
    );
  }

  // 여러 판매자의 순서를 한 번에 업데이트
  Future<void> updateMultipleSellerOrders(List<Map<String, dynamic>> updates) async {
    final db = await _database;
    final batch = db.batch();

    for (final update in updates) {
      batch.update(
        DatabaseServiceImpl.sellersTable,
        {'sortOrder': update['sortOrder']},
        where: 'id = ?',
        whereArgs: [update['id']],
      );
    }

    await batch.commit();
  }

  /// ========== 배치 처리 메서드들 ==========

  /// 대량 판매자 등록 (배치 처리)
  Future<BatchResult<Seller>> batchInsertSellers(List<Seller> sellers) async {
    final processor = AdvancedBatchProcessor<Seller>(
      items: sellers,
      processBatch: (batch) async {
        final db = await _database;
        await db.transaction((txn) async {
          for (final seller in batch) {
            await txn.insert(
              DatabaseServiceImpl.sellersTable,
              seller.toMap(),
              conflictAlgorithm: ConflictAlgorithm.ignore,
            );
          }
        });
      },
    );
    return await processor.process();
  }

  /// 대량 판매자 수정 (배치 처리)
  Future<BatchResult<Seller>> batchUpdateSellers(List<Seller> sellers) async {
    final processor = AdvancedBatchProcessor<Seller>(
      items: sellers,
      processBatch: (batch) async {
        final db = await _database;
        await db.transaction((txn) async {
          for (final seller in batch) {
            if (seller.id != null) {
              await txn.update(
                DatabaseServiceImpl.sellersTable,
                seller.toMap(),
                where: 'id = ?',
                whereArgs: [seller.id],
              );
            }
          }
        });
      },
    );
    return await processor.process();
  }

  /// 대표 판매자 조회
  Future<Seller?> getDefaultSeller() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.sellersTable,
        where: 'isDefault = ?',
        whereArgs: [1],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Seller.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('Failed to get default seller', error: e, tag: 'SellerRepository');
      return null;
    }
  }

  /// 특정 행사의 대표 판매자 조회
  Future<Seller?> getDefaultSellerByEventId(int eventId) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.sellersTable,
        where: 'isDefault = ? AND eventId = ?',
        whereArgs: [1, eventId],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Seller.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('Failed to get default seller by event ID', error: e, tag: 'SellerRepository');
      return null;
    }
  }

  /// 대표 판매자 설정 (다른 판매자의 대표 설정 해제 후 설정)
  Future<int> setDefaultSeller(int sellerId) async {
    try {
      final db = await _database;
      return await db.transaction((txn) async {
        // 모든 판매자의 대표 설정 해제
        await txn.update(
          DatabaseServiceImpl.sellersTable,
          {'isDefault': 0},
          where: 'isDefault = ?',
          whereArgs: [1],
        );

        // 지정된 판매자를 대표로 설정
        return await txn.update(
          DatabaseServiceImpl.sellersTable,
          {'isDefault': 1},
          where: 'id = ?',
          whereArgs: [sellerId],
        );
      });
    } catch (e) {
      LoggerUtils.logError('Failed to set default seller', error: e, tag: 'SellerRepository');
      return 0;
    }
  }

  /// 특정 행사의 대표 판매자 설정 (해당 행사의 다른 판매자 대표 설정 해제 후 설정)
  Future<int> setDefaultSellerByEventId(int sellerId, int eventId) async {
    try {
      final db = await _database;
      return await db.transaction((txn) async {
        // 해당 행사의 모든 판매자의 대표 설정 해제
        await txn.update(
          DatabaseServiceImpl.sellersTable,
          {'isDefault': 0},
          where: 'isDefault = ? AND eventId = ?',
          whereArgs: [1, eventId],
        );

        // 지정된 판매자를 대표로 설정
        return await txn.update(
          DatabaseServiceImpl.sellersTable,
          {'isDefault': 1},
          where: 'id = ? AND eventId = ?',
          whereArgs: [sellerId, eventId],
        );
      });
    } catch (e) {
      LoggerUtils.logError('Failed to set default seller by event ID', error: e, tag: 'SellerRepository');
      return 0;
    }
  }

  /// 대표 판매자 해제
  Future<int> unsetDefaultSeller(int sellerId) async {
    try {
      final db = await _database;
      return await db.update(
        DatabaseServiceImpl.sellersTable,
        {'isDefault': 0},
        where: 'id = ?',
        whereArgs: [sellerId],
      );
    } catch (e) {
      LoggerUtils.logError('Failed to unset default seller', error: e, tag: 'SellerRepository');
      return 0;
    }
  }

  /// 대표 판매자 여부 확인
  Future<bool> isDefaultSeller(int sellerId) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.sellersTable,
        where: 'id = ? AND isDefault = ?',
        whereArgs: [sellerId, 1],
        limit: 1,
      );
      return maps.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('Failed to check if seller is default', error: e, tag: 'SellerRepository');
      return false;
    }
  }

  // 기존 BatchJob, addJob, getJob, getResult 등 관련 메서드/필드 제거


}
