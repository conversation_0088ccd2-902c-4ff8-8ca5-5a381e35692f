import 'package:flutter/material.dart';
import 'terms_agreement_screen.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../widgets/onboarding_components.dart';

/// 회원가입 화면 - 웜톤 디자인으로 개선
///
/// 단계별 진행 표시와 개선된 폼 디자인을 적용한 현대적 회원가입 경험
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class RegisterScreen extends StatefulWidget {
  final VoidCallback onRegisterSuccess;
  final VoidCallback onLoginRequested;
  const RegisterScreen({super.key, required this.onRegisterSuccess, required this.onLoginRequested});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  // FocusNode 추가
  final FocusNode emailFocus = FocusNode();
  final FocusNode passwordFocus = FocusNode();
  final FocusNode confirmPasswordFocus = FocusNode();
  // 닉네임 관련 변수/컨트롤러 완전 삭제
  bool isLoading = false;
  String? error;

  bool passwordVisible = false;
  bool confirmPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();
  }


  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    emailFocus.dispose();
    passwordFocus.dispose();
    confirmPasswordFocus.dispose();
    // nicknameController.dispose(); // 삭제
    super.dispose();
  }

  bool get isPasswordMatch => passwordController.text == confirmPasswordController.text;

  Future<void> _handleNext() async {
    setState(() { isLoading = true; error = null; });

    if (emailController.text.trim().isEmpty) {
      setState(() { error = '이메일을 입력하세요.'; isLoading = false; });
      return;
    }
    if (passwordController.text.trim().isEmpty) {
      setState(() { error = '비밀번호를 입력하세요.'; isLoading = false; });
      return;
    }
    if (confirmPasswordController.text.trim().isEmpty) {
      setState(() { error = '비밀번호 확인을 입력하세요.'; isLoading = false; });
      return;
    }
    if (!isPasswordMatch) {
      setState(() { isLoading = false; });
      return;
    }

    setState(() { isLoading = false; });

    // 약관 동의 페이지로 이동
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TermsAgreementScreen(
          email: emailController.text.trim(),
          password: passwordController.text.trim(),
          phone: '', // 전화번호 필드 제거로 빈 문자열 전달
          onBackToLogin: widget.onLoginRequested,
        ),
      ),
    );
  }





  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        widget.onLoginRequested();
      },
      child: Scaffold(
        body: OnboardingComponents.buildBackground(
          child: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: OnboardingComponents.buildCard(
                  context: context,
                  child: _buildRegisterForm(context),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 회원가입 폼 구성
  Widget _buildRegisterForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 헤더
        _buildHeader(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 진행 단계 표시
        _buildProgressIndicator(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 이메일 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: emailController,
          focusNode: emailFocus,
          label: '이메일',
          hint: '<EMAIL>',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onSubmitted: (_) => FocusScope.of(context).requestFocus(passwordFocus),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 비밀번호 입력
        _buildPasswordField(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 비밀번호 확인 입력
        _buildConfirmPasswordField(context),

        // 비밀번호 불일치 메시지
        if (!isPasswordMatch && confirmPasswordController.text.isNotEmpty)
          _buildPasswordMismatchMessage(context),

        OnboardingComponents.buildSmallSpacing(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 약관 동의
        _buildTermsAgreement(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 에러 메시지
        if (error != null) _buildErrorMessage(context),

        // 회원가입 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '다음',
          onPressed: _canRegister() ? _handleNext : null,
          isLoading: isLoading,
          icon: Icons.arrow_forward,
        ),

        OnboardingComponents.buildSectionSpacing(context),

        // 로그인 링크
        _buildLoginLink(context),
      ],
    );
  }
  /// 헤더 섹션
  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // 로고 아이콘
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
            boxShadow: [
              BoxShadow(
                color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            Icons.person_add_rounded,
            size: 30,
            color: AppColors.onboardingTextOnPrimary,
          ),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '회원가입',
        ),

        const SizedBox(height: 8),

        // 부제목
        OnboardingComponents.buildBody(
          context: context,
          text: '바라 부스 매니저와 함께 시작해보세요',
        ),
      ],
    );
  }

  /// 진행 단계 표시
  Widget _buildProgressIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.primaryOverlay,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        border: Border.all(color: AppColors.onboardingPrimary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppColors.onboardingPrimary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '계정 정보를 입력하고 이메일 인증을 완료해주세요',
              style: TextStyle(
                color: AppColors.onboardingPrimary,
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                fontWeight: FontWeight.w500,
                inherit: true,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 비밀번호 입력 필드
  Widget _buildPasswordField(BuildContext context) {
    return TextFormField(
      controller: passwordController,
      focusNode: passwordFocus,
      obscureText: !passwordVisible,
      textInputAction: TextInputAction.next,
      onChanged: (_) => setState(() {}), // 실시간 UI 갱신
      onFieldSubmitted: (_) => FocusScope.of(context).requestFocus(confirmPasswordFocus),
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context),
        color: AppColors.onboardingTextPrimary,
        inherit: true,
      ),
      decoration: InputDecoration(
        labelText: '비밀번호',
        prefixIcon: Icon(
          Icons.lock_outline,
          color: AppColors.onboardingPrimary,
          size: ResponsiveHelper.getIconSize(context),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            passwordVisible ? Icons.visibility : Icons.visibility_off,
            color: AppColors.onboardingTextSecondary,
          ),
          onPressed: () => setState(() => passwordVisible = !passwordVisible),
        ),
        labelStyle: TextStyle(
          color: AppColors.onboardingTextSecondary,
          fontSize: ResponsiveHelper.getBodyFontSize(context),
          inherit: true,
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: AppColors.onboardingPrimary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: ResponsiveHelper.getButtonPadding(context),
      ),
    );
  }

  /// 비밀번호 확인 입력 필드
  Widget _buildConfirmPasswordField(BuildContext context) {
    return TextFormField(
      controller: confirmPasswordController,
      focusNode: confirmPasswordFocus,
      obscureText: !confirmPasswordVisible,
      textInputAction: TextInputAction.next,
      onChanged: (_) => setState(() {}), // 실시간 UI 갱신
      onFieldSubmitted: (_) => _handleNext(),
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context),
        color: AppColors.onboardingTextPrimary,
        inherit: true,
      ),
      decoration: InputDecoration(
        labelText: '비밀번호 확인',
        prefixIcon: Icon(
          Icons.lock_outline,
          color: AppColors.onboardingPrimary,
          size: ResponsiveHelper.getIconSize(context),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            confirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
            color: AppColors.onboardingTextSecondary,
          ),
          onPressed: () => setState(() => confirmPasswordVisible = !confirmPasswordVisible),
        ),
        labelStyle: TextStyle(
          color: AppColors.onboardingTextSecondary,
          fontSize: ResponsiveHelper.getBodyFontSize(context),
          inherit: true,
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: AppColors.onboardingPrimary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: ResponsiveHelper.getButtonPadding(context),
      ),
    );
  }

  /// 비밀번호 불일치 메시지
  Widget _buildPasswordMismatchMessage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.errorLight.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: AppColors.error, size: 16),
            const SizedBox(width: 8),
            Text(
              '비밀번호가 일치하지 않습니다.',
              style: TextStyle(
                color: AppColors.error,
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                inherit: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 약관 동의 섹션
  Widget _buildTermsAgreement(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 안내 텍스트
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            '다음 단계에서 이용약관에 동의해주세요.',
            style: TextStyle(
              fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
              color: AppColors.onboardingTextSecondary,
              inherit: true,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// 에러 메시지
  Widget _buildErrorMessage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.errorLight.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: AppColors.error, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                error!,
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                  inherit: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 로그인 링크
  Widget _buildLoginLink(BuildContext context) {
    return TextButton(
      onPressed: widget.onLoginRequested,
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
      ),
      child: RichText(
        text: TextSpan(
          style: TextStyle(
            fontSize: ResponsiveHelper.getBodyFontSize(context),
            color: AppColors.onboardingTextSecondary,
            inherit: true,
          ),
          children: [
            const TextSpan(text: '이미 계정이 있으신가요? '),
            TextSpan(
              text: '로그인',
              style: TextStyle(
                color: AppColors.onboardingPrimary,
                fontWeight: FontWeight.w600,
                inherit: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 회원가입 가능 여부 확인
  bool _canRegister() {
    return !isLoading &&
           emailController.text.trim().isNotEmpty &&
           passwordController.text.trim().isNotEmpty &&
           isPasswordMatch &&
           passwordController.text.isNotEmpty &&
           confirmPasswordController.text.isNotEmpty;
  }
}
