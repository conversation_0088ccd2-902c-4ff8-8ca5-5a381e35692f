import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'verify_email_screen.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/onboarding_components.dart';
import '../../providers/nickname_provider.dart';

import '../../services/subscription_service.dart';
import '../../models/subscription_plan.dart';


class TermsAgreementScreen extends ConsumerStatefulWidget {
  final String email;
  final String password;
  final String phone;
  final bool isGoogleLogin;
  final bool isAppleLogin;
  final UserCredential? socialUserCredential;
  final VoidCallback? onBackToLogin;
  final void Function(bool hasServerData)? onLoginSuccess;

  const TermsAgreementScreen({
    super.key,
    required this.email,
    required this.password,
    required this.phone,
    this.isGoogleLogin = false,
    this.isAppleLogin = false,
    this.socialUserCredential,
    this.onBackToLogin,
    this.onLoginSuccess,
  });

  @override
  ConsumerState<TermsAgreementScreen> createState() => _TermsAgreementScreenState();
}

class _TermsAgreementScreenState extends ConsumerState<TermsAgreementScreen> {
  bool agreeAll = false;
  bool agreeTermsOfService = false;
  bool agreePrivacyPolicy = false;
  bool agreeAge = false; // 14세 이상 확인
  bool isLoading = false;
  String? error;

  static const String termsOfService = '''바라 부스 매니저 서비스 이용약관

제1조 (목적)
이 약관은 바라 부스 매니저(이하 "서비스")를 제공하는 권태영(이하 "회사")과 서비스를 이용하는 회원 간의 권리, 의무 및 책임사항을 규정함을 목적으로 합니다.

제2조 (정의)
1. "서비스"란 회사가 제공하는 동인 행사 부스 관리 모바일 애플리케이션 및 관련 서비스를 의미합니다.
2. "회원"이란 이 약관에 동의하고 회사와 서비스 이용계약을 체결한 개인을 의미합니다.
3. "부스 데이터"란 회원이 서비스를 통해 입력, 저장하는 상품정보, 판매기록, 재고정보 등을 의미합니다.
4. "유료 서비스"란 회사가 제공하는 구독 기반의 프리미엄 서비스를 의미합니다.
5. "콘텐츠"란 회원이 서비스를 통해 생성, 업로드, 저장하는 모든 정보와 데이터를 의미합니다.

제3조 (약관의 효력 및 변경)
1. 이 약관은 서비스 화면에 게시하거나 기타의 방법으로 회원에게 공지함으로써 효력이 발생합니다.
2. 회사는 필요한 경우 이 약관을 변경할 수 있으며, 변경된 약관은 제1항과 같은 방법으로 공지 또는 통지함으로써 효력이 발생합니다.
3. 중요한 약관 변경 시에는 최소 7일 전에 공지하며, 회원이 변경에 동의하지 않을 경우 서비스 이용을 중단하고 탈퇴할 수 있습니다.

제4조 (서비스의 제공)
1. 회사는 다음과 같은 서비스를 제공합니다:
   - 상품 등록 및 재고 관리
   - 판매 기록 및 매출 통계
   - 선입금 관리
   - 행사별 데이터 관리
   - 클라우드 데이터 동기화
   - 프리미엄 기능 (유료 서비스)
2. 서비스는 연중무휴, 1일 24시간 제공함을 원칙으로 합니다.
3. 회사는 시스템 점검, 보수 또는 기타 필요에 의해 서비스 제공을 일시 중단할 수 있습니다.

제5조 (회원가입 및 계정 관리)
1. 회원가입은 이용신청자가 약관에 동의하고 회사가 승낙함으로써 성립됩니다.
2. 회원은 정확하고 최신의 정보를 제공해야 하며, 허위 정보 제공 시 서비스 이용이 제한될 수 있습니다.
3. 회원은 자신의 계정 정보를 안전하게 관리할 책임이 있으며, 제3자에게 계정을 양도하거나 대여할 수 없습니다.
4. 만 14세 미만인 경우 법정대리인의 동의가 필요하며, 허위 연령 정보 제공 시 서비스 이용이 제한됩니다

제6조 (유료 서비스 및 구독)
1. 회사는 일부 서비스를 유료로 제공할 수 있으며, 유료 서비스 이용 시 별도의 결제가 필요합니다.
2. 구독 서비스는 월 단위 또는 연 단위로 제공되며, 구독 기간 중 서비스를 이용할 수 있습니다.
3. 구독료는 구독 시작일로부터 매월 또는 매년 동일한 날짜에 자동 결제됩니다.
4. 구독 서비스 이용을 위해서는 전화번호 인증이 필요하며, 동일한 전화번호로 중복 구독은 불가능합니다.

제7조 (결제 및 환불)
1. 유료 서비스 결제는 신용카드, 체크카드 등 회사가 지정한 결제 수단을 통해 이루어집니다.
2. 결제 완료 후 구독 서비스가 즉시 활성화됩니다.
3. 환불 정책:
   - 구독 서비스 해지 시, 남은 구독 기간에 대해 일할 계산하여 환불합니다.
   - 환불 신청은 고객센터를 통해 접수하며, 신청일로부터 3-5 영업일 내에 처리됩니다.
   - 단, 서비스를 이미 상당 부분 이용한 경우 환불이 제한될 수 있습니다.
4. 자동 결제를 원하지 않는 경우, 구독 만료 최소 24시간 전에 해지해야 합니다.

제8조 (회원의 의무)
1. 회원은 다음 행위를 하여서는 안 됩니다:
   - 타인의 개인정보를 도용하거나 허위 정보를 입력하는 행위
   - 서비스의 안정적 운영을 방해하는 행위
   - 회사나 제3자의 지적재산권을 침해하는 행위
   - 불법적이거나 부적절한 콘텐츠를 업로드하는 행위
   - 서비스를 상업적 목적으로 무단 이용하는 행위
2. 회원이 위 의무를 위반할 경우, 회사는 서비스 이용을 제한하거나 계약을 해지할 수 있습니다.

제9조 (회사의 의무)
1. 회사는 안정적이고 지속적인 서비스 제공을 위해 최선을 다합니다.
2. 회사는 회원의 개인정보를 관련 법령에 따라 보호하며, 개인정보처리방침에 따라 처리합니다.
3. 회사는 회원의 의견이나 불만을 신속하고 적절하게 처리합니다.

제10조 (서비스 이용 제한)
1. 회사는 다음의 경우 서비스 이용을 제한하거나 중단할 수 있습니다:
   - 회원이 본 약관을 위반한 경우
   - 서비스의 정상적인 운영을 방해하는 경우
   - 법령에 위반되는 행위를 한 경우
   - 기타 회사가 필요하다고 판단하는 경우
2. 서비스 이용 제한 시 회원에게 사전 통지하며, 긴급한 경우 사후 통지할 수 있습니다.

제11조 (데이터 및 콘텐츠)
1. 회원이 생성한 데이터와 콘텐츠에 대한 권리는 회원에게 있습니다.
2. 회사는 서비스 제공을 위해 필요한 범위 내에서 회원의 콘텐츠를 이용할 수 있습니다.
3. 회원 탈퇴 시 회원의 데이터는 즉시 삭제되며, 복구가 불가능합니다.
4. 회사는 회원 데이터의 백업 및 보안을 위해 노력하나, 데이터 손실에 대한 완전한 책임을 지지 않습니다.

제12조 (책임의 제한)
1. 회사는 천재지변, 전쟁, 파업, 정부의 규제 등 불가항력으로 인한 서비스 중단에 대해 책임을 지지 않습니다.
2. 회사는 회원의 귀책사유로 인한 서비스 이용 장애에 대해 책임을 지지 않습니다.
3. 회사는 회원이 서비스를 통해 얻은 정보나 자료의 신뢰성, 정확성에 대해 보증하지 않습니다.
4. 회사의 손해배상 책임은 고의 또는 중과실이 있는 경우를 제외하고는 회원이 지급한 서비스 이용료를 초과하지 않습니다.

제13조 (분쟁 해결)
1. 서비스 이용과 관련하여 회사와 회원 간에 분쟁이 발생한 경우, 상호 협의를 통해 해결하도록 노력합니다.
2. 협의로 해결되지 않는 분쟁에 대해서는 대한민국 법원의 관할에 따릅니다.
3. 본 약관은 대한민국 법률에 따라 해석됩니다.

제14조 (기타)
1. 본 약관에서 정하지 않은 사항은 관련 법령 및 상관례에 따릅니다.
2. 본 약관의 일부 조항이 무효로 판단되더라도 나머지 조항의 효력에는 영향을 주지 않습니다.

시행일: 2025년 8월 22일
문의: <EMAIL>
전화: 070-8080-4308''';

  static const String privacyPolicy = '''바라 부스 매니저 개인정보처리방침

1. 수집하는 개인정보의 항목

필수 수집 정보
• 이메일 주소: 회원가입, 로그인, 본인 확인
• 비밀번호: 계정 보안 (암호화 저장)

선택 수집 정보 (구독 서비스 이용 시 필수)
• 전화번호: 본인 인증, 구독 서비스 이용 자격 확인, 고객 지원
• ※ 전화번호는 구독 서비스 이용을 위해 필수적으로 수집됩니다.
• ※ 동일한 전화번호로 여러 계정에서 구독 서비스를 이용할 수 없습니다.
• ※ SMS를 통한 본인 인증 및 중요 알림 발송에 활용됩니다.

결제 관련 정보 (구독 시)
• 카드번호, 유효기간, 생년월일, 카드 비밀번호 앞 2자리
• 구매자명, 구매자 이메일, 구매자 전화번호
• 결제내역: 결제금액, 결제일시, 승인번호

서비스 이용 데이터
• 행사정보: 행사명, 일정 등 (사용자 입력)
• 상품정보: 상품명, 가격, 재고 등 (사용자 입력)
• 판매자 닉네임 등 (사용자 입력)
• 매출기록: 판매 내역, 통계 등 (사용자 입력)

2. 개인정보의 수집·이용 목적
• 회원 가입, 본인 확인 및 회원제 서비스 제공
• 결제 처리, 정기 구독 관리 및 요금 정산
• 고객 지원, 문의 응대 및 서비스 안내
• 서비스 개선 및 맞춤형 서비스 제공
• 부정 이용 방지 및 서비스 안정성 확보

3. 개인정보의 보유 및 이용기간

회원 탈퇴 시
• 개인정보는 즉시 삭제됩니다
• 단, 관련 법령에 의해 보존이 필요한 경우 해당 기간 동안 보관

법령에 의한 보존
• 계약 또는 청약철회 등에 관한 기록: 5년 (전자상거래법)
• 대금결제 및 재화 등의 공급에 관한 기록: 5년 (전자상거래법)
• 소비자의 불만 또는 분쟁처리에 관한 기록: 3년 (전자상거래법)
• 로그인 기록: 3개월 (통신비밀보호법)

4. 개인정보의 제3자 제공
• 회사는 원칙적으로 이용자의 개인정보를 외부에 제공하지 않습니다
• 다음의 경우에는 예외로 합니다:
  - 이용자가 사전에 동의한 경우
  - 법령의 규정에 의거하거나, 수사 목적으로 법령에 정해진 절차와 방법에 따라 수사기관의 요구가 있는 경우

5. 개인정보처리 위탁

위탁업체 및 위탁업무
• Google Firebase: 회원관리, 데이터 저장, 인증 서비스
• 나이스페이먼츠: 결제 처리 및 정산
• 네이버 클라우드 플랫폼: SMS 발송

위탁업체 관리
• 개인정보보호 관련 법령 준수 의무 부과
• 위탁계약서를 통한 개인정보보호 의무 명시
• 위탁업무 수행 목적 외 개인정보 처리 금지
• 개인정보의 안전성 확보를 위한 관리·감독

6. 이용자의 권리·의무 및 행사방법

권리 행사 방법
• 앱 내 설정 메뉴에서 직접 처리
• 이메일: <EMAIL>
• 전화: 070-8080-4308
• 처리기간: 요청일로부터 10일 이내

이용자 권리
• 개인정보 열람 요구
• 오류 등이 있을 경우 정정·삭제 요구
• 개인정보 처리정지 요구
• 손해배상 청구

7. 개인정보의 안전성 확보조치
• 개인정보 암호화: 비밀번호 등 중요정보 암호화 저장
• 해킹 등에 대비한 기술적 대책
• 개인정보에 대한 접근 제한
• 접속기록의 보관 및 위변조 방지
• 개인정보취급자에 대한 정기적 교육

8. 개인정보보호책임자
성명: 권태영
직책: 대표
연락처: 070-8080-4308
이메일: <EMAIL>

개인정보 처리와 관련한 불만처리 및 피해구제를 위해 언제든지 연락해 주시기 바랍니다.

9. 앱스토어 및 플랫폼 관련 정보

앱 배포 플랫폼
• Google Play Store
• Apple App Store
• 각 플랫폼의 개인정보 처리방침도 함께 적용됩니다

10. 개인정보 처리방침의 변경
• 본 개인정보처리방침은 시행일로부터 적용됩니다
• 법령·정책 또는 보안기술의 변경에 따라 내용의 추가·삭제 및 수정이 있을 경우 변경사항의 시행 7일 전부터 공지사항을 통하여 고지합니다
• 수집하는 개인정보의 항목, 이용목적의 변경 등 이용자 권리의 중대한 변경이 발생할 때에는 최소 30일 전에 공지하며, 필요시 이용자 동의를 다시 받을 수 있습니다

시행일: 2025년 8월 20일
문의: <EMAIL>
전화: 070-8080-4308''';

  @override
  void initState() {
    super.initState();
    OrientationHelper.enterPortraitMode();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _updateAgreeAll() {
    setState(() {
      agreeAll = agreeTermsOfService && agreePrivacyPolicy && agreeAge;
    });
  }

  void _toggleAgreeAll(bool? value) {
    setState(() {
      agreeAll = value ?? false;
      agreeTermsOfService = agreeAll;
      agreePrivacyPolicy = agreeAll;
      agreeAge = agreeAll;
    });
  }

  void _showTermsDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(content),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleRegister() async {
    if (!agreeTermsOfService || !agreePrivacyPolicy || !agreeAge) {
      setState(() {
        error = '필수 약관에 동의해야 회원가입이 가능합니다.';
      });
      return;
    }

    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      UserCredential userCred;

      if (widget.isGoogleLogin || widget.isAppleLogin) {
        // 소셜 로그인의 경우 이미 인증된 사용자 사용
        if (widget.socialUserCredential == null) {
          throw Exception('소셜 로그인 정보가 없습니다.');
        }
        userCred = widget.socialUserCredential!;

        // Firebase Auth 상태 확인 (재인증 로직 제거)
        final currentUser = FirebaseAuth.instance.currentUser;
        LoggerUtils.logInfo('현재 Firebase Auth 사용자: ${currentUser?.uid}', tag: 'TermsAgreementScreen');
        LoggerUtils.logInfo('소셜 로그인 사용자: ${userCred.user?.uid}', tag: 'TermsAgreementScreen');

        // 인증 상태가 일치하지 않아도 socialUserCredential을 신뢰하고 진행
        // Apple/Google 로그인에서 전달받은 userCredential을 그대로 사용
      } else {
        // 이메일 회원가입
        final auth = FirebaseAuth.instance;
        userCred = await auth.createUserWithEmailAndPassword(
          email: widget.email.trim(),
          password: widget.password.trim(),
        );
      }

      // 인증 상태 재확인
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != userCred.user!.uid) {
        throw Exception('Firebase 인증 상태가 올바르지 않습니다.');
      }

      // Firestore에 사용자 정보 저장
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userCred.user!.uid)
          .set({
        'email': widget.email.trim(),
        'createdAt': FieldValue.serverTimestamp(),
        'agreement': {
          'agreed': true,
          'agreedAt': FieldValue.serverTimestamp(),
          'termsOfService': agreeTermsOfService,
          'privacyPolicy': agreePrivacyPolicy,
          'ageConfirmed': agreeAge,
        },
      }, SetOptions(merge: true));

      if (!widget.isGoogleLogin && !widget.isAppleLogin) {
        // 이메일 회원가입의 경우 이메일 인증 필요
        await userCred.user!.sendEmailVerification();
        
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('email_verification_pending', true);
        
        await FirebaseAuth.instance.signOut();
        
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const VerifyEmailScreen(),
          ),
        );
      } else {
        // 소셜 로그인의 경우 바로 완료 - 로그인 성공 처리
        await _handleSocialLoginSuccess(userCred.user!);
      }
    } on FirebaseAuthException catch (e) {
      setState(() {
        error = _firebaseErrorToKorean(e);
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = '회원가입 실패: $e';
        isLoading = false;
      });
    }
  }

  /// 소셜 로그인 성공 후 처리
  Future<void> _handleSocialLoginSuccess(User user) async {
    try {
      // nicknameProvider 동기화
      await ref.read(nicknameProvider.notifier).loadNickname();

      // 닉네임 전용 동기화 (플랜에 관계없이 항상 실행)
      await ref.read(nicknameProvider.notifier).syncNicknameOnly();

      // 서버 데이터 존재 여부 확인
      final hasServerData = await _checkServerDataAndSetSyncFlag();



      // 로그인 성공 처리 - LoginScreen의 onLoginSuccess 콜백 호출
      if (mounted && widget.onLoginSuccess != null) {
        // 현재 화면을 닫고 LoginScreen으로 돌아간 후 onLoginSuccess 호출
        Navigator.of(context).pop();
        widget.onLoginSuccess!(hasServerData);
      }
    } catch (e) {
      LoggerUtils.logError('소셜 로그인 후처리 실패', tag: 'TermsAgreementScreen', error: e);
      setState(() {
        error = '로그인 처리 중 오류가 발생했습니다: $e';
        isLoading = false;
      });
    }
  }

  /// Firebase Auth 에러를 한국어로 변환
  String _firebaseErrorToKorean(FirebaseAuthException e) {
    switch (e.code) {
      case 'email-already-in-use':
        return '이미 가입된 이메일입니다. 로그인을 시도해보세요.';
      case 'invalid-email':
        return '올바른 이메일 형식이 아닙니다.';
      case 'weak-password':
        return '비밀번호가 너무 약합니다. 6자 이상 입력해주세요.';
      case 'operation-not-allowed':
        return '이메일 회원가입이 비활성화되어 있습니다.';
      case 'too-many-requests':
        return '잠시 후 다시 시도해 주세요.';
      case 'network-request-failed':
        return '네트워크 오류가 발생했습니다.';
      case 'user-disabled':
        return '이 계정은 비활성화되어 있습니다.';
      default:
        return '회원가입 중 오류가 발생했습니다. 다시 시도해주세요.';
    }
  }

  /// 서버 데이터 존재 여부 확인 및 동기화 플래그 설정 (플랜별 처리)
  Future<bool> _checkServerDataAndSetSyncFlag() async {
    try {
      // 현재 사용자의 구독 플랜 확인
      final subscriptionService = SubscriptionService();
      final currentPlan = await subscriptionService.getCurrentPlan();
      final planType = await subscriptionService.getCurrentPlanType();

      LoggerUtils.logInfo('현재 구독 플랜: ${currentPlan.name} (타입: $planType)', tag: 'TermsAgreementScreen');

      // 🔥 프리플랜, 플러스 플랜은 서버 동기화 기능이 없으므로 무조건 스킵
      if (planType == SubscriptionPlanType.free || planType == SubscriptionPlanType.plus || !currentPlan.hasServerSyncFeature) {
        LoggerUtils.logInfo('${currentPlan.name}은 서버 동기화 기능이 없어 동기화 플로우를 스킵합니다 (플랜타입: $planType)', tag: 'TermsAgreementScreen');

        // SharedPreferences에 플래그 저장 (동기화 불필요)
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_server_data_on_login', false);

        return false; // 동기화 화면으로 이동하지 않음
      }

      // 🔥 프로 플랜이 아닌 경우 추가 안전장치
      if (planType != SubscriptionPlanType.plus) { // 프로 플랜 제거됨
        LoggerUtils.logWarning('프로 플랜이 아닌데 서버 동기화 기능이 활성화됨 - 강제로 스킵: $planType', tag: 'TermsAgreementScreen');
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_server_data_on_login', false);
        return false;
      }

      // 로컬 전용 모드: 서버 데이터 확인 불필요
      LoggerUtils.logInfo('로컬 전용 모드: 서버 데이터 확인 건너뜀', tag: 'TermsAgreementScreen');

      // SharedPreferences에 서버 데이터 존재 여부 저장 (항상 false)
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_server_data_on_login', false);

      LoggerUtils.logInfo('로컬 전용 모드: 서버 데이터 없음 - 정상 온보딩 진행', tag: 'TermsAgreementScreen');
      return false;
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: 'TermsAgreementScreen', error: e);
      // 확인 실패 시 안전하게 false로 설정
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_server_data_on_login', false);
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        if (widget.onBackToLogin != null) {
          widget.onBackToLogin!();
        }
      },
      child: Scaffold(
        body: OnboardingComponents.buildBackground(
          child: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: OnboardingComponents.buildCard(
                  context: context,
                  child: _buildTermsForm(context),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTermsForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 제목
        Text(
          '약관 동의',
          style: TextStyle(
            fontSize: ResponsiveHelper.getTitleFontSize(context),
            fontWeight: FontWeight.bold,
            color: AppColors.onboardingTextPrimary,
          ),
        ),
        
        const SizedBox(height: 24),
        
        // 전체 동의 체크박스
        _buildAgreeAllCheckbox(context),
        
        const Divider(height: 32),
        
        // 개별 약관 체크박스들
        _buildIndividualAgreements(context),
        
        const SizedBox(height: 24),
        
        // 에러 메시지
        if (error != null)
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.error.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: AppColors.error,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    error!,
                    style: TextStyle(
                      color: AppColors.error,
                      fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        
        // 회원가입 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: widget.isGoogleLogin || widget.isAppleLogin ? '로그인 완료' : '회원가입',
          onPressed: (agreeTermsOfService && agreePrivacyPolicy && agreeAge && !isLoading)
              ? _handleRegister
              : null,
          isLoading: isLoading,
        ),
      ],
    );
  }

  Widget _buildAgreeAllCheckbox(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.onboardingPrimary.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: agreeAll,
            onChanged: _toggleAgreeAll,
            activeColor: AppColors.onboardingPrimary,
          ),
          Expanded(
            child: Text(
              '아래 약관 모두에 동의합니다',
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
                color: AppColors.onboardingTextPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndividualAgreements(BuildContext context) {
    return Column(
      children: [
        _buildAgreementItem(
          context: context,
          title: '[필수] 서비스 이용약관',
          isRequired: true,
          isChecked: agreeTermsOfService,
          onChanged: (value) {
            setState(() {
              agreeTermsOfService = value ?? false;
              _updateAgreeAll();
            });
          },
          onViewDetails: () => _showTermsDialog('서비스 이용약관', termsOfService),
        ),

        const SizedBox(height: 12),

        _buildAgreementItem(
          context: context,
          title: '[필수] 개인정보처리방침',
          isRequired: true,
          isChecked: agreePrivacyPolicy,
          onChanged: (value) {
            setState(() {
              agreePrivacyPolicy = value ?? false;
              _updateAgreeAll();
            });
          },
          onViewDetails: () => _showTermsDialog('개인정보처리방침', privacyPolicy),
        ),

        const SizedBox(height: 12),

        _buildAgreementItem(
          context: context,
          title: '[필수] 만 14세 이상입니다',
          isRequired: true,
          isChecked: agreeAge,
          onChanged: (value) {
            setState(() {
              agreeAge = value ?? false;
              _updateAgreeAll();
            });
          },
          onViewDetails: () => _showTermsDialog(
            '연령 확인',
            '본 서비스는 만 14세 이상만 이용할 수 있습니다.\n\n개인정보보호법에 따라 만 14세 미만 아동의 개인정보 수집·이용을 위해서는 법정대리인의 동의가 필요하므로, 만 14세 미만은 서비스를 이용할 수 없습니다.',
          ),
        ),
      ],
    );
  }

  Widget _buildAgreementItem({
    required BuildContext context,
    required String title,
    required bool isRequired,
    required bool isChecked,
    required ValueChanged<bool?> onChanged,
    required VoidCallback onViewDetails,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        children: [
          Checkbox(
            value: isChecked,
            onChanged: onChanged,
            activeColor: AppColors.onboardingPrimary,
          ),
          Expanded(
            child: GestureDetector(
              onTap: onViewDetails,
              child: Text(
                title,
                style: TextStyle(
                  fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
                  color: AppColors.onboardingTextPrimary,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: onViewDetails,
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.onboardingTextSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
