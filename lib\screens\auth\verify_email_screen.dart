import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/toast_utils.dart';
import '../../app/app_entry_point.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/onboarding_components.dart';
import 'login_screen.dart';

/// 이메일 확인 페이지 - 웜톤 디자인으로 개선
///
/// 완전히 새로운 디자인과 일러스트레이션을 적용한 현대적 이메일 인증 안내
/// 명확한 액션 가이드와 반응형 레이아웃으로 사용자 경험 개선
class VerifyEmailScreen extends StatefulWidget {
  const VerifyEmailScreen({super.key});

  @override
  State<VerifyEmailScreen> createState() => _VerifyEmailScreenState();
}

class _VerifyEmailScreenState extends State<VerifyEmailScreen> {

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        if (!mounted) return;
        // mounted 체크 후 안전하게 Navigator 사용
        if (context.mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) => const AppEntryPoint()),
            (route) => false,
          );
        }
      },
      child: Scaffold(
        body: OnboardingComponents.buildBackground(
          child: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: OnboardingComponents.buildCard(
                  context: context,
                  child: _buildContent(context),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 메인 컨텐츠 구성
  Widget _buildContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 성공 일러스트레이션
        _buildSuccessIllustration(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '회원가입 완료!',
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 메인 메시지
        OnboardingComponents.buildSubtitle(
          context: context,
          text: '입력하신 이메일로\n인증 메일이 발송되었습니다',
        ),

        OnboardingComponents.buildSectionSpacing(context),

        // 단계별 안내
        _buildStepGuide(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 액션 버튼들
        _buildActionButtons(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 도움말 텍스트
        _buildHelpText(context),
      ],
    );
  }

  /// 성공 일러스트레이션
  Widget _buildSuccessIllustration(BuildContext context) {
    final iconSize = ResponsiveHelper.getMainIconSize(context);

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 배경 원
          Container(
            width: iconSize * 0.7,
            height: iconSize * 0.7,
            decoration: BoxDecoration(
              color: AppColors.onboardingAccentLight,
              shape: BoxShape.circle,
            ),
          ),
          // 메일 아이콘
          Icon(
            Icons.mark_email_read_rounded,
            size: iconSize * 0.4,
            color: AppColors.onboardingTextOnPrimary,
          ),
          // 체크 마크
          Positioned(
            right: iconSize * 0.1,
            top: iconSize * 0.1,
            child: Container(
              width: iconSize * 0.25,
              height: iconSize * 0.25,
              decoration: BoxDecoration(
                color: AppColors.success,
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.surface, width: 2),
              ),
              child: Icon(
                Icons.check,
                size: iconSize * 0.15,
                color: AppColors.onboardingTextOnPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 단계별 안내
  Widget _buildStepGuide(BuildContext context) {
    final steps = [
      {'icon': Icons.email_outlined, 'text': '이메일 앱을 열어주세요'},
      {'icon': Icons.search, 'text': '바라 부스 매니저에서 온 메일을 찾아주세요'},
      {'icon': Icons.touch_app, 'text': '인증 링크를 클릭해주세요'},
      {'icon': Icons.login, 'text': '로그인 화면에서 다시 로그인해주세요'},
    ];

    return Container(
      padding: ResponsiveHelper.getCardPadding(context),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '인증 완료 방법',
            style: TextStyle(
              fontSize: ResponsiveHelper.getSubtitleFontSize(context) - 2,
              fontWeight: FontWeight.w600,
              color: AppColors.onboardingTextPrimary,
            ),
          ),

          const SizedBox(height: 16),

          ...steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;

            return Padding(
              padding: EdgeInsets.only(bottom: index < steps.length - 1 ? 12 : 0),
              child: Row(
                children: [
                  // 단계 번호
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: AppColors.onboardingPrimary,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: AppColors.onboardingTextOnPrimary,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // 아이콘
                  Icon(
                    step['icon'] as IconData,
                    color: AppColors.onboardingPrimary,
                    size: 20,
                  ),

                  const SizedBox(width: 12),

                  // 텍스트
                  Expanded(
                    child: Text(
                      step['text'] as String,
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
                        color: AppColors.onboardingTextSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// 액션 버튼들
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // 메인 버튼 - 로그인 화면으로
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '로그인 화면으로 이동',
          onPressed: () async {
            try {
              // 이메일 인증 대기 상태 해제 및 인증 완료 상태 설정
              final prefs = await SharedPreferences.getInstance();
              await prefs.remove('email_verification_pending');
              await prefs.setBool('email_verification_completed', true); // 이메일 인증 완료 플래그

              // 이메일 인증 완료 후 바로 로그인 페이지로 이동 (사용자 요청대로 단순화)
              LoggerUtils.logInfo('이메일 인증 완료 - 로그인 페이지로 직접 이동', tag: 'VerifyEmailScreen');

              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (newContext) => LoginScreen(
                    onLoginSuccess: (hasServerData) {
                      // 로그인 성공 시 AppEntryPoint로 이동하여 정상적인 플로우 따르기
                      // newContext를 사용하여 현재 활성화된 LoginScreen의 context 사용
                      Navigator.of(newContext).pushAndRemoveUntil(
                        MaterialPageRoute(builder: (_) => const AppEntryPoint()),
                        (route) => false,
                      );
                    },
                    onRegisterRequested: () {
                      // 회원가입 요청 시 AppEntryPoint로 이동
                      // newContext를 사용하여 현재 활성화된 LoginScreen의 context 사용
                      Navigator.of(newContext).pushAndRemoveUntil(
                        MaterialPageRoute(builder: (_) => const AppEntryPoint()),
                        (route) => false,
                      );
                    },
                  ),
                ),
                (route) => false,
              );
            } catch (e) {
              // 전체 프로세스 에러 처리
              LoggerUtils.logError('로그인 화면 이동 실패', tag: 'VerifyEmailScreen', error: e);
              if (mounted) {
                try {
                  ToastUtils.showToast(context, '로그인 화면으로 이동하는 중 오류가 발생했습니다.');
                } catch (toastError) {
                  // Toast 표시 실패도 조용히 처리
                  LoggerUtils.logWarning('Toast 표시 실패', tag: 'VerifyEmailScreen', error: toastError);
                }
              }
            }
          },
          icon: Icons.login,
        ),

        const SizedBox(height: 12),

        // 보조 버튼 - 메일 재발송
        OnboardingComponents.buildSecondaryButton(
          context: context,
          text: '인증 메일 재발송',
          onPressed: () async {
            if (!mounted) return;
            try {
              final user = FirebaseAuth.instance.currentUser;
              if (user != null && !user.emailVerified) {
                await user.sendEmailVerification();
                if (mounted) {
                  try {
                    ToastUtils.showToast(context, '인증 메일이 재발송되었습니다.');
                  } catch (toastError) {
                    LoggerUtils.logWarning('Toast 표시 실패', tag: 'VerifyEmailScreen', error: toastError);
                  }
                }
              }
            } catch (e) {
              LoggerUtils.logError('메일 재발송 실패', tag: 'VerifyEmailScreen', error: e);
              if (mounted) {
                try {
                  ToastUtils.showToast(context, '메일 재발송에 실패했습니다.');
                } catch (toastError) {
                  LoggerUtils.logWarning('Toast 표시 실패', tag: 'VerifyEmailScreen', error: toastError);
                }
              }
            }
          },
          icon: Icons.refresh,
        ),
      ],
    );
  }

  /// 도움말 텍스트
  Widget _buildHelpText(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.infoLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        border: Border.all(color: AppColors.info.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help_outline,
                color: AppColors.info,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '도움이 필요하신가요?',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
                    fontWeight: FontWeight.w600,
                    color: AppColors.info,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 아이콘과 정렬을 맞추기 위해 Row로 감싸고 아이콘 너비만큼 여백 추가
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 28), // 아이콘 크기(20) + SizedBox(8) = 28
              Expanded(
                child: Text(
                  '• 스팸 폴더도 확인해보세요\n• 메일이 오지 않으면 재발송 버튼을 눌러주세요',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    color: AppColors.onboardingTextSecondary,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
