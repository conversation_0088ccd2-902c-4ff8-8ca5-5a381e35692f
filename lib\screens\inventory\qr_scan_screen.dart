import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../utils/app_colors.dart';

class QrScanScreen extends StatefulWidget {
  const QrScanScreen({Key? key}) : super(key: key);

  @override
  State<QrScanScreen> createState() => _QrScanScreenState();
}

class _QrScanScreenState extends State<QrScanScreen> {
  MobileScannerController? _controller;
  bool _flashOn = false;
  bool _isScanning = true;
  String? _errorMessage;
  bool _permissionGranted = false;

  @override
  void initState() {
    super.initState();
    _initializeScanner();
  }

  Future<void> _initializeScanner() async {
    try {
      // 1. 카메라 권한 확인 및 요청
      final permission = await Permission.camera.request();
      if (permission != PermissionStatus.granted) {
        setState(() {
          _errorMessage = '카메라 권한이 필요합니다.';
        });
        return;
      }

      setState(() {
        _permissionGranted = true;
      });

      // 2. 스캐너 컨트롤러 초기화
      _controller = MobileScannerController(
        detectionSpeed: DetectionSpeed.noDuplicates,
        facing: CameraFacing.back,
        torchEnabled: false,
      );

    } catch (e) {
      setState(() {
        _errorMessage = 'QR코드 스캐너 초기화 실패: $e';
      });
    }
  }

  void _onDetect(BarcodeCapture capture) {
    if (!_isScanning) return;
    
    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isNotEmpty && barcodes.first.rawValue != null) {
      setState(() {
        _isScanning = false;
      });
      Navigator.of(context).pop(barcodes.first.rawValue);
    }
  }

  void _toggleFlash() {
    if (_controller != null) {
      setState(() {
        _flashOn = !_flashOn;
      });
      _controller!.toggleTorch();
    }
  }

  void _retryInitialization() {
    setState(() {
      _errorMessage = null;
      _isScanning = true;
      _permissionGranted = false;
    });
    _controller?.dispose();
    _controller = null;
    _initializeScanner();
  }

  @override
  Future<void> dispose() async {
    _controller?.dispose();
    super.dispose();
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'QR코드 스캐너를 사용할 수 없습니다.',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _retryInitialization,
              child: const Text('다시 시도'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final media = MediaQuery.of(context);
    final isPortrait = media.orientation == Orientation.portrait;
    final scanSize = isPortrait
        ? media.size.width * 0.6
        : media.size.height * 0.6;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // QR 스캐너 화면
          if (_permissionGranted && _controller != null && _errorMessage == null)
            MobileScanner(
              controller: _controller!,
              fit: BoxFit.cover,
              onDetect: _onDetect,
            )
          else
            _buildErrorWidget(),

          // 스캔 영역 오버레이
          if (_permissionGranted && _controller != null && _errorMessage == null)
            Center(
              child: Container(
                width: scanSize,
                height: scanSize,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.primarySeed,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),

          // 상단 바
          SafeArea(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  const Expanded(
                    child: Text(
                      'QR코드 스캔',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  if (_permissionGranted && _controller != null)
                    IconButton(
                      icon: Icon(
                        _flashOn ? Icons.flash_on : Icons.flash_off,
                        color: Colors.white,
                      ),
                      onPressed: _toggleFlash,
                    ),
                ],
              ),
            ),
          ),

          // 하단 안내 텍스트
          if (_permissionGranted && _controller != null && _errorMessage == null)
            Positioned(
              bottom: 100,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: const Text(
                  'QR코드를 스캔 영역에 맞춰주세요',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
