import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/product.dart';
import '../../models/prepayment_virtual_product.dart';
import '../../models/prepayment_product_link.dart';
import '../../providers/product_provider.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/prepayment_product_link_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/toast_utils.dart';
import '../../utils/app_colors.dart';

class CreateLinkDialog extends ConsumerStatefulWidget {
  const CreateLinkDialog({Key? key}) : super(key: key);

  @override
  ConsumerState<CreateLinkDialog> createState() => _CreateLinkDialogState();
}

class _CreateLinkDialogState extends ConsumerState<CreateLinkDialog> with TickerProviderStateMixin {
  final _productSearchController = TextEditingController();
  final _prepaymentSearchController = TextEditingController();
  
  Product? _selectedProduct;
  PrepaymentVirtualProduct? _selectedPrepayment;
  
  String _productSearch = '';
  String _prepaymentSearch = '';
  
  bool _isCreating = false;
  
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _productSearchController.dispose();
    _prepaymentSearchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  List<Product> _getFilteredProducts(List<Product> products) {
    final filtered = _productSearch.isEmpty 
        ? products 
        : products.where((p) => 
            p.name.toLowerCase().contains(_productSearch.toLowerCase())
          ).toList();
    
    // 연동된 상품들을 하단으로 이동
    final links = ref.read(prepaymentProductLinkNotifierProvider);
    final linkedProductIds = links.map((l) => l.productId).toSet();
    
    final available = filtered.where((p) => !linkedProductIds.contains(p.id)).toList();
    final linked = filtered.where((p) => linkedProductIds.contains(p.id)).toList();
    
    return [...available, ...linked];
  }

  List<PrepaymentVirtualProduct> _getFilteredPrepayments(List<PrepaymentVirtualProduct> prepayments) {
    final filtered = _prepaymentSearch.isEmpty 
        ? prepayments 
        : prepayments.where((p) => 
            p.name.toLowerCase().contains(_prepaymentSearch.toLowerCase())
          ).toList();
    
    // 연동된 선입금 상품들을 하단으로 이동
    final links = ref.read(prepaymentProductLinkNotifierProvider);
    final linkedVirtualIds = links.map((l) => l.virtualProductId).toSet();
    
    final available = filtered.where((p) => !linkedVirtualIds.contains(p.id)).toList();
    final linked = filtered.where((p) => linkedVirtualIds.contains(p.id)).toList();
    
    return [...available, ...linked];
  }

  bool _isProductLinked(int productId) {
    final links = ref.read(prepaymentProductLinkNotifierProvider);
    return links.any((l) => l.productId == productId);
  }

  bool _isPrepaymentLinked(int? prepaymentId) {
    if (prepaymentId == null) return false;
    final links = ref.read(prepaymentProductLinkNotifierProvider);
    return links.any((l) => l.virtualProductId == prepaymentId);
  }

  Future<void> _createLink() async {
    if (_selectedProduct == null || _selectedPrepayment == null) return;

    setState(() {
      _isCreating = true;
    });

    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        ToastUtils.showError(context, '워크스페이스를 선택해주세요.');
        return;
      }

      final link = PrepaymentProductLink(
        productId: _selectedProduct!.id!,
        virtualProductId: _selectedPrepayment!.id,
        eventId: currentWorkspace.id,
        linkedAt: DateTime.now(),
      );

      await ref.read(prepaymentProductLinkNotifierProvider.notifier).addLink(link);

      if (mounted) {
        ToastUtils.showSuccess(context, '연동이 생성되었습니다.');
        Navigator.of(context).pop();
      }
    } catch (e) {
      ToastUtils.showError(context, '연동 생성 실패: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final productsAsync = ref.watch(productNotifierProvider);
    final prepaymentsAsync = ref.watch(prepaymentVirtualProductNotifierProvider);

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                const Expanded(
                  child: Center(
                    child: Text(
                      '새 연동 생성',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 48), // IconButton 크기만큼 공간 확보
              ],
            ),
            const SizedBox(height: 16),

            // 선택된 항목 카드
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primarySeed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primarySeed.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '선택된 항목',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('상품', style: TextStyle(fontSize: 12, color: Colors.grey)),
                              Text(
                                _selectedProduct?.name ?? '선택되지 않음',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: _selectedProduct != null ? FontWeight.w500 : FontWeight.normal,
                                  color: _selectedProduct != null ? Colors.black : Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward, color: Colors.grey),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('선입금', style: TextStyle(fontSize: 12, color: Colors.grey)),
                              Text(
                                _selectedPrepayment?.name ?? '선택되지 않음',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: _selectedPrepayment != null ? FontWeight.w500 : FontWeight.normal,
                                  color: _selectedPrepayment != null ? Colors.black : Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: (_selectedProduct != null && _selectedPrepayment != null && !_isCreating)
                          ? _createLink
                          : null,
                      icon: _isCreating 
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.link),
                      label: Text(_isCreating ? '연동 생성 중...' : '상품 연결'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade600,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 탭바 (선택된 항목 카드 밑으로 이동)
            TabBar(
              controller: _tabController,
              labelColor: AppColors.primarySeed,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppColors.primarySeed,
              tabs: const [
                Tab(text: '상품 선택'),
                Tab(text: '선입금 상품 선택'),
              ],
            ),
            const SizedBox(height: 16),

            // 탭 컨텐츠
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // 첫 번째 탭: 상품 선택
                  _buildProductTab(productsAsync),
                  // 두 번째 탭: 선입금 상품 선택
                  _buildPrepaymentTab(prepaymentsAsync),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductTab(ProductState productsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _productSearchController,
          decoration: const InputDecoration(
            hintText: '상품 검색...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _productSearch = value;
            });
          },
        ),
        const SizedBox(height: 8),
        Expanded(
          child: productsAsync.isLoading
              ? const Center(child: CircularProgressIndicator())
              : productsAsync.errorMessage != null
                  ? Center(child: Text('오류: ${productsAsync.errorMessage}'))
                  : Builder(
                      builder: (context) {
                        final filteredProducts = _getFilteredProducts(productsAsync.products);
                        return ListView.builder(
                          itemCount: filteredProducts.length,
                          itemBuilder: (context, index) {
                            final product = filteredProducts[index];
                            final category = ref.read(categoryByIdProvider(product.categoryId));
                            final isSelected = _selectedProduct?.id == product.id;
                            final isLinked = _isProductLinked(product.id!);

                            return Container(
                              color: isLinked ? Colors.grey.shade100 : null,
                              child: ListTile(
                                selected: isSelected,
                                enabled: !isLinked,
                                title: Text(
                                  category != null ? '${category.name}-${product.name}' : product.name,
                                  style: TextStyle(
                                    color: isLinked ? Colors.grey : null,
                                  ),
                                ),
                                subtitle: Text(
                                  isLinked ? '이미 연동됨' : '가격: ${product.price}원',
                                  style: TextStyle(
                                    color: isLinked ? Colors.grey : null,
                                  ),
                                ),
                                onTap: isLinked ? null : () {
                                  setState(() {
                                    _selectedProduct = isSelected ? null : product;
                                  });
                                  // 상품 선택 시 자동으로 다음 탭으로 이동
                                  if (!isSelected) {
                                    _tabController.animateTo(1);
                                  }
                                },
                                trailing: isLinked
                                    ? const Icon(Icons.link, color: Colors.grey)
                                    : (isSelected ? const Icon(Icons.check, color: AppColors.primarySeed) : null),
                              ),
                            );
                          },
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildPrepaymentTab(PrepaymentVirtualProductState prepaymentsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _prepaymentSearchController,
          decoration: const InputDecoration(
            hintText: '선입금 상품 검색...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _prepaymentSearch = value;
            });
          },
        ),
        const SizedBox(height: 8),
        Expanded(
          child: prepaymentsAsync.isLoading
              ? const Center(child: CircularProgressIndicator())
              : prepaymentsAsync.error != null
                  ? Center(child: Text('오류: ${prepaymentsAsync.error}'))
                  : Builder(
                      builder: (context) {
                        final filteredPrepayments = _getFilteredPrepayments(prepaymentsAsync.virtualProducts);
                        return ListView.builder(
                          itemCount: filteredPrepayments.length,
                          itemBuilder: (context, index) {
                            final prepayment = filteredPrepayments[index];
                            final isSelected = _selectedPrepayment?.id == prepayment.id;
                            final isLinked = _isPrepaymentLinked(prepayment.id);

                            return Container(
                              color: isLinked ? Colors.grey.shade100 : null,
                              child: ListTile(
                                selected: isSelected,
                                enabled: !isLinked,
                                title: Text(
                                  prepayment.name,
                                  style: TextStyle(
                                    color: isLinked ? Colors.grey : null,
                                  ),
                                ),
                                subtitle: isLinked ? Text(
                                  '이미 연동됨',
                                  style: TextStyle(
                                    color: Colors.grey,
                                  ),
                                ) : null,
                                onTap: isLinked ? null : () {
                                  setState(() {
                                    _selectedPrepayment = isSelected ? null : prepayment;
                                  });
                                },
                                trailing: isLinked
                                    ? const Icon(Icons.link, color: Colors.grey)
                                    : (isSelected ? const Icon(Icons.check, color: AppColors.primarySeed) : null),
                              ),
                            );
                          },
                        );
                      },
                    ),
        ),
      ],
    );
  }
}
