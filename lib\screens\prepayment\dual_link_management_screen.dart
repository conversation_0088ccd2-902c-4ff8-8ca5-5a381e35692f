import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/prepayment_product_link_provider.dart';
import '../../models/prepayment_product_link.dart';
import '../../providers/category_provider.dart';
import '../../utils/toast_utils.dart';
import '../../utils/app_colors.dart';
import '../../widgets/app_bar_styles.dart';
import 'create_link_dialog.dart';

/// 듀얼 리스트 기반 선입금 상품 ↔ 실제 상품 연동 관리 화면 (1차 경량 버전)
class DualLinkManagementScreen extends ConsumerStatefulWidget {
  const DualLinkManagementScreen({super.key});

  @override
  ConsumerState<DualLinkManagementScreen> createState() => _DualLinkManagementScreenState();
}

class _DualLinkManagementScreenState extends ConsumerState<DualLinkManagementScreen> {
  bool _initialLoading = true;
  String? _initialError;

  @override
  void initState() {
    super.initState();
    // 최초 로딩: 기존 탭에서 하던 load 호출을 여기에서 수행
    Future.microtask(() async {
      try {
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
        await ref.read(productNotifierProvider.notifier).loadProducts();
        await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
      } catch (e) {
        _initialError = e.toString();
      } finally {
        if (mounted) setState(() => _initialLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final vpState = ref.watch(prepaymentVirtualProductNotifierProvider);
    final productsState = ref.watch(productNotifierProvider);
    final links = ref.watch(prepaymentProductLinkNotifierProvider);

    if (_initialLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }
    if (_initialError != null) {
      return Scaffold(
        appBar: AppBar(title: Builder(builder: (ctx)=> Text('연동 관리', style: AppBarStyles.of(ctx)))),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 40),
              const SizedBox(height: 12),
              Text('초기 로딩 실패\n$_initialError', textAlign: TextAlign.center),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() { _initialLoading = true; _initialError = null; });
                  Future.microtask(() async {
                    try {
                      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
                      await ref.read(productNotifierProvider.notifier).loadProducts();
                      await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
                    } catch (e) {
                      _initialError = e.toString();
                    } finally {
                      if (mounted) setState(() => _initialLoading = false);
                    }
                  });
                },
                child: const Text('재시도'),
              )
            ],
          ),
        ),
      );
    }



    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 통계 정보
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      '선입금 상품',
                      vpState.virtualProducts.length.toString(),
                      Icons.payment,
                      AppColors.primarySeed,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                      '실제 상품',
                      productsState.products.length.toString(),
                      Icons.inventory,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                      '연동된 링크',
                      links.length.toString(),
                      Icons.link,
                      Colors.green,
                    ),
                  ),
                ],
              ),
            ),

            // 새 연동 생성 버튼
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => const CreateLinkDialog(),
                    );
                  },
                  icon: const Icon(Icons.add_link),
                  label: const Text('새 연동 생성'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),
            const Divider(),

            // 연동된 리스트
            Expanded(
              child: _buildLinksList(links),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinksList(List<PrepaymentProductLink> links) {
    if (links.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.link_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '연동된 상품이 없습니다.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '위의 "새 연동 생성" 버튼을 눌러 연동을 만들어보세요.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: links.length,
      itemBuilder: (context, index) {
        final link = links[index];
        final virtualProduct = ref.read(prepaymentVirtualProductNotifierProvider)
            .virtualProducts.firstWhere((vp) => vp.id == link.virtualProductId);
        final product = ref.read(productNotifierProvider)
            .products.firstWhere((p) => p.id == link.productId);
        final category = ref.read(categoryByIdProvider(product.categoryId));

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const Icon(Icons.link, color: AppColors.primarySeed),
            title: Text(
              category != null ? '${category.name} - ${product.name}' : product.name,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            subtitle: Text('연동된 선입금: ${virtualProduct.name}'),
            trailing: IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('연동 해제'),
                    content: const Text('이 연동을 해제하시겠습니까?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('취소'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('해제'),
                      ),
                    ],
                  ),
                );

                if (confirmed == true) {
                  try {
                    await ref.read(prepaymentProductLinkNotifierProvider.notifier)
                        .removeLink(link.virtualProductId, link.productId);
                    if (mounted) {
                      ToastUtils.showSuccess(context, '연동이 해제되었습니다.');
                    }
                  } catch (e) {
                    if (mounted) {
                      ToastUtils.showError(context, '연동 해제 중 오류가 발생했습니다: $e');
                    }
                  }
                }
              },
              tooltip: '연동 해제',
            ),
          ),
        );
      },
    );
  }

}
