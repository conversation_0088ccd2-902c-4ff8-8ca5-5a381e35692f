import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dual_link_management_screen.dart';

/// 기존 긴 Stateful 위젯을 듀얼 리스트 화면으로 교체.
/// Hot reload 시 기존 State 필드 참조 에러를 방지하려고 동일한 Stateful 구조 유지.
class PrepaymentProductLinkTab extends ConsumerStatefulWidget {
  const PrepaymentProductLinkTab({super.key});

  @override
  ConsumerState<PrepaymentProductLinkTab> createState() => _PrepaymentProductLinkTabState();
}

class _PrepaymentProductLinkTabState extends ConsumerState<PrepaymentProductLinkTab> {
  // 이전 위젯에서 사용되던 필드들 (hot reload state migration 문제 방지용 더미)
  // ignore: unused_field
  bool _isLoading = false; // legacy placeholder
  // ignore: unused_field
  String? _errorMessage; // legacy placeholder

  @override
  Widget build(BuildContext context) {
    // 단순 위임
    return const DualLinkManagementScreen();
  }
}
