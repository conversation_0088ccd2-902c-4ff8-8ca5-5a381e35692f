import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/unified_workspace_provider.dart';
import '../../providers/prepayment_tab_provider.dart';
import '../../utils/app_colors.dart';
import '../inventory/prepayment_tab.dart';
import 'prepayment_product_link_tab.dart';

/// 선입금 화면
///
/// 선입금 관리를 하나의 화면에서 관리하는 2탭 구조의 화면입니다.
/// - 좌측 탭: 선입금 목록 (기존 PrepaymentTab 기반)
/// - 우측 탭: 선입금-상품 연동 관리 (기존 PrepaymentProductLinkScreen 기반)
/// - 통합된 네비게이션 제공
/// - 슬라이드 탭 전환 지원
class PrepaymentScreen extends ConsumerStatefulWidget {
  final int initialTabIndex;

  const PrepaymentScreen({
    super.key,
    this.initialTabIndex = 0,
  });

  @override
  ConsumerState<PrepaymentScreen> createState() => _PrepaymentScreenState();
}

class _PrepaymentScreenState extends ConsumerState<PrepaymentScreen>
    with SingleTickerProviderStateMixin, RestorationMixin {
  late TabController _tabController;

  @override
  String? get restorationId => 'prepayment_screen';
  
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialTabIndex,
    );
    // 초기 provider 동기화
    // ignore: invalid_use_of_protected_member
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(prepaymentTabIndexProvider.notifier).state = _tabController.index;
      }
    });
    
    // 탭 변경 시 앱바 다시 빌드하기 위해 리스너 추가
    _tabController.addListener(() {
      if (mounted) {
        // 탭 인덱스 업데이트
        ref.read(prepaymentTabIndexProvider.notifier).state = _tabController.index;
        setState(() {
          // 탭 변경 시 State 업데이트
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final currentWorkspace = ref.watch(currentWorkspaceProvider);
        
        // 현재 행사가 선택되지 않은 경우
        if (currentWorkspace == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('선입금'),
              centerTitle: true,
              backgroundColor: AppColors.primarySeed,
              foregroundColor: AppColors.onPrimary,
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.event_busy,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '행사를 선택해주세요',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    '좌측 상단 메뉴에서 행사를 선택하거나\n새 행사를 생성할 수 있습니다.',
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

  // 링크 목록 버튼은 상위 InventoryScreen AppBar에서 처리
    return SafeArea(
      top: false, // AppBar 이미 있으므로 상단 패딩 제거해 빈 공간 제거
      child: Column(
              children: [
                // 기록&통계 화면과 동일한 스타일의 탭바 컨테이너
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: Theme.of(context).colorScheme.primary,
                    unselectedLabelColor: Colors.grey.shade600,
                    indicatorColor: Theme.of(context).colorScheme.primary,
                    indicatorWeight: 3,
                    indicatorSize: TabBarIndicatorSize.label,
                    labelStyle: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    onTap: (idx) {
                      ref.read(prepaymentTabIndexProvider.notifier).state = idx;
                      setState(() {}); // 즉시 AppBar actions 반영
                    },
                    tabs: const [
                      Tab(text: '선입금 목록'),
                      Tab(text: '상품 연동 관리'),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      const PrepaymentTab(),
                      PrepaymentProductLinkTab(),
                    ],
                  ),
                ),
              ],
            ),
  );
      },
    );
  }
}
