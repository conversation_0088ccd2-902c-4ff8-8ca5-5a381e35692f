import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../../models/product.dart';
import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/image_utils.dart';
import '../../utils/permission_utils.dart';
import '../../utils/app_colors.dart';
import '../../widgets/registration_complete_page.dart';
import '../../widgets/image_crop_widget.dart';
import '../sale/sale_screen.dart';
// 로컬 전용 모드: 사용하지 않는 import 제거됨
import '../../widgets/app_bar_styles.dart';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../../utils/image_cache.dart';
import '../../widgets/product_image.dart';

/// 상품 입력 항목을 관리하는 클래스
class ProductEntry {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();
  String? selectedSeller;
  int? selectedCategoryId;
  String? originalPaddedImagePath; // 크롭을 위한 원본 패딩 이미지 경로
  String? croppedImagePath; // 크롭된 이미지 경로

  void dispose() {
    nameController.dispose();
    priceController.dispose();
    quantityController.dispose();
  }
}

/// 수동 대량 상품 등록 화면
/// 상품명부터 직접 입력하여 여러 상품을 한번에 등록
class ManualBulkProductRegistrationScreen extends ConsumerStatefulWidget {
  const ManualBulkProductRegistrationScreen({super.key});

  @override
  ConsumerState<ManualBulkProductRegistrationScreen> createState() => _ManualBulkProductRegistrationScreenState();
}

class _ManualBulkProductRegistrationScreenState extends ConsumerState<ManualBulkProductRegistrationScreen> {
  final List<ProductEntry> _productEntries = [];
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    // 기본적으로 3개의 빈 상품 항목으로 시작
    _addProductEntry();
    _addProductEntry();
    _addProductEntry();

    // 카테고리와 판매자 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoryNotifierProvider.notifier).loadCategories();
      _initializeDefaults();
    });
  }

  /// 기본값들을 초기화합니다.
  void _initializeDefaults() {
    // 대표 판매자 설정
    final sellerState = ref.read(sellerNotifierProvider);
    final defaultSeller = sellerState.defaultSeller;

    // 기본 카테고리 설정
    final categoriesAsync = ref.read(categoryNotifierProvider);
    int? defaultCategoryId;
    if (categoriesAsync.hasValue && categoriesAsync.value!.isNotEmpty) {
      final sortedCategories = [...categoriesAsync.value!]
        ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
      defaultCategoryId = sortedCategories.first.id;
    }

    // 모든 항목에 기본값 적용
    setState(() {
      for (final entry in _productEntries) {
        if (defaultSeller != null) {
          entry.selectedSeller = defaultSeller.name;
        }
        if (defaultCategoryId != null) {
          entry.selectedCategoryId = defaultCategoryId;
        }
      }
    });
  }

  @override
  void dispose() {
    // 모든 컨트롤러 정리
    for (final entry in _productEntries) {
      entry.dispose();
    }
    super.dispose();
  }

  void _addProductEntry() {
    final newEntry = ProductEntry();

    // 기본값 설정
    final sellerState = ref.read(sellerNotifierProvider);
    final defaultSeller = sellerState.defaultSeller;
    if (defaultSeller != null) {
      newEntry.selectedSeller = defaultSeller.name;
    }

    final categoriesAsync = ref.read(categoryNotifierProvider);
    if (categoriesAsync.hasValue && categoriesAsync.value!.isNotEmpty) {
      final sortedCategories = [...categoriesAsync.value!]
        ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
      newEntry.selectedCategoryId = sortedCategories.first.id;
    }

    setState(() {
      _productEntries.add(newEntry);
    });
  }

  /// 여러 개의 상품 항목을 한번에 추가합니다.
  void _addMultipleProductEntries(int count) {
    for (int i = 0; i < count; i++) {
      _addProductEntry();
    }
  }

  void _removeProductEntry(int index) {
    if (_productEntries.length > 1) {
      setState(() {
        _productEntries[index].dispose();
        _productEntries.removeAt(index);
      });
    }
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx)=> Text('대량 상품 등록', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          // 상품 추가 버튼
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddProductDialog,
            tooltip: '상품 추가',
          ),
          // 일괄 입력 버튼
          IconButton(
            icon: const Icon(Icons.auto_fix_high),
            onPressed: _showQuickInputDialog,
            tooltip: '일괄 입력',
          ),
          // 등록 버튼
          IconButton(
            icon: _isProcessing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.check),
            onPressed: _isProcessing ? null : _registerProducts,
            tooltip: '등록',
          ),
        ],
      ),
      body: SafeArea(
        child: _buildProductList(),
      ),
    );
  }

  /// 상품 추가 다이얼로그를 표시합니다.
  void _showAddProductDialog() {
    final countController = TextEditingController(text: '1');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('상품 추가'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('추가할 상품 개수를 입력하세요.'),
            const SizedBox(height: 16),
            TextField(
              controller: countController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.done,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: '개수',
                hintText: '1',
                suffixText: '개',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              final count = int.tryParse(countController.text) ?? 1;
              if (count > 0 && count <= 50) { // 최대 50개 제한
                _addMultipleProductEntries(count);
                Navigator.of(context).pop();
              } else {
                ToastUtils.showError(context, '1~50개 사이의 숫자를 입력해주세요.');
              }
            },
            child: const Text('추가'),
          ),
        ],
      ),
    );
  }

  /// 상품 리스트를 구성합니다.
  Widget _buildProductList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _productEntries.length,
      itemBuilder: (context, index) {
        return _buildProductCard(index);
      },
    );
  }

  /// 개별 상품 카드를 구성합니다.
  Widget _buildProductCard(int index) {
    final entry = _productEntries[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 중앙정렬로 변경
          children: [
            // 왼쪽: 이미지와 상품 번호 (중앙정렬)
            Column(
              mainAxisAlignment: MainAxisAlignment.center, // 중앙정렬 추가
              children: [
                // 이미지 미리보기
                GestureDetector(
                  onTap: () => _selectImage(entry),
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[50],
                    ),
                    child: entry.croppedImagePath != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(entry.croppedImagePath!),
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(Icons.add_a_photo, color: Colors.grey[400], size: 24),
                  ),
                ),
                const SizedBox(height: 8),
                // 상품 번호
                Text(
                  '상품 ${index + 1}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 12),

            // 오른쪽: 입력 필드들
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 상품명과 삭제 버튼
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: entry.nameController,
                          decoration: InputDecoration(
                            labelText: '상품명 *',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            hintText: '상품명을 입력하세요',
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 14),
                          textInputAction: TextInputAction.next,
                        ),
                      ),
                      if (_productEntries.length > 1) ...[
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.delete_outline, color: Colors.red, size: 20),
                          onPressed: () => _removeProductEntry(index),
                          tooltip: '삭제',
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),

                  // 가격과 수량 입력
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: entry.priceController,
                          decoration: InputDecoration(
                            labelText: '가격 *',
                            suffixText: '원',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 14),
                          keyboardType: TextInputType.number,
                          textInputAction: TextInputAction.next,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: entry.quantityController,
                          decoration: InputDecoration(
                            labelText: '수량 *',
                            suffixText: '개',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 14),
                          keyboardType: TextInputType.number,
                          textInputAction: TextInputAction.next,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // 카테고리와 판매자 선택
                  Row(
                    children: [
                      Expanded(
                        child: _buildCategoryDropdown(entry),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildSellerDropdown(entry),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 카테고리 드롭다운을 구성합니다.
  Widget _buildCategoryDropdown(ProductEntry entry) {
    return Consumer(
      builder: (context, ref, child) {
        final categoriesAsync = ref.watch(categoryNotifierProvider);

        return categoriesAsync.when(
          loading: () => const SizedBox(
            height: 56,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stack) => Container(
            height: 56,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(child: Text('오류')),
          ),
          data: (categories) {
            if (categories.isEmpty) {
              return Container(
                height: 56,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(child: Text('카테고리 없음')),
              );
            }

            return DropdownButtonFormField<int>(
              value: entry.selectedCategoryId,
              decoration: InputDecoration(
                labelText: '카테고리 *',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 13, color: Colors.black),
              isExpanded: true, // 오버플로우 방지
              items: categories.map((category) {
                return DropdownMenuItem<int>(
                  value: category.id,
                  child: Text(
                    category.name,
                    overflow: TextOverflow.ellipsis, // 텍스트 오버플로우 처리
                    maxLines: 1,
                  ),
                );
              }).toList(),
              onChanged: (int? value) {
                setState(() {
                  entry.selectedCategoryId = value;
                });
              },
            );
          },
        );
      },
    );
  }

  /// 판매자 드롭다운을 구성합니다.
  Widget _buildSellerDropdown(ProductEntry entry) {
    return Consumer(
      builder: (context, ref, child) {
        final sellerState = ref.watch(sellerNotifierProvider);
        // 로컬 데이터 우선 사용 (로딩 상태 무시)
        final sellers = sellerState.sellers;

        if (sellers.isEmpty) {
          return Container(
            height: 56,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                '판매자 없음',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          );
        }

        return DropdownButtonFormField<String>(
          value: entry.selectedSeller,
          decoration: InputDecoration(
            labelText: '판매자 *',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 14, color: Colors.black),
          isExpanded: true, // 오버플로우 방지
          items: sellers.map((seller) {
            final isDefault = seller.isDefault;
            return DropdownMenuItem<String>(
              value: seller.name,
              child: Text(
                seller.name,
                style: TextStyle(
                  fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                  color: isDefault ? AppColors.primarySeed : null, // 기본 판매자는 앱 테마 색상으로 표시
                ),
                overflow: TextOverflow.ellipsis, // 텍스트 오버플로우 처리
                maxLines: 1,
              ),
            );
          }).toList(),
          onChanged: (String? value) {
            setState(() {
              entry.selectedSeller = value;
            });
          },
        );
      },
    );
  }





  /// 이미지를 선택하고 크롭합니다 (기존 상품 등록과 동일한 방식).
  Future<void> _selectImage(ProductEntry entry) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // 1. 흰색 캔버스+중앙 배치 적용
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await getTemporaryDirectory();
        final paddedPath = path.join(tempDir.path, 'padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        final paddedFile = await File(paddedPath).writeAsBytes(paddedBytes);

        // 원본 패딩 이미지 경로 저장 (재크롭을 위해)
        entry.originalPaddedImagePath = paddedFile.path;

        // 2. 크롭 다이얼로그(라운드 사각형, 1:1, 오버레이/로딩/원본노출방지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: paddedFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null && mounted) {
          setState(() {
            entry.croppedImagePath = croppedFile.path;
          });
        } else {
          // 크롭을 취소한 경우 이미지를 표시하지 않음
          setState(() {
            entry.croppedImagePath = null;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('이미지 선택 실패', error: e);
      if (mounted) {
        ToastUtils.showError(context, '이미지 선택에 실패했습니다.');
      }
    }
  }

  /// 일괄 입력 다이얼로그를 표시합니다.
  void _showQuickInputDialog() {
    final priceController = TextEditingController();
    final quantityController = TextEditingController();
    String? selectedSeller;
    int? selectedCategoryId;

    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final sellerState = ref.watch(sellerNotifierProvider);
          final sellers = sellerState.sellers;
          final categoriesAsync = ref.watch(categoryNotifierProvider);

          return AlertDialog(
            title: const Text('일괄 입력'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('모든 상품에 동일한 값을 적용합니다.'),
                  const SizedBox(height: 16),

                  // 가격 입력
                  TextField(
                    controller: priceController,
                    decoration: const InputDecoration(
                      labelText: '가격',
                      suffixText: '원',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 수량 입력
                  TextField(
                    controller: quantityController,
                    decoration: const InputDecoration(
                      labelText: '수량',
                      suffixText: '개',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 카테고리 선택
                  categoriesAsync.when(
                    loading: () => const SizedBox(
                      height: 56,
                      child: Center(child: CircularProgressIndicator()),
                    ),
                    error: (error, stack) => Container(
                      height: 56,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.red),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Center(child: Text('카테고리 로드 오류')),
                    ),
                    data: (categories) {
                      if (categories.isEmpty) {
                        return Container(
                          height: 56,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Center(child: Text('카테고리 없음')),
                        );
                      }

                      return DropdownButtonFormField<int>(
                        value: selectedCategoryId,
                        decoration: const InputDecoration(
                          labelText: '카테고리',
                          border: OutlineInputBorder(),
                        ),
                        items: categories.map((category) {
                          return DropdownMenuItem<int>(
                            value: category.id,
                            child: Text(category.name),
                          );
                        }).toList(),
                        onChanged: (int? value) {
                          selectedCategoryId = value;
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 16),

                  // 판매자 선택
                  if (sellers.isNotEmpty)
                    DropdownButtonFormField<String>(
                      value: selectedSeller,
                      decoration: const InputDecoration(
                        labelText: '판매자',
                        border: OutlineInputBorder(),
                      ),
                      items: sellers.map((seller) {
                        final isDefault = seller.isDefault;
                        return DropdownMenuItem<String>(
                          value: seller.name,
                          child: Text(
                            seller.name,
                            style: TextStyle(
                              fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                              color: isDefault ? AppColors.primarySeed : null, // 기본 판매자는 앱 테마 색상으로 표시
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? value) {
                        selectedSeller = value;
                      },
                    ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('취소'),
              ),
              ElevatedButton(
                onPressed: () {
                  _applyQuickInput(
                    priceController.text,
                    quantityController.text,
                    selectedSeller,
                    selectedCategoryId,
                  );
                  Navigator.of(context).pop();
                },
                child: const Text('적용'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 일괄 입력을 적용합니다.
  void _applyQuickInput(String price, String quantity, String? seller, int? categoryId) {
    setState(() {
      for (final entry in _productEntries) {
        if (price.isNotEmpty) {
          entry.priceController.text = price;
        }
        if (quantity.isNotEmpty) {
          entry.quantityController.text = quantity;
        }
        if (seller != null) {
          entry.selectedSeller = seller;
        }
        if (categoryId != null) {
          entry.selectedCategoryId = categoryId;
        }
      }
    });
  }

  /// 상품들을 등록합니다.
  Future<void> _registerProducts() async {
    if (_isProcessing) return;

    // 권한 체크
    final hasPermission = await PermissionUtils.checkModifyPermission(context, ref);
    if (!hasPermission) {
      return;
    }

    // 현재 워크스페이스 확인
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      ToastUtils.showError(context, '현재 선택된 행사 워크스페이스가 없습니다. 행사 워크스페이스를 선택해주세요.');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final List<Product> productsToAdd = [];
      final List<String> incompleteProducts = [];

      LoggerUtils.logInfo('상품 데이터 처리 시작: 총 ${_productEntries.length}개 항목');

      for (int i = 0; i < _productEntries.length; i++) {
        final entry = _productEntries[i];

        // 빈 항목 건너뛰기
        if (entry.nameController.text.trim().isEmpty) {
          LoggerUtils.logDebug('빈 항목 건너뛰기: 인덱스 $i');
          continue;
        }

        LoggerUtils.logDebug('상품 데이터 처리 시작: 인덱스 $i, 상품명: "${entry.nameController.text.trim()}"');

        // 필수 데이터 검증
        bool isIncomplete = false;
        final name = entry.nameController.text.trim();
        final priceText = entry.priceController.text.trim();
        final quantityText = entry.quantityController.text.trim();
        final seller = entry.selectedSeller;
        final categoryId = entry.selectedCategoryId;

        if (priceText.isEmpty) {
          LoggerUtils.logWarning('가격이 입력되지 않음: $name');
          isIncomplete = true;
        }

        if (quantityText.isEmpty) {
          LoggerUtils.logWarning('수량이 입력되지 않음: $name');
          isIncomplete = true;
        }

        if (seller == null || seller.isEmpty) {
          LoggerUtils.logWarning('판매자가 선택되지 않음: $name');
          isIncomplete = true;
        }

        if (categoryId == null) {
          LoggerUtils.logWarning('카테고리가 선택되지 않음: $name');
          isIncomplete = true;
        }

        if (isIncomplete) {
          incompleteProducts.add('${i + 1}. $name');
          LoggerUtils.logWarning('불완전한 상품 데이터: $name (인덱스: $i)');
          continue;
        }

        LoggerUtils.logDebug('상품 데이터 검증 완료: $name');

        final price = int.tryParse(priceText) ?? 0;
        final quantity = int.tryParse(quantityText) ?? 0;

        if (price <= 0) {
          LoggerUtils.logWarning('가격이 0 이하: $name');
          incompleteProducts.add('${i + 1}. $name (가격 오류)');
          continue;
        }

        if (quantity <= 0) {
          LoggerUtils.logWarning('수량이 0 이하: $name');
          incompleteProducts.add('${i + 1}. $name (수량 오류)');
          continue;
        }

        LoggerUtils.logDebug('가격/수량 검증 완료: $name (가격: $price, 수량: $quantity)');

        // 이미지 저장 (크롭된 이미지 파일 경로 사용)
        String? imagePath;
        if (entry.croppedImagePath != null) {
          LoggerUtils.logInfo('이미지 저장 시작: $name, 크롭된 파일: ${entry.croppedImagePath}');
          imagePath = await _saveImageFileToInternalStorage(name, entry.croppedImagePath!);

          // 저장된 파일 검증
          if (imagePath != null) {
            final savedFile = File(imagePath);
            final exists = await savedFile.exists();
            final size = exists ? await savedFile.length() : 0;
            LoggerUtils.logInfo('이미지 저장 검증: $name → 존재: $exists, 크기: ${size}bytes, 경로: $imagePath');

            // 저장된 이미지의 캐시 클리어 (즉시 표시되도록)
            ImageStateCache.clearCacheForPath(imagePath);
            ProductImage.clearCacheForPath(imagePath);
            PaintingBinding.instance.imageCache.clear();
          } else {
            LoggerUtils.logError('이미지 저장 실패: $name');
          }
        }

        LoggerUtils.logInfo('이미지 저장 결과: $name → $imagePath');

        // 상품명 검증 및 정리
        final productName = name.trim();
        if (productName.isEmpty) {
          LoggerUtils.logWarning('상품명이 비어있음, 건너뜀: ${i + 1}번째 상품');
          continue; // 빈 상품명인 경우 건너뜀
        }

        // 새 상품 생성 (일반 등록과 같은 방식: ID를 null로 설정하여 DB가 자동 생성하도록 함)
        LoggerUtils.logInfo('상품 데이터 생성: $productName (DB 자동 ID 생성 방식, imagePath: $imagePath)');

        final product = Product(
          id: null, // null로 설정하여 toMap()에서 제외되고 DB가 auto-increment ID 생성
          name: productName, // 검증된 상품명 사용
          quantity: quantity,
          price: price,
          sellerName: seller,
          categoryId: categoryId!,
          imagePath: imagePath,
          lastServicedDate: null,
          isActive: true,

          eventId: currentWorkspace.id,
        );

        productsToAdd.add(product);
        LoggerUtils.logInfo('상품 데이터 준비 완료 (${i + 1}/${_productEntries.length}): $name (가격: $price원, 수량: $quantity개)');
      }

      if (productsToAdd.isEmpty) {
        LoggerUtils.logWarning('등록할 상품이 없음. 전체 항목 수: ${_productEntries.length}, 불완전한 상품 수: ${incompleteProducts.length}');
        ToastUtils.showError(context, '등록할 수 있는 상품이 없습니다. 상품 정보를 확인해주세요.');
        return;
      }

      LoggerUtils.logInfo('등록 준비 완료: ${productsToAdd.length}개 상품');
      for (int i = 0; i < productsToAdd.length; i++) {
        final p = productsToAdd[i];
        LoggerUtils.logInfo('등록할 상품 ${i + 1}: ${p.name} (DB 자동 ID 생성 방식)');
      }

      if (incompleteProducts.isNotEmpty) {
        final shouldContinue = await _showIncompleteProductsDialog(incompleteProducts, productsToAdd.length);
        if (!shouldContinue) {
          return;
        }
      }

      // 배치 처리로 상품들을 등록 (성능 최적화)
      int successCount = 0;
      final List<String> failedProducts = [];

      // 2단계 하이브리드 방식: 1) 로컬 저장 2) Firebase 배치 업로드
      LoggerUtils.logInfo('2단계 하이브리드 등록 시작: ${productsToAdd.length}개');
      
      final notifier = ref.read(productNotifierProvider.notifier);

      // 1단계: 로컬에만 저장 (Firebase 스킵)
      LoggerUtils.logInfo('1단계: 로컬 저장 시작 (Firebase 스킵)');
      final results = await Future.wait(
        productsToAdd.map((product) async {
          try {
            await notifier.addProduct(product, skipFirebase: true);
            LoggerUtils.logInfo('로컬 저장 성공: ${product.name}');
            return MapEntry(true, product.name);
          } catch (e) {
            LoggerUtils.logWarning('로컬 저장 실패: ${product.name}', error: e);
            return MapEntry(false, product.name);
          }
        }),
        eagerError: false,
      );

      // 결과 집계
      for (final result in results) {
        if (result.key) {
          successCount++;
        } else {
          failedProducts.add(result.value);
        }
      }

      LoggerUtils.logInfo('1단계 완료: 로컬 저장 $successCount개, 실패 ${failedProducts.length}개');

      // 로컬 저장된 상품들의 imagePath 확인 (디버깅용)
      if (successCount > 0) {
        final repository = ref.read(productRepositoryProvider);
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        final recentProducts = await repository.getProductsByEventId(currentWorkspace!.id);
        final recentlyAddedProducts = recentProducts.where((saved) => 
          productsToAdd.any((original) => original.name == saved.name)
        ).toList();

        for (final savedProduct in recentlyAddedProducts) {
          LoggerUtils.logInfo('저장된 상품 이미지 경로: ${savedProduct.name} → ${savedProduct.imagePath}');
          
          // 로컬 파일 존재 여부도 확인
          if (savedProduct.imagePath != null && savedProduct.imagePath!.isNotEmpty) {
            final file = File(savedProduct.imagePath!);
            final exists = await file.exists();
            LoggerUtils.logInfo('로컬 파일 존재 여부: ${savedProduct.imagePath} → $exists');
          }
        }
      }

      LoggerUtils.logInfo('1단계 완료: 로컬 저장 $successCount개, 실패 ${failedProducts.length}개');

      // 로컬 저장 완료 후 즉시 UI 갱신 (새로 저장된 상품들이 바로 표시되도록)
      if (successCount > 0) {
        // 이미지 캐시 클리어 (새로 저장된 이미지들이 바로 표시되도록)
        ImageStateCache.clearCacheForDirectory('/product_images');
        ProductImage.clearGlobalCache(); // ProductImage 위젯의 글로벌 캐시도 클리어
        LoggerUtils.logInfo('이미지 캐시 클리어 완료');

        await notifier.loadProducts(showLoading: false);
        LoggerUtils.logInfo('UI 갱신 완료: 새로 저장된 상품들이 표시됨');
      }

      // 2단계: Firebase 업로드를 백그라운드에서 처리 (로컬 저장 성공한 것들만)
      if (successCount > 0) {
        // 백그라운드에서 Firebase 업로드 처리
        _scheduleBackgroundFirebaseUpload(productsToAdd);
      }

      final message = failedProducts.isEmpty
          ? '총 ${productsToAdd.length}개 상품이 성공적으로 등록되었습니다.'
          : '총 ${productsToAdd.length}개 중 $successCount개 상품이 성공적으로 등록되었습니다. 실패: ${failedProducts.length}개';
      LoggerUtils.logInfo(message);

      // 등록 완료 페이지로 이동
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => RegistrationCompletePage(
              description: message,
              onConfirm: () {
                // POS 편집 화면으로 돌아가기 (편집 모드 활성화)
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const SaleScreen(startInEditMode: true)),
                  (route) => false,
                );
              },
            ),
          ),
          (route) => false,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError('상품 대량 등록 중 오류', error: e, stackTrace: stackTrace);
      if (mounted) {
        ToastUtils.showError(context, '상품 등록 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 미완성 상품 확인 다이얼로그를 표시합니다.
  Future<bool> _showIncompleteProductsDialog(List<String> incompleteProducts, int validCount) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('미완성 상품 발견'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('다음 상품들은 필수 정보가 누락되어 등록되지 않습니다:'),
            const SizedBox(height: 8),
            ...incompleteProducts.map((product) => Text('• $product')),
            const SizedBox(height: 16),
            Text('완성된 ${validCount}개 상품만 등록하시겠습니까?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('계속 등록'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 이미지 파일을 내부 저장소에 저장합니다. (개별 등록과 동일한 안정적인 방식)
  Future<String?> _saveImageFileToInternalStorage(String productName, String croppedImagePath) async {
    try {
      LoggerUtils.logInfo('이미지 저장 시작: $productName, 소스: $croppedImagePath');

      // 현재 워크스페이스 ID 가져오기
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logError('현재 워크스페이스가 선택되지 않았습니다.');
        return null;
      }

      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/product_images/${currentWorkspace.id}');

      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
        LoggerUtils.logInfo('product_images 디렉토리 생성: ${imagesDir.path}');
      }

      final fileName = '${productName}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final targetFile = File('${imagesDir.path}/$fileName');

      // 크롭된 이미지 파일을 바이트로 읽어서 직접 저장 (개별 등록과 동일한 방식)
      final sourceFile = File(croppedImagePath);

      // 소스 파일 존재 여부 확인
      if (!await sourceFile.exists()) {
        LoggerUtils.logError('소스 이미지 파일이 존재하지 않음: $croppedImagePath');
        return null;
      }

      // 파일 바이트를 읽어서 직접 저장 (복사 대신 바이트 처리로 안정성 향상)
      final imageBytes = await sourceFile.readAsBytes();
      await targetFile.writeAsBytes(imageBytes, flush: true);

      // 파일 시스템 동기화를 위한 대기
      await Future.delayed(const Duration(milliseconds: 100));

      // 저장 결과 확인 (여러 번 시도)
      bool fileVerified = false;
      for (int attempt = 1; attempt <= 3; attempt++) {
        if (await targetFile.exists()) {
          final fileSize = await targetFile.length();
          if (fileSize > 0) {
            LoggerUtils.logInfo('이미지 저장 성공: $productName → ${targetFile.path} (${fileSize} bytes)');
            fileVerified = true;
            break;
          }
        }

        if (attempt < 3) {
          LoggerUtils.logWarning('이미지 저장 검증 실패 (시도 $attempt/3): $productName');
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      if (!fileVerified) {
        LoggerUtils.logError('이미지 저장 검증 실패 (3회 시도 후): $productName');
        return null;
      }

      return targetFile.path;
    } catch (e) {
      LoggerUtils.logError('이미지 저장 실패: $productName', error: e);
      return null;
    }
  }

  /// 백그라운드에서 Firebase 업로드를 스케줄링
  void _scheduleBackgroundFirebaseUpload(List<Product> productsToAdd) {
    // 백그라운드에서 실행 (UI 블로킹 방지)
    Future.microtask(() async {
      try {
        LoggerUtils.logInfo('백그라운드 Firebase 업로드 시작: ${productsToAdd.length}개 상품');

        // 최근 저장된 상품들 조회 (eventId로 필터링)
        final repository = ref.read(productRepositoryProvider);
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        final recentProducts = await repository.getProductsByEventId(currentWorkspace!.id);

        // 방금 저장된 상품들 중 이름이 매칭되는 것들만 필터링
        final productsToUpload = recentProducts.where((saved) =>
          productsToAdd.any((original) => original.name == saved.name)
        ).toList();

        if (productsToUpload.isNotEmpty) {
          // 로컬 전용 모드: 서버 동기화 제거됨
          LoggerUtils.logInfo('로컬 전용 모드: 상품 서버 동기화 건너뜀 - ${productsToUpload.length}개 상품');
        }
      } catch (e) {
        LoggerUtils.logWarning('백그라운드 Firebase 업로드 실패 (로컬 저장은 완료됨)', error: e);
        // 백그라운드 실패는 사용자에게 알리지 않음
      }
    });
  }
}
