import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/currency_utils.dart';
import '../../providers/payment_methods_provider.dart';

/// POS 화면의 고정 하단 바
/// 총 합계, 판매 버튼, 주문 내역 토글 버튼을 포함
class FixedBottomBar extends ConsumerWidget {
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final ValueNotifier<Map<int, int>> serviceQuantitiesNotifier;
  final ValueNotifier<int> totalAmountNotifier;
  final ValueNotifier<String?> setDiscountInfoNotifier;
  final bool isOrderListVisible;
  final VoidCallback onToggleOrderList;
  final void Function(String method)? onSellWithMethod;

  const FixedBottomBar({
    super.key,
    required this.productQuantitiesNotifier,
    required this.serviceQuantitiesNotifier,
    required this.totalAmountNotifier,
    required this.setDiscountInfoNotifier,
    required this.isOrderListVisible,
    required this.onToggleOrderList,
    this.onSellWithMethod,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(isTablet ? Dimens.space12 : Dimens.space8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 총 합계와 판매 버튼들 (1층 구조)
              Row(
                children: [
                  // 총 합계 (축소된 크기)
                  Expanded(
                    flex: 2,
                    child: ValueListenableBuilder<int>(
                      valueListenable: totalAmountNotifier,
                      builder: (context, totalAmount, child) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: isTablet ? Dimens.space12 : Dimens.space8,
                            vertical: isTablet ? Dimens.space8 : Dimens.space6,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.onboardingPrimary.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Text(
                                '₩',
                                style: TextStyle(
                                  fontSize: isTablet ? 16 : 14,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.onboardingPrimary,
                                ),
                              ),
                              SizedBox(width: isTablet ? Dimens.space4 : Dimens.space2),
                              Expanded(
                                child: Text(
                                  CurrencyUtils.formatCurrency(totalAmount).replaceFirst('₩', ''), // ₩ 기호 제거
                                  style: TextStyle(
                                    fontSize: isTablet ? 16 : 14,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.onboardingPrimary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),

                  SizedBox(width: isTablet ? Dimens.space12 : Dimens.space8),

                  // 판매 버튼들 (축소된 크기)
                  Expanded(
                    flex: 3,
                    child: _buildSaleButtons(context, ref, isTablet),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSaleButtons(BuildContext context, WidgetRef ref, bool isTablet) {
    final pmState = ref.watch(paymentMethodsProvider);

    return ValueListenableBuilder<Map<int, int>>(
      valueListenable: productQuantitiesNotifier,
      builder: (context, productQuantities, child) {
        return ValueListenableBuilder<Map<int, int>>(
          valueListenable: serviceQuantitiesNotifier,
          builder: (context, serviceQuantities, child) {
            final hasSelectedItems = productQuantities.values.any((q) => q > 0) ||
                serviceQuantities.values.any((q) => q > 0);

            final methods = pmState.activeMethods;

            if (methods.isEmpty) {
              return SizedBox(
                width: double.infinity,
                height: isTablet ? 44 : 40,
                child: ElevatedButton(
                  onPressed: null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.onboardingPrimary.withValues(alpha: 0.3),
                    foregroundColor: Colors.white.withValues(alpha: 0.5),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    '판매',
                    style: TextStyle(
                      fontSize: isTablet ? 16 : 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            }

            // 동적 배치: 1개=전체, 2개=반씩, 3개=1/3씩
            return Row(
              children: [
                for (int i = 0; i < methods.length; i++) ...[
                  if (i > 0) SizedBox(width: isTablet ? Dimens.space8 : Dimens.space4),
                  Expanded(
                    child: SizedBox(
                      height: isTablet ? 44 : 40,
                      child: ElevatedButton(
                        onPressed: hasSelectedItems && onSellWithMethod != null
                            ? () => onSellWithMethod!(methods[i].id)
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: hasSelectedItems
                            ? AppColors.primarySeed
                            : AppColors.onboardingTextSecondary.withValues(alpha: 0.3),
                          foregroundColor: Colors.white,
                          elevation: hasSelectedItems ? 4 : 0,
                          shadowColor: AppColors.primarySeed.withValues(alpha: 0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          methods[i].name,
                          style: TextStyle(
                            fontSize: isTablet ? 14 : 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            );
          },
        );
      },
    );
  }
}
