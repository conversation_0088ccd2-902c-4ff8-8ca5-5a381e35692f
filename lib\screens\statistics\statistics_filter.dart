import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/seller_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../widgets/confirmation_dialog.dart';

/// 통계 화면 필터링 기능을 담당하는 클래스
///
/// 주요 기능:
/// - 판매자 필터
/// - 날짜 범위 필터
/// - 필터 다이얼로그
/// - 필터 상태 관리
class StatisticsFilter {
  /// 필터 다이얼로그를 표시합니다.
  ///
  /// [context]: BuildContext
  /// [selectedSeller]: 현재 선택된 판매자
  /// [selectedDateRange]: 현재 선택된 날짜 범위
  /// 반환값: 필터 변경 여부와 새로운 필터 값들
  static Future<Map<String, dynamic>?> showFilterDialog({
    required BuildContext context,
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) async {
    String tempSelectedSeller = selectedSeller;
    DateTimeRange? tempSelectedDateRange = selectedDateRange;
    final filterChanged = ValueNotifier(false);

    final confirmed = await ConfirmationDialog.show(
      context: context,
      title: '필터 설정',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 판매자 선택
          Consumer(
            builder: (context, ref, child) {
              // 실시간 판매자 목록 감시
              final sellerAsync = ref.watch(sellerNotifierProvider);
              // 로컬 데이터 우선 사용 (로딩 상태 무시)
              final sellerNames = sellerAsync.sellers.map((s) => s.name).toList()..sort();
              final sellers = ['전체 판매자', ...sellerNames];
              return DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: '판매자',
                  prefixIcon: Icon(
                    Icons.person_rounded,
                    color: AppColors.primarySeed,
                  ),
                ),
                value: tempSelectedSeller,
                items: sellers.map((seller) {
                  return DropdownMenuItem(value: seller, child: Text(seller));
                }).toList(),
                onChanged: (value) {
                  tempSelectedSeller = value ?? '전체 판매자';
                  filterChanged.value = !filterChanged.value;
                },
              );
            },
          ),
          const SizedBox(height: Dimens.space16),
          // 날짜 범위 선택
          ValueListenableBuilder(
            valueListenable: filterChanged,
            builder: (context, _, __) {
              return Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.neutral30),
                      borderRadius: BorderRadius.circular(Dimens.radiusM),
                    ),
                    child: ListTile(
                      leading: Icon(
                        Icons.date_range_rounded,
                        color: AppColors.primarySeed,
                      ),
                      title: const Text('날짜 범위'),
                      subtitle: Text(
                        tempSelectedDateRange != null
                            ? '${tempSelectedDateRange!.start.year}-${tempSelectedDateRange!.start.month.toString().padLeft(2, '0')}-${tempSelectedDateRange!.start.day.toString().padLeft(2, '0')} ~ ${tempSelectedDateRange!.end.year}-${tempSelectedDateRange!.end.month.toString().padLeft(2, '0')}-${tempSelectedDateRange!.end.day.toString().padLeft(2, '0')}'
                            : '전체 기간',
                      ),
                      trailing: Icon(
                        Icons.chevron_right_rounded,
                        color: AppColors.onSurfaceVariant,
                      ),
                      onTap: () async {
                        final dateRange = await showDateRangePicker(
                          context: context,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                          initialDateRange: tempSelectedDateRange,
                        );
                        if (dateRange != null) {
                          tempSelectedDateRange = dateRange;
                          filterChanged.value = !filterChanged.value;
                        }
                      },
                    ),
                  ),
                  if (tempSelectedDateRange != null)
                    Padding(
                      padding: const EdgeInsets.only(top: Dimens.space8),
                      child: TextButton.icon(
                        onPressed: () {
                          tempSelectedDateRange = null;
                          filterChanged.value = !filterChanged.value;
                        },
                        icon: const Icon(Icons.clear_rounded),
                        label: const Text('날짜 범위 초기화'),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      confirmLabel: '적용',
      cancelLabel: '취소',
    );

    if (confirmed == true) {
      return {
        'selectedSeller': tempSelectedSeller,
        'selectedDateRange': tempSelectedDateRange,
      };
    }

    return null;
  }

  /// 필터 정보를 표시할 수 있는지 확인합니다.
  ///
  /// [selectedSeller]: 선택된 판매자
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터 정보 표시 여부
  static bool shouldShowFilterInfo({
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) {
    return selectedSeller != '전체 판매자' || selectedDateRange != null;
  }

  /// 필터가 적용되었는지 확인합니다.
  ///
  /// [selectedSeller]: 선택된 판매자
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터 적용 여부
  static bool hasActiveFilters({
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) {
    return selectedSeller != '전체 판매자' || selectedDateRange != null;
  }

  /// 필터를 초기화합니다.
  ///
  /// 반환값: 초기화된 필터 값들
  static Map<String, dynamic> resetFilters() {
    return {
      'selectedSeller': '전체 판매자',
      'selectedDateRange': null,
    };
  }
} 
