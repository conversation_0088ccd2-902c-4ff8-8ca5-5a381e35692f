/// 바라 부스 매니저 - 구독용 카드 등록 화면
///
/// 실제 운영 환경의 카드 등록 화면입니다.
/// 기존 CardRegistrationScreen 스타일을 참고하여 제작되었습니다.
///
/// 작성자: Blue
/// 버전: 2.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/nicepay_subscription_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';

/// 구독용 카드 등록 화면
class SubscriptionCardRegistrationScreen extends StatefulWidget {
  final Function(String bid)? onSuccess;

  const SubscriptionCardRegistrationScreen({
    super.key,
    this.onSuccess,
  });

  @override
  State<SubscriptionCardRegistrationScreen> createState() => _SubscriptionCardRegistrationScreenState();
}

class _SubscriptionCardRegistrationScreenState extends State<SubscriptionCardRegistrationScreen> {
  static const String _tag = 'SubscriptionCardRegistrationScreen';
  
  final NicePaySubscriptionService _subscriptionService = NicePaySubscriptionService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final _formKey = GlobalKey<FormState>();
  
  // 폼 컨트롤러들
  final _cardNoController = TextEditingController();
  final _expYearController = TextEditingController();
  final _expMonthController = TextEditingController();
  final _idNoController = TextEditingController();
  final _cardPwController = TextEditingController();
  final _buyerNameController = TextEditingController();
  final _buyerEmailController = TextEditingController();
  final _buyerTelController = TextEditingController();
  
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _cardNoController.dispose();
    _expYearController.dispose();
    _expMonthController.dispose();
    _idNoController.dispose();
    _cardPwController.dispose();
    _buyerNameController.dispose();
    _buyerEmailController.dispose();
    _buyerTelController.dispose();
    super.dispose();
  }

  /// 폼 초기화
  void _initializeForm() {
    final user = _auth.currentUser;
    if (user != null) {
      _buyerEmailController.text = user.email ?? '';
      _buyerNameController.text = user.displayName ?? '구매자';

      // 인증된 전화번호 자동 입력
      _loadVerifiedPhoneNumber();
    }
  }

  /// 인증된 전화번호 로드
  Future<void> _loadVerifiedPhoneNumber() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (userDoc.exists) {
          final data = userDoc.data()!;
          final phoneVerified = data['phoneVerified'] as bool? ?? false;
          final phone = data['phone'] as String?;

          if (phoneVerified && phone != null && mounted) {
            // 전화번호 형식 변환 (010-1234-5678 → 01012345678)
            final cleanPhone = phone.replaceAll('-', '');
            _buyerTelController.text = cleanPhone;
          }
        }
      }
    } catch (e) {
      LoggerUtils.logError('인증된 전화번호 로드 실패', tag: _tag, error: e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('카드 등록', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 안내 메시지
                _buildNoticeCard(),
                const SizedBox(height: 24),
                
                // 카드 정보 입력
                _buildCardInfoSection(),
                const SizedBox(height: 24),
                
                // 구매자 정보 입력
                _buildBuyerInfoSection(),
                const SizedBox(height: 32),
                
                // 등록 버튼
                _buildRegisterButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoticeCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  '카드 등록 안내',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• 카드 정보가 안전하게 암호화됩니다\n'
              '• 유료 플랜 자동 결제에 사용됩니다\n'
              '• 카드 정보는 나이스페이에서 안전하게 관리됩니다\n'
              '• 언제든지 구독을 취소할 수 있습니다',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade600,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.credit_card, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Text(
                  '카드 정보',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 카드번호 (자동 하이픈 추가)
            TextFormField(
              controller: _cardNoController,
              decoration: const InputDecoration(
                labelText: '카드번호',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.credit_card),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(16),
                CardNumberFormatter(), // 자동 하이픈 추가
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '카드번호를 입력해주세요';
                }
                final digitsOnly = value.replaceAll('-', '');
                if (digitsOnly.length != 16) {
                  return '카드번호는 16자리여야 합니다';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // 유효기간 (월/년)
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _expMonthController,
                    decoration: const InputDecoration(
                      labelText: '월',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.calendar_month),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(2),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '월을 입력해주세요';
                      }
                      if (value.length != 2) {
                        return '2자리 입력';
                      }
                      final month = int.tryParse(value);
                      if (month == null || month < 1 || month > 12) {
                        return '01~12 입력';
                      }
                      return null;
                    },
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text('/', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                ),
                Expanded(
                  child: TextFormField(
                    controller: _expYearController,
                    decoration: const InputDecoration(
                      labelText: '년',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.calendar_today),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(2),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '년도를 입력해주세요';
                      }
                      if (value.length != 2) {
                        return '2자리 입력';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 생년월일/사업자번호
            TextFormField(
              controller: _idNoController,
              decoration: const InputDecoration(
                labelText: '생년월일 또는 사업자번호',
                hintText: 'YYMMDD',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '생년월일 또는 사업자번호를 입력해주세요';
                }
                if (value.length != 6 && value.length != 10) {
                  return '생년월일(6자리) 또는 사업자번호(10자리)를 입력해주세요';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // 카드 비밀번호
            TextFormField(
              controller: _cardPwController,
              decoration: const InputDecoration(
                labelText: '카드 비밀번호 앞 2자리',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(2),
              ],
              obscureText: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '카드 비밀번호 앞 2자리를 입력해주세요';
                }
                if (value.length != 2) {
                  return '2자리를 입력해주세요';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuyerInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.orange.shade700),
                const SizedBox(width: 8),
                Text(
                  '구매자 정보',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 구매자명
            TextFormField(
              controller: _buyerNameController,
              decoration: const InputDecoration(
                labelText: '구매자명',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person_outline),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '구매자명을 입력해주세요';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // 이메일
            TextFormField(
              controller: _buyerEmailController,
              decoration: const InputDecoration(
                labelText: '이메일',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '이메일을 입력해주세요';
                }
                if (!value.contains('@')) {
                  return '올바른 이메일 형식을 입력해주세요';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // 전화번호
            TextFormField(
              controller: _buyerTelController,
              decoration: const InputDecoration(
                labelText: '전화번호',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(11),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '전화번호를 입력해주세요';
                }
                if (value.length < 10 || value.length > 11) {
                  return '올바른 전화번호를 입력해주세요';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegisterButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isProcessing ? null : _registerCard,
        icon: _isProcessing
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.add_card),
        label: Text(
          _isProcessing ? '등록 중...' : '카드 등록',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 카드 등록 실행
  Future<void> _registerCard() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isProcessing = true);

    try {
      LoggerUtils.logInfo('카드 등록 시작', tag: _tag);

      final result = await _subscriptionService.registerCard(
        cardNo: _cardNoController.text.replaceAll('-', ''), // 하이픈 제거
        expYear: _expYearController.text,
        expMonth: _expMonthController.text,
        idNo: _idNoController.text,
        cardPw: _cardPwController.text,
        buyerName: _buyerNameController.text,
        buyerEmail: _buyerEmailController.text,
        buyerTel: _buyerTelController.text,
      );

      if (result.isSuccess) {
        LoggerUtils.logInfo('카드 등록 성공', tag: _tag);
        ToastUtils.showSuccess(context, '카드가 성공적으로 등록되었습니다!');

        // 성공 콜백 호출
        if (widget.onSuccess != null) {
          widget.onSuccess!(result.data!['bid']);
        }

        // 화면 닫기
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } else {
        LoggerUtils.logError('카드 등록 실패: ${result.error}', tag: _tag);
        ToastUtils.showError(context, result.error ?? '카드 등록에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('카드 등록 오류: $e', tag: _tag);
      ToastUtils.showError(context, '카드 등록 중 오류가 발생했습니다.');
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }
}

/// 카드번호 자동 포맷터 (4자리마다 하이픈 추가)
class CardNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll('-', '');
    final buffer = StringBuffer();

    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write('-');
      }
      buffer.write(text[i]);
    }

    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
