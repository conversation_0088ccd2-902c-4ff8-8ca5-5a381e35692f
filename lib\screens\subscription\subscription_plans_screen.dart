/// 바라 부스 매니저 - 구독 플랜 비교 화면
///
/// 무료, 플러스, 프로 플랜의 기능을 비교하고 업그레이드를 안내하는 화면입니다.
/// - 플랜별 기능 비교표
/// - 가격 정보
/// - 업그레이드 안내
///
/// 작성자: Blue
/// 버전: 2.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/subscription_plan.dart';
import '../../providers/subscription_provider.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/currency_utils.dart';
import '../../utils/app_colors.dart';
import '../../config/app_config.dart';
import '../../services/remote_config_service.dart';
import 'in_app_subscription_screen.dart';
import '../settings/native_phone_verification_screen.dart';

/// 구독 플랜 비교 화면
class SubscriptionPlansScreen extends ConsumerStatefulWidget {
  const SubscriptionPlansScreen({super.key});

  @override
  ConsumerState<SubscriptionPlansScreen> createState() => _SubscriptionPlansScreenState();
}

class _SubscriptionPlansScreenState extends ConsumerState<SubscriptionPlansScreen> {

  @override
  Widget build(BuildContext context) {
    final currentPlanAsync = ref.watch(currentPlanTypeProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('플랜 비교', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: currentPlanAsync.when(
          data: (currentPlanType) => _buildContent(currentPlanType),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text('플랜 정보를 불러올 수 없습니다'),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(currentPlanTypeProvider),
                  child: Text('다시 시도'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(SubscriptionPlanType currentPlanType) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 플랜 카드들 (새로운 디자인)
          _buildNewPlanCards(currentPlanType),
          const SizedBox(height: 24),

          // 업그레이드 버튼 (하단에 하나만)
          _buildUpgradeButton(currentPlanType),
          const SizedBox(height: 32),

          // 기능 비교표
          _buildFeatureComparison(),
          const SizedBox(height: 32),

          // FAQ 섹션
          _buildFaqSection(),
        ],
      ),
    );
  }



  /// 새로운 플랜 카드들 (세로 배치)
  Widget _buildNewPlanCards(SubscriptionPlanType currentPlanType) {
    return Column(
      children: [
        // 무료 플랜
        _buildNewPlanCard(
          plan: PredefinedPlans.free,
          isCurrentPlan: currentPlanType == SubscriptionPlanType.free,
          isRecommended: false,
        ),
        const SizedBox(height: 16),

        // 플러스 플랜
        _buildNewPlanCard(
          plan: PredefinedPlans.plus,
          isCurrentPlan: currentPlanType == SubscriptionPlanType.plus,
          isRecommended: false,
        ),
        const SizedBox(height: 16),


      ],
    );
  }

  /// 새로운 플랜 카드 디자인
  Widget _buildNewPlanCard({
    required SubscriptionPlan plan,
    required bool isCurrentPlan,
    required bool isRecommended,
  }) {
    Color cardColor;
    Color textColor;
    String statusText = '';

    if (isCurrentPlan) {
      // 현재 플랜은 플랜 타입에 따라 색상을 다르게 설정하되 더 진하게 표시
      if (plan.type == SubscriptionPlanType.free) {
        cardColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
      } else if (plan.type == SubscriptionPlanType.plus) {
        cardColor = Colors.blue[100]!;
        textColor = Colors.blue[800]!;
      // 프로 플랜 제거됨
      } else {
        cardColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
      }
      statusText = '현재 플랜';
    } else if (plan.type == SubscriptionPlanType.plus) {
      cardColor = Colors.blue[50]!;
      textColor = Colors.blue[800]!;
    // 프로 플랜 제거됨
    } else {
      cardColor = Colors.grey[100]!;
      textColor = Colors.grey[700]!;
    }

    return Container(
      width: double.infinity,
      height: 150, // 고정 높이 (10px 증가)
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // 왼쪽: 플랜 정보
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 플랜 이름과 상태
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            plan.name,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: textColor,
                            ),
                          ),
                        ),
                        if (isCurrentPlan) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: plan.type == SubscriptionPlanType.free ? Colors.green[600] :
                                     plan.type == SubscriptionPlanType.plus ? Colors.blue[600] :
                                     Colors.amber[600],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              statusText,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      plan.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: textColor.withValues(alpha: 0.8),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 오른쪽: 가격 정보
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (plan.type == SubscriptionPlanType.plus) ...[
                      // 플러스 플랜 이벤트 표시
                      FutureBuilder<bool>(
                        future: RemoteConfigService.isPlusPlanFreeNow(),
                        builder: (context, snapshot) {
                          final isFreeNow = snapshot.data ?? false;

                          if (isFreeNow) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                // 원래 가격에 가로줄
                                Text(
                                  '${CurrencyUtils.formatWon(plan.monthlyPrice)}',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: textColor.withValues(alpha: 0.6),
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor: textColor.withValues(alpha: 0.6),
                                    decorationThickness: 2,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // 이벤트 가격
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: const Color.fromARGB(255, 3, 151, 209),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: const Text(
                                    '0원',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 2),
                                FutureBuilder<String>(
                                  future: RemoteConfigService.getPlusPlanEventMessage(),
                                  builder: (context, msgSnapshot) {
                                    return Text(
                                      msgSnapshot.data ?? '오픈 이벤트!',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.red,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      textAlign: TextAlign.right,
                                    );
                                  },
                                ),
                              ],
                            );
                          } else {
                            // 이벤트 기간이 아니면 정상 가격 표시
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  '${CurrencyUtils.formatWon(plan.monthlyPrice)}',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: textColor,
                                  ),
                                ),
                                Text(
                                  '월 결제',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: textColor.withValues(alpha: 0.7),
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ],
                            );
                          }
                        },
                      ),
                    ] else if (plan.monthlyPrice > 0) ...[
                      Text(
                        '${CurrencyUtils.formatWon(plan.monthlyPrice)}',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                      Text(
                        '월 결제',
                        style: TextStyle(
                          fontSize: 12,
                          color: textColor.withValues(alpha: 0.7),
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ] else ...[
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          '무료',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 업그레이드 버튼
  Widget _buildUpgradeButton(SubscriptionPlanType currentPlanType) {
    // 프로 플랜 제거됨 - 플러스가 최고 플랜
    if (currentPlanType == SubscriptionPlanType.plus) {
      return const SizedBox.shrink(); // 이미 최고 플랜이면 버튼 숨김
    }

    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          await _navigateToSubscriptionWithPhoneCheck();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primarySeed,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.upgrade, size: 20),
            const SizedBox(width: 8),
            Text(
              currentPlanType == SubscriptionPlanType.free ? '플랜 업그레이드' : '플랜 변경',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }





  /// 전화번호 인증 확인 후 구독 관리 페이지로 이동
  Future<void> _navigateToSubscriptionWithPhoneCheck() async {
    try {
      // 현재 사용자의 전화번호 인증 상태 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('로그인이 필요합니다.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // ⚠️ 앱스토어 심사용 임시 비활성화
      // 심사 완료 후 AppConfig.PHONE_VERIFICATION_REQUIRED를 true로 변경하세요!
      if (AppConfig.PHONE_VERIFICATION_REQUIRED) {
        // Firestore에서 전화번호 인증 상태 확인
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        final phoneVerified = userDoc.data()?['phoneVerified'] as bool? ?? false;

        if (!phoneVerified) {
          // 전화번호 인증이 안 된 경우 안내 다이얼로그 표시
          _showPhoneVerificationRequiredDialog();
          return;
        }
      }

      // 인앱 구독 관리 페이지로 이동
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const InAppSubscriptionScreen(),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('오류가 발생했습니다: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 전화번호 인증 필요 다이얼로그 표시
  void _showPhoneVerificationRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('전화번호 인증 필요'),
          content: const Text('구독 서비스 이용을 위해 전화번호 인증이 필요합니다.\n전화번호 인증을 진행하시겠습니까?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('취소'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const NativePhoneVerificationScreen(),
                  ),
                );
              },
              child: const Text('인증하기'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFeatureComparison() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey.shade50,
            Colors.white,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.compare_arrows,
                color: Colors.blue.shade600,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '기능 비교',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 헤더 행
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade50,
                  Colors.indigo.shade50,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade100),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    '기능',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.star_border, color: Colors.grey.shade600, size: 20),
                      const SizedBox(height: 4),
                      Text(
                        '무료',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.star_half, color: Colors.blue.shade600, size: 20),
                      const SizedBox(height: 4),
                      Text(
                        '플러스',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),

              ],
            ),
          ),
          const SizedBox(height: 20),

          // 기능 비교 행들 - 체크(✅)가 많은 기능부터 정렬
          _buildFeatureRow('행사 등록', '1개', '무제한'),
          _buildFeatureRow('상품 등록', '30개', '무제한'),
          _buildFeatureRow('TODO 체크리스트', '✅', '✅'),
          _buildFeatureRow('목표 매출 설정', '✅', '✅'),
          _buildFeatureRow('서비스 기능', '✅', '✅'),
          _buildFeatureRow('선입금 관리', '✅', '✅'),
          _buildFeatureRow('세트 설정 할인', '❌', '✅'),
          _buildFeatureRow('엑셀 선입금 일괄 등록', '❌', '✅'),
          _buildFeatureRow('프리미엄 통계', '❌', '✅'),
          _buildFeatureRow('엑셀 내보내기', '❌', '✅'),
          _buildFeatureRow('PDF 내보내기', '❌', '✅'),
          _buildFeatureRow('판매자별 관리', '❌', '✅'),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(String feature, String freeValue, String plusValue) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade100),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              feature,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
          ),
          Expanded(
            child: _buildFeatureValue(freeValue, Colors.grey.shade600),
          ),
          Expanded(
            child: _buildFeatureValue(plusValue, Colors.blue.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureValue(String value, Color baseColor) {
    if (value == '✅') {
      return Container(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            Icons.check,
            color: Colors.green.shade600,
            size: 16,
          ),
        ),
      );
    } else if (value == '❌') {
      return Container(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            Icons.close,
            color: Colors.red.shade600,
            size: 16,
          ),
        ),
      );
    } else {
      return Container(
        alignment: Alignment.center,
        child: Text(
          value,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 13,
            color: baseColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
  }

  Widget _buildFaqSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '자주 묻는 질문',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          _buildFaqItem(
            '무료 플랜에서 유료 플랜으로 언제든 업그레이드할 수 있나요?',
            '네, 언제든지 업그레이드할 수 있습니다. 기존 데이터는 모두 유지되며, 즉시 추가 기능을 사용할 수 있습니다. 다만 전화번호 인증이 필요합니다.',
          ),
          _buildFaqItem(
            '유료 플랜을 해지하면 어떻게 되나요?',
            '구독 만료일까지는 유료 플랜 기능을 계속 사용할 수 있으며, 만료 후 자동으로 무료 플랜으로 전환됩니다. 기존 데이터는 유지되지만 무료 플랜 제한이 적용됩니다.',
          ),
          _buildFaqItem(
            '플러스 플랜의 주요 기능은 무엇인가요?',
            '플러스 플랜은 무제한 상품 등록, 세트 할인, 판매자별 관리, 고급 통계, 엑셀/PDF 내보내기 등 모든 프리미엄 기능을 제공합니다.',
          ),
          _buildFaqItem(
            '데이터는 어떻게 관리되나요?',
            '모든 플랜에서 로컬에만 저장되며 어플을 삭제하면 데이터도 함께 삭제됩니다. 중요한 데이터는 엑셀 내보내기 기능을 통해 백업하시기 바랍니다.',
          ),
          _buildFaqItem(
            '엑셀 선입금 일괄 등록 기능은 어떤 기능인가요?',
            '플러스/프로 플랜에서 제공되는 기능으로, 위치폼에서 제공하는 엑셀 파일을 통해 여러 고객의 선입금 정보를 한 번에 간편하게 등록할 수 있습니다.',
          ),
          _buildFaqItem(
            '결제는 어떻게 이루어지나요?',
            'NICE 페이를 통해 안전하게 데이터가 암호화되어 결제가 진행됩니다. 월간 구독으로 진행되며, 매월 자동으로 결제됩니다.',
          ),
        ],
      ),
    );
  }

  Widget _buildFaqItem(String question, String answer) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            answer,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }




}
