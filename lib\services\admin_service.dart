import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/admin_models.dart';
import 'cache_service.dart';
import '../utils/logger_utils.dart';

/// 관리자 API 서비스
class AdminService {
  static const String _baseUrl = 'https://us-central1-parabara-1a504.cloudfunctions.net';
  static const String _tokenKey = 'admin_token';
  
  // API URLs
  static const Map<String, String> _apiUrls = {
    'adminAuth': 'https://adminauth-kahfshl2oa-uc.a.run.app',
    'adminDashboard': 'https://admindashboard-kahfshl2oa-uc.a.run.app',
    'adminUsers': 'https://adminusers-kahfshl2oa-uc.a.run.app',
    'adminSubscriptionManagement': '$_baseUrl/adminSubscriptionManagement',

    'getAdminSystemInfo': '$_baseUrl/getAdminSystemInfo',
  };

  /// 관리자 로그인
  static Future<AdminAuthResponse> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse(_apiUrls['adminAuth']!),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      );

      final data = jsonDecode(response.body);
      final authResponse = AdminAuthResponse.fromJson(data);

      // 토큰 저장
      if (authResponse.success && authResponse.token != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, authResponse.token!);
      }

      return authResponse;
    } catch (e) {
      return AdminAuthResponse(
        success: false,
        message: '로그인 중 오류가 발생했습니다: $e',
      );
    }
  }

  /// 저장된 토큰 가져오기
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// 로그아웃
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  /// 인증 헤더 생성
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// 대시보드 통계 조회 (캐싱 적용)
  static Future<AdminDashboardStats?> getDashboardStats({bool forceRefresh = false}) async {
    const cacheKey = CacheService.adminDashboardStatsKey;
    const cacheDuration = Duration(hours: 24); // 24시간 캐시 (수동 새로고침 방식)

    try {
      // 강제 새로고침이 아닌 경우에만 캐시 확인
      if (!forceRefresh) {
        final cachedStats = await CacheService.get<AdminDashboardStats>(
          cacheKey,
          (json) => AdminDashboardStats.fromJson(json),
        );

        if (cachedStats != null) {
          LoggerUtils.logDebug('대시보드 통계 캐시 사용', tag: 'AdminService');
          return cachedStats;
        }
      }

      // 캐시 미스 - API 호출
      LoggerUtils.logDebug('대시보드 통계 API 호출', tag: 'AdminService');
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse(_apiUrls['adminDashboard']!),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final stats = AdminDashboardStats.fromJson(data['data']);

          // 캐시에 저장
          await CacheService.set<AdminDashboardStats>(
            cacheKey,
            stats,
            cacheDuration,
            (stats) => stats.toJson(),
          );

          return stats;
        }
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('대시보드 통계 조회 오류', tag: 'AdminService', error: e);
      return null;
    }
  }

  /// 사용자 목록 조회 (페이지네이션 포함, 캐싱 적용)
  static Future<AdminUsersResponse> getUsers({
    int page = 1,
    int limit = 20,
    String? statusFilter,
    String? searchQuery,
  }) async {
    final cacheKey = CacheService.generateKey(CacheService.adminUsersKey, {
      'page': page,
      'limit': limit,
      'statusFilter': statusFilter ?? 'all',
      'searchQuery': searchQuery ?? '',
    });
    const cacheDuration = Duration(hours: 1); // 1시간 캐시 (사용자 목록은 상대적으로 자주 변경)

    try {
      // 캐시에서 먼저 확인
      final cachedResponse = await CacheService.get<AdminUsersResponse>(
        cacheKey,
        (json) => AdminUsersResponse.fromJson(json),
      );

      if (cachedResponse != null) {
        LoggerUtils.logDebug('사용자 목록 캐시 사용 (page: $page)', tag: 'AdminService');
        return cachedResponse;
      }

      // 캐시 미스 - API 호출
      LoggerUtils.logDebug('사용자 목록 API 호출 (page: $page)', tag: 'AdminService');
      final headers = await _getAuthHeaders();

      // 쿼리 파라미터 구성
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (statusFilter != null && statusFilter != 'all') {
        queryParams['statusFilter'] = statusFilter;
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['searchQuery'] = searchQuery;
      }

      final uri = Uri.parse(_apiUrls['adminUsers']!).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final users = (data['data']['users'] as List)
              .map((user) => AdminUser.fromJson(user))
              .toList();



          final pagination = data['data']['pagination'];
          final usersResponse = AdminUsersResponse(
            users: users,
            pagination: AdminPagination.fromJson(pagination),
          );

          // 캐시에 저장
          await CacheService.set<AdminUsersResponse>(
            cacheKey,
            usersResponse,
            cacheDuration,
            (response) => response.toJson(),
          );

          return usersResponse;
        }
      }
      return AdminUsersResponse(users: [], pagination: AdminPagination.empty());
    } catch (e) {
      LoggerUtils.logError('사용자 목록 조회 오류', tag: 'AdminService', error: e);
      return AdminUsersResponse(users: [], pagination: AdminPagination.empty());
    }
  }

  /// 사용자 목록 조회 (하위 호환성을 위해 유지)
  static Future<List<AdminUser>> getUsersLegacy() async {
    final response = await getUsers();
    return response.users;
  }

  /// 시스템 모니터링 정보 조회
  static Future<Map<String, dynamic>?> getSystemInfo() async {
    try {
      final response = await http.get(
        Uri.parse(_apiUrls['getAdminSystemInfo']!),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['data'];
        }
      }
      return null;
    } catch (e) {
      print('시스템 정보 조회 오류: $e');
      return null;
    }
  }



  /// 구독 기간 연장 (일수 추가)
  static Future<bool> addSubscriptionDays(String uid, int days) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await http.post(
        Uri.parse(_apiUrls['adminSubscriptionManagement']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': 'add_days',
          'days': days,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('구독 기간 연장 오류: $e');
      return false;
    }
  }

  /// 구독 취소 (다음 결제일까지 유지)
  static Future<bool> cancelUserSubscription(String uid) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await http.post(
        Uri.parse(_apiUrls['adminSubscriptionManagement']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': 'cancel',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('구독 취소 오류: $e');
      return false;
    }
  }

  /// 구독 활성화 (특정 날짜까지)
  static Future<bool> activateSubscriptionUntilDate(String uid, DateTime endDate) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await http.post(
        Uri.parse(_apiUrls['adminSubscriptionManagement']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': 'activate_until_date',
          'endDate': endDate.toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('구독 활성화 오류: $e');
      return false;
    }
  }

  /// 구독 활성화 (일수 지정)
  static Future<bool> activateSubscriptionForDays(String uid, int days) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await http.post(
        Uri.parse(_apiUrls['adminSubscriptionManagement']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': 'activate_for_days',
          'days': days,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('구독 활성화 오류: $e');
      return false;
    }
  }

  /// 강제로 무료 플랜으로 전환
  static Future<bool> forceFreePlan(String uid) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await http.post(
        Uri.parse(_apiUrls['adminSubscriptionManagement']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': 'force_free',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('강제 무료 전환 오류: $e');
      return false;
    }
  }

  /// Firestore timestamp 파싱 헬퍼 메서드
  static DateTime _parseTimestamp(dynamic timestamp) {
    try {
      if (timestamp == null) return DateTime.now();

      // Firestore Timestamp 객체인 경우
      if (timestamp is Map && timestamp.containsKey('_seconds')) {
        final seconds = timestamp['_seconds'] as int;
        final nanoseconds = timestamp['_nanoseconds'] as int? ?? 0;
        return DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds / 1000000).round()
        );
      }

      // ISO 문자열인 경우
      if (timestamp is String) {
        return DateTime.parse(timestamp);
      }

      // Unix timestamp (밀리초)인 경우
      if (timestamp is int) {
        return DateTime.fromMillisecondsSinceEpoch(
          timestamp > 1000000000000 ? timestamp : timestamp * 1000
        );
      }

      // 기본값
      return DateTime.now();
    } catch (e) {
      print('Timestamp 파싱 오류: $e, timestamp: $timestamp');
      return DateTime.now();
    }
  }

  /// 관리자 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAdminLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['adminLogs'] != null) {
        final logsData = systemInfo['adminLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: _parseTimestamp(log['timestamp']),
          level: log['success'] == true ? 'SUCCESS' : 'INFO',
          message: '관리자 로그인',
          details: '${log['username']} - ${log['ip']}',
        )).toList();
      }
    } catch (e) {
      print('관리자 로그 조회 실패: $e');
    }

    return [];
  }

  /// 통합 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAutoPaymentLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['logs'] != null) {
        final logsData = systemInfo['logs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: _parseTimestamp(log['timestamp']),
          level: log['level'] ?? 'INFO',
          message: log['message'] ?? '시스템 로그',
          details: log['details'] != null
            ? (log['details'] is String ? log['details'] : '상세정보 있음')
            : '처리: ${log['totalProcessed'] ?? 0}건, 성공: ${log['successCount'] ?? 0}건, 실패: ${log['failureCount'] ?? 0}건',
        )).toList();
      }
    } catch (e) {
      print('통합 로그 조회 실패: $e');
    }

    return [];
  }



  /// 시스템 로그 조회 (하위 호환성을 위해 유지)
  static Future<List<SystemLog>> getSystemLogs() async {
    return await getAdminLogs();
  }

  /// 시스템 통계 조회 (실데이터)
  static Future<SystemStats> getSystemStats() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['systemStats'] != null) {
        final stats = systemInfo['systemStats'];
        return SystemStats(
          totalFunctionCalls: stats['totalFunctionCalls'] ?? '-',
          successRate: stats['successRate'] ?? '-',
          lastExecution: stats['lastExecution'] ?? '-',
        );
      }
    } catch (e) {
      print('실데이터 통계 조회 실패, 기본 데이터 반환: $e');
    }

    // 실데이터 조회 실패 시 기본 데이터 반환
    return SystemStats(
      totalFunctionCalls: '-',
      successRate: '-',
      lastExecution: '-',
    );
  }


}
