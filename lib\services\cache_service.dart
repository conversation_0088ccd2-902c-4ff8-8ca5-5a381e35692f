import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger_utils.dart';

/// 캐시 항목
class CacheItem<T> {
  final T data;
  final DateTime expiry;

  CacheItem({
    required this.data,
    required this.expiry,
  });

  bool get isExpired => DateTime.now().isAfter(expiry);

  Map<String, dynamic> toJson() => {
    'data': data,
    'expiry': expiry.toIso8601String(),
  };

  factory CacheItem.fromJson(Map<String, dynamic> json, T Function(dynamic) fromJsonT) {
    return CacheItem<T>(
      data: fromJsonT(json['data']),
      expiry: DateTime.parse(json['expiry']),
    );
  }
}

/// 캐싱 서비스 (POS 최적화)
/// 관리자 대시보드 및 자주 사용되는 데이터를 메모리와 로컬 저장소에 캐시
class CacheService {
  static const String _tag = 'CacheService';
  
  // 메모리 캐시
  static final Map<String, CacheItem> _memoryCache = {};
  
  // 캐시 키 상수
  static const String adminDashboardStatsKey = 'admin_dashboard_stats';
  static const String adminUsersKey = 'admin_users';
  static const String adminSystemInfoKey = 'admin_system_info';
  static const String salesStatsKey = 'sales_stats';
  static const String productStatsKey = 'product_stats';
  
  /// 캐시에서 데이터 가져오기
  static Future<T?> get<T>(
    String key, 
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    try {
      // 1. 메모리 캐시 확인
      final memoryItem = _memoryCache[key];
      if (memoryItem != null && !memoryItem.isExpired) {
        LoggerUtils.logDebug('메모리 캐시 히트: $key', tag: _tag);
        return memoryItem.data as T;
      }

      // 2. 로컬 저장소 캐시 확인
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(key);
      
      if (cachedJson != null) {
        final cachedData = jsonDecode(cachedJson) as Map<String, dynamic>;
        final cacheItem = CacheItem.fromJson(
          cachedData, 
          (data) => fromJson(data as Map<String, dynamic>),
        );
        
        if (!cacheItem.isExpired) {
          // 메모리 캐시에도 저장
          _memoryCache[key] = cacheItem;
          LoggerUtils.logDebug('로컬 캐시 히트: $key', tag: _tag);
          return cacheItem.data;
        } else {
          // 만료된 캐시 제거
          await prefs.remove(key);
          _memoryCache.remove(key);
          LoggerUtils.logDebug('만료된 캐시 제거: $key', tag: _tag);
        }
      }

      return null;
    } catch (e) {
      LoggerUtils.logError('캐시 조회 실패: $key', tag: _tag, error: e);
      return null;
    }
  }

  /// 캐시에 데이터 저장
  static Future<void> set<T>(
    String key, 
    T data, 
    Duration duration,
    Map<String, dynamic> Function(T) toJson,
  ) async {
    try {
      final expiry = DateTime.now().add(duration);
      final cacheItem = CacheItem<T>(data: data, expiry: expiry);
      
      // 1. 메모리 캐시에 저장
      _memoryCache[key] = cacheItem;
      
      // 2. 로컬 저장소에 저장
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'data': toJson(data),
        'expiry': expiry.toIso8601String(),
      };
      await prefs.setString(key, jsonEncode(cacheData));
      
      LoggerUtils.logDebug('캐시 저장 완료: $key (만료: ${expiry.toLocal()})', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('캐시 저장 실패: $key', tag: _tag, error: e);
    }
  }

  /// 특정 키의 캐시 제거
  static Future<void> remove(String key) async {
    try {
      _memoryCache.remove(key);
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
      LoggerUtils.logDebug('캐시 제거: $key', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('캐시 제거 실패: $key', tag: _tag, error: e);
    }
  }

  /// 패턴으로 캐시 제거 (예: 'admin_*')
  static Future<void> removeByPattern(String pattern) async {
    try {
      final regex = RegExp(pattern.replaceAll('*', '.*'));
      
      // 메모리 캐시에서 제거
      _memoryCache.removeWhere((key, value) => regex.hasMatch(key));
      
      // 로컬 저장소에서 제거
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => regex.hasMatch(key)).toList();
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      LoggerUtils.logDebug('패턴 캐시 제거: $pattern (${keys.length}개)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('패턴 캐시 제거 실패: $pattern', tag: _tag, error: e);
    }
  }

  /// 모든 캐시 제거
  static Future<void> clear() async {
    try {
      _memoryCache.clear();
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      LoggerUtils.logInfo('모든 캐시 제거 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('캐시 전체 제거 실패', tag: _tag, error: e);
    }
  }

  /// 만료된 캐시 정리
  static Future<void> cleanupExpired() async {
    try {
      // 메모리 캐시 정리
      _memoryCache.removeWhere((key, value) => value.isExpired);
      
      // 로컬 저장소 캐시 정리
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().toList();
      int removedCount = 0;
      
      for (final key in keys) {
        try {
          final cachedJson = prefs.getString(key);
          if (cachedJson != null) {
            final cachedData = jsonDecode(cachedJson) as Map<String, dynamic>;
            final expiry = DateTime.parse(cachedData['expiry']);
            
            if (DateTime.now().isAfter(expiry)) {
              await prefs.remove(key);
              removedCount++;
            }
          }
        } catch (e) {
          // 파싱 실패한 캐시는 제거
          await prefs.remove(key);
          removedCount++;
        }
      }
      
      LoggerUtils.logDebug('만료된 캐시 정리 완료: ${removedCount}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('만료된 캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// 캐시 통계 조회
  static Future<Map<String, dynamic>> getStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localKeys = prefs.getKeys().length;
      final memoryKeys = _memoryCache.length;
      
      return {
        'memoryCache': memoryKeys,
        'localStorage': localKeys,
        'totalSize': memoryKeys + localKeys,
      };
    } catch (e) {
      LoggerUtils.logError('캐시 통계 조회 실패', tag: _tag, error: e);
      return {'error': e.toString()};
    }
  }

  /// 캐시 키 생성 헬퍼
  static String generateKey(String prefix, [Map<String, dynamic>? params]) {
    if (params == null || params.isEmpty) {
      return prefix;
    }
    
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key))
    );
    
    final paramString = sortedParams.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    
    return '${prefix}_$paramString';
  }
}
