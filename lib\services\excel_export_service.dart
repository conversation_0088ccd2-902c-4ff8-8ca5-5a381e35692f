import 'dart:io';
import 'package:excel/excel.dart' as excel;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/currency_utils.dart';

/// 통계 데이터를 엑셀로 내보내는 서비스
class ExcelExportService {
  // 안전한 타입 변환 헬퍼 메서드들
  /// dynamic 값을 안전하게 int로 변환
  static int _safeToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) {
      final parsed = double.tryParse(value);
      return parsed?.round() ?? 0;
    }
    return 0;
  }

  /// dynamic 값을 안전하게 double로 변환
  static double _safeToDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  // 완전한 헤더 스타일 (웜 테라코타 색상)
  static final _headerStyle = excel.CellStyle(
    bold: true,
    fontSize: 12,
    fontColorHex: excel.ExcelColor.white, // 흰색 글씨
    backgroundColorHex: excel.ExcelColor.orange,
    horizontalAlign: excel.HorizontalAlign.Center,
    verticalAlign: excel.VerticalAlign.Center,
    textWrapping: excel.TextWrapping.WrapText,
    leftBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    rightBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    topBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    bottomBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
  );

  static final _titleStyle = excel.CellStyle(
    bold: true,
    fontSize: 16,
    fontColorHex: excel.ExcelColor.blue800,
    horizontalAlign: excel.HorizontalAlign.Center,
    verticalAlign: excel.VerticalAlign.Center, // 세로 중앙정렬 추가
    textWrapping: excel.TextWrapping.WrapText,
  );

  static final _subtitleStyle = excel.CellStyle(
    bold: true,
    fontSize: 12, // 원래대로 12로 복구
    fontColorHex: excel.ExcelColor.black,
    horizontalAlign: excel.HorizontalAlign.Left,
    verticalAlign: excel.VerticalAlign.Center, // 세로 중앙정렬 추가
    textWrapping: excel.TextWrapping.WrapText,
  );

  static final _dataStyle = excel.CellStyle(
    fontSize: 10,
    fontColorHex: excel.ExcelColor.black,
    horizontalAlign: excel.HorizontalAlign.Left,
    verticalAlign: excel.VerticalAlign.Center,
    textWrapping: excel.TextWrapping.WrapText,
    leftBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    rightBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    topBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    bottomBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
  );

  static final _numberStyle = excel.CellStyle(
    fontSize: 10,
    fontColorHex: excel.ExcelColor.black,
    horizontalAlign: excel.HorizontalAlign.Right,
    verticalAlign: excel.VerticalAlign.Center,
    textWrapping: excel.TextWrapping.WrapText,
    leftBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    rightBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    topBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
    bottomBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
  );

  /// 매출 통계를 엑셀 파일로 내보내는 메인 메서드
  static Future<File> exportSalesData({
    required Map<DateTime, Map<String, dynamic>> dailyStats,
    required List<Map<String, dynamic>> productStats,
    required DateTimeRange? dateRange,
    required String eventName, // 행사명 추가
  }) async {
    final excelFile = excel.Excel.createExcel();

    // 먼저 필요한 시트들 생성
    excelFile['종합 리포트']; // 시트 생성
    final summarySheet = excelFile['종합 리포트'];
    _createSummarySheet(summarySheet, dailyStats, dateRange);

    // 두 번째 탭: 상품별 상세
    excelFile['상품별 상세']; // 시트 생성
    final productSheet = excelFile['상품별 상세'];
    _createProductDetailSheet(productSheet, productStats, dateRange);

    // 시트 생성 후 기본 시트들 삭제 (Sheet1 등)
    final existingSheets = excelFile.sheets.keys.toList();
    for (final sheetName in existingSheets) {
      if (sheetName != '종합 리포트' && sheetName != '상품별 상세') {
        excelFile.delete(sheetName);
      }
    }

    // 열 크기 설정 (개선된 방식)
    _setColumnWidths(summarySheet);
    _setColumnWidths(productSheet);

    // 상품별 상세 시트의 열 크기 별도 조정 (A열=스페이서, 데이터는 B열부터)
    try {
      productSheet.setColumnWidth(0, 2.0);  // A열 스페이서 (크기 2.0)
      productSheet.setColumnWidth(1, 30.0); // 상품명 (B열)
      productSheet.setColumnWidth(2, 12.0); // 판매량 (C열)
      productSheet.setColumnWidth(3, 20.0); // 총매출 (D열)
    } catch (e) {
      debugPrint('상품별 상세 열 크기 설정 실패: $e');
    }

    return _saveExcelFile(excelFile, eventName);
  }

  /// 전체 통계 데이터로 엑셀 파일 생성 (선입금, 판매자 정보 포함)
  static Future<File> exportFullStatsData({
    required Map<String, dynamic> statsData,
    required DateTimeRange? dateRange,
    required String eventName, // 행사명 추가
  }) async {
    final excelFile = excel.Excel.createExcel();

    // 먼저 필요한 시트들 생성
    excelFile['종합 리포트']; // 시트 생성
    final summarySheet = excelFile['종합 리포트'];
    _createFullSummarySheet(summarySheet, statsData, dateRange);

    // 두 번째 탭: 상품별 상세
    excelFile['상품별 상세']; // 시트 생성
    final productSheet = excelFile['상품별 상세'];
    final productStats = statsData['productStats'] as Map<String, Map<String, int>>? ?? {};
    _createFullProductDetailSheet(productSheet, productStats, dateRange);

    // 세 번째 탭: 세트 판매 상세
    excelFile['세트 판매 상세']; // 시트 생성
    final setDiscountSheet = excelFile['세트 판매 상세'];
    final setDiscountStats = statsData['setDiscountStats'] as Map<String, Map<String, int>>? ?? {};
    _createSetDiscountDetailSheet(setDiscountSheet, setDiscountStats, dateRange);

    // 시트 생성 후 기본 시트들 삭제 (Sheet1 등)
    final existingSheets = excelFile.sheets.keys.toList();
    for (final sheetName in existingSheets) {
      if (sheetName != '종합 리포트' && sheetName != '상품별 상세' && sheetName != '세트 판매 상세') {
        excelFile.delete(sheetName);
      }
    }

    // 열 크기 설정
    _setColumnWidths(summarySheet);
    _setColumnWidths(productSheet);
    _setColumnWidths(setDiscountSheet);

    // 상품별 상세 시트의 열 크기 별도 조정 (A열=스페이서, 데이터는 B열부터)
    try {
      productSheet.setColumnWidth(0, 2.0);  // A열 스페이서 (크기 2.0)
      productSheet.setColumnWidth(1, 30.0); // 상품명 (B열)
      productSheet.setColumnWidth(2, 12.0); // 판매량 (C열)
      productSheet.setColumnWidth(3, 20.0); // 총매출 (D열)
    } catch (e) {
      debugPrint('상품별 상세 열 크기 설정 실패: $e');
    }

    return _saveExcelFile(excelFile, eventName);
  }

  /// 종합 리포트 시트 생성
  static void _createSummarySheet(
    excel.Sheet sheet,
    Map<DateTime, Map<String, dynamic>> dailyStats,
    DateTimeRange? dateRange,
  ) {
    int row = 2; // 1행은 스페이서로 사용, 2행부터 데이터 시작

    // 1행 높이 설정 (스페이서)
    try {
      sheet.setRowHeight(0, 2.6); // 2.0 * 1.3 = 2.6 (제목행 높이 증가)
    } catch (e) {
      debugPrint('1행 높이 설정 실패: $e');
    }

    // 시트 제목 (개선된 병합)
    _createAdvancedMergedHeader(sheet, 'B2', 'D2', '📊 매출 종합 분석 리포트', _titleStyle);
    row += 2;

    // 기간 정보
    if (dateRange != null) {
      final startStr = '${dateRange.start.year}.${dateRange.start.month.toString().padLeft(2, '0')}.${dateRange.start.day.toString().padLeft(2, '0')}';
      final endStr = '${dateRange.end.year}.${dateRange.end.month.toString().padLeft(2, '0')}.${dateRange.end.day.toString().padLeft(2, '0')}';
      sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📅 분석 기간: $startStr ~ $endStr');
      sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _subtitleStyle;
      row += 2;
    }

    // 요약 통계
    final summaryData = _calculateSummaryStats(dailyStats);
    
    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('💰 총 매출액');
    sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(summaryData['totalSales'] ?? 0));
    sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _numberStyle;
    row++;

    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('🛍️ 총 주문 건수');
    sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.IntCellValue(_safeToInt(summaryData['totalOrders']));
    sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _numberStyle;
    row++;

    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📦 총 상품 수량');
    sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.IntCellValue(_safeToInt(summaryData['totalQuantity']));
    sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _numberStyle;
    row++;

    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('💵 평균 주문 금액');
    sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(summaryData['averageOrderValue'] ?? 0));
    sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _numberStyle;
    row += 2;

    // 일별 매출 테이블
    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📈 일별 매출 현황');
    sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _subtitleStyle;
    row += 2;

    // 테이블 헤더 - 올바른 방식으로 수정
    final headers = ['날짜', '매출액', '주문 건수', '상품 수량', '평균 주문액'];
    for (int i = 0; i < headers.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress)); // 셀을 한 번만 가져오기
      cell.value = excel.TextCellValue(headers[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    // 일별 데이터
    final sortedEntries = dailyStats.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    for (final entry in sortedEntries) {
      final date = entry.key;
      final stats = entry.value;
      
      final dateStr = '${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}';
      
      sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue(dateStr);
      sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _dataStyle;
      
      sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(_safeToInt(stats['totalSales'])));
      sheet.cell(excel.CellIndex.indexByString('C$row')).cellStyle = _numberStyle;
      
      sheet.cell(excel.CellIndex.indexByString('D$row')).value = excel.IntCellValue(_safeToInt(stats['orderCount']));
      sheet.cell(excel.CellIndex.indexByString('D$row')).cellStyle = _numberStyle;
      
      sheet.cell(excel.CellIndex.indexByString('E$row')).value = excel.IntCellValue(_safeToInt(stats['totalQuantity']));
      sheet.cell(excel.CellIndex.indexByString('E$row')).cellStyle = _numberStyle;
      
      final orderCount = _safeToInt(stats['orderCount']);
      final totalSales = _safeToDouble(stats['totalSales']);
      final avgValue = orderCount > 0 ? totalSales / orderCount : 0.0;
      sheet.cell(excel.CellIndex.indexByString('F$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(avgValue.round()));
      sheet.cell(excel.CellIndex.indexByString('F$row')).cellStyle = _numberStyle;
      
      row++;
    }
  }

  /// 전체 통계 데이터를 포함한 종합 리포트 시트 생성
  static void _createFullSummarySheet(
    excel.Sheet sheet,
    Map<String, dynamic> statsData,
    DateTimeRange? dateRange,
  ) {
    int row = 2; // 1행은 스페이서로 사용, 2행부터 데이터 시작

    // 1행 높이 설정 (스페이서)
    try {
      sheet.setRowHeight(0, 2.6); // 2.0 * 1.3 = 2.6 (제목행 높이 증가)
    } catch (e) {
      debugPrint('1행 높이 설정 실패: $e');
    }

    // 시트 제목 (확장된 병합)
    _createAdvancedMergedHeader(sheet, 'B2', 'D2', '📊 매출 종합 분석 리포트', _titleStyle);
    row += 2;

    // 기간 정보
    if (dateRange != null) {
      final startStr = '${dateRange.start.year}.${dateRange.start.month.toString().padLeft(2, '0')}.${dateRange.start.day.toString().padLeft(2, '0')}';
      final endStr = '${dateRange.end.year}.${dateRange.end.month.toString().padLeft(2, '0')}.${dateRange.end.day.toString().padLeft(2, '0')}';
      sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📅 분석 기간: $startStr ~ $endStr');
      sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _subtitleStyle;
      row += 2;
    }

    // 핵심 지표 섹션
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '📈 핵심 지표', _subtitleStyle);
    row += 1; // 간격 줄이기

    // 핵심 지표 헤더 추가
    sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue('항목');
    sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _headerStyle;
    sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.TextCellValue('값');
    sheet.cell(excel.CellIndex.indexByString('C$row')).cellStyle = _headerStyle;
    row++;

    final coreMetrics = [
      ['총 매출', CurrencyUtils.formatCurrency(statsData['totalRevenue'] ?? 0)],
      ['총 거래 건수', '${statsData['totalTransactions'] ?? 0}건'],
      ['평균 거래액', CurrencyUtils.formatCurrency(statsData['averageTransaction'] ?? 0)],
      ['총 판매량', '${statsData['totalQuantity'] ?? 0}개'],
    ];

    for (final metric in coreMetrics) {
      sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue(metric[0]); // B열부터 시작 (A열은 스페이서)
      sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _dataStyle;
      sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.TextCellValue(metric[1]); // 값도 C열로 이동
      sheet.cell(excel.CellIndex.indexByString('C$row')).cellStyle = _numberStyle;
      _setCellBorder(sheet, 'B$row');
      _setCellBorder(sheet, 'C$row');
      row++;
    }
    row += 1; // 간격 줄이기

    // 선입금 현황 섹션
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '💰 선입금 현황', _subtitleStyle);
    row += 1; // 간격 줄이기

    final prepaymentHeaders = ['항목', '금액', '건수'];
    for (int i = 0; i < prepaymentHeaders.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress)); // 셀을 한 번만 가져오기
      cell.value = excel.TextCellValue(prepaymentHeaders[i]);
      cell.cellStyle = _headerStyle; // 테두리도 포함된 완전한 스타일
      // _setCellBorder 제거 - 이미 _headerStyle에 테두리 포함됨
    }
    row++;

    final prepaymentData = [
      ['총 선입금', CurrencyUtils.formatCurrency(statsData['totalPrepaymentAmount'] ?? 0), '${statsData['totalPrepaymentCount'] ?? 0}건'],
      ['수령 완료', CurrencyUtils.formatCurrency(statsData['receivedPrepaymentAmount'] ?? 0), '${statsData['receivedPrepaymentCount'] ?? 0}건'],
      ['미수령', CurrencyUtils.formatCurrency(statsData['pendingPrepaymentAmount'] ?? 0), '${statsData['pendingPrepaymentCount'] ?? 0}건'],
    ];

    for (final data in prepaymentData) {
      for (int i = 0; i < data.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(data[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }
    row += 1; // 간격 줄이기

    // 카테고리별 분석 섹션
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '📂 카테고리별 매출', _subtitleStyle);
    row += 1; // 간격 줄이기

    final categoryHeaders = ['카테고리', '판매량', '총 매출'];
    for (int i = 0; i < categoryHeaders.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(categoryHeaders[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    final categoryStats = statsData['categoryStats'] as Map<String, Map<String, int>>? ?? {};
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    for (final entry in sortedCategories.take(10)) { // 상위 10개 카테고리
      final categoryData = [
        entry.key,
        '${entry.value['quantity'] ?? 0}개',
        CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0),
      ];

      for (int i = 0; i < categoryData.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(categoryData[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }
    row += 1; // 간격 줄이기

    // 판매자별 통계 섹션
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '👥 판매자별 통계', _subtitleStyle);
    row += 1; // 간격 줄이기

    final sellerHeaders = ['판매자', '거래 건수', '총 매출'];
    for (int i = 0; i < sellerHeaders.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(sellerHeaders[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    final sellerStats = statsData['sellerStats'] as Map<String, Map<String, int>>? ?? {};
    final sortedSellers = sellerStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    for (final entry in sortedSellers.take(10)) { // 상위 10명 판매자
      final sellerData = [
        entry.key,
        '${entry.value['count'] ?? 0}건',
        CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0),
      ];

      for (int i = 0; i < sellerData.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(sellerData[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }
    row += 1; // 간격 줄이기

    // 할인 내역 섹션
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '💸 할인 내역', _subtitleStyle);
    row += 1;

    final discountHeaders = ['할인 종류', '건수', '할인액'];
    for (int i = 0; i < discountHeaders.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(discountHeaders[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    final discountData = [
      ['세트 할인', '${statsData['setDiscountCount'] ?? 0}건', CurrencyUtils.formatCurrency(statsData['totalSetDiscountAmount'] ?? 0)],
      ['수동 할인', '${statsData['manualDiscountCount'] ?? 0}건', CurrencyUtils.formatCurrency(statsData['totalManualDiscountAmount'] ?? 0)],
    ];

    for (final data in discountData) {
      for (int i = 0; i < data.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row';
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(data[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }
    row += 1; // 간격 줄이기

    // 서비스 제공 섹션
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '🎁 서비스 제공', _subtitleStyle);
    row += 1;

    final serviceHeaders = ['항목', '값'];
    for (int i = 0; i < serviceHeaders.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(serviceHeaders[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    final serviceData = [
      ['서비스 상품 종류', '${statsData['serviceProductTypes'] ?? 0}종류'],
      ['서비스 제공 횟수', '${statsData['serviceCount'] ?? 0}건'],
      ['서비스 제공 수량', '${statsData['totalServiceQuantity'] ?? 0}개'],
    ];

    for (final data in serviceData) {
      for (int i = 0; i < data.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row';
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(data[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }
  }

  /// 전체 상품별 상세 시트 생성 
  static void _createFullProductDetailSheet(
    excel.Sheet sheet,
    Map<String, Map<String, int>> productStats,
    DateTimeRange? dateRange,
  ) {
    int row = 2; // 1행은 스페이서로 사용, 2행부터 데이터 시작

    // 1행 높이 설정 (스페이서)
    try {
      sheet.setRowHeight(0, 2.6); // 2.0 * 1.3 = 2.6 (제목행 높이 증가)
    } catch (e) {
      debugPrint('1행 높이 설정 실패: $e');
    }

    // 시트 제목 (확장된 병합)
    _createAdvancedMergedHeader(sheet, 'B2', 'D2', '🛍️ 상품별 매출 상세', _titleStyle);
    row += 2;

    // 기간 정보
    if (dateRange != null) {
      final startStr = '${dateRange.start.year}.${dateRange.start.month.toString().padLeft(2, '0')}.${dateRange.start.day.toString().padLeft(2, '0')}';
      final endStr = '${dateRange.end.year}.${dateRange.end.month.toString().padLeft(2, '0')}.${dateRange.end.day.toString().padLeft(2, '0')}';
      sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📅 분석 기간: $startStr ~ $endStr');
      sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _subtitleStyle;
      row += 2;
    }

    // 총 상품 수 정보 (셀 병합으로 줄바뀜 방지)
    _createAdvancedMergedHeader(sheet, 'B$row', 'D$row', '📊 총 ${productStats.length}개 상품', _subtitleStyle);
    row += 2;

    // 테이블 헤더
    final headers = ['상품명', '판매량', '총 매출'];
    for (int i = 0; i < headers.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(headers[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    // 상품별 데이터 (매출액 기준 정렬)
    final sortedProducts = productStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    for (final entry in sortedProducts) {
      final productData = [
        entry.key,
        '${entry.value['quantity'] ?? 0}개',
        CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0),
      ];

      for (int i = 0; i < productData.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(productData[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }
  }

  /// 세트 판매 상세 시트 생성
  static void _createSetDiscountDetailSheet(
    excel.Sheet sheet,
    Map<String, Map<String, int>> setDiscountStats,
    DateTimeRange? dateRange,
  ) {
    int row = 2; // 1행은 스페이서로 사용, 2행부터 데이터 시작

    // 1행 높이 설정 (스페이서)
    try {
      sheet.setRowHeight(0, 2.0);
    } catch (e) {
      debugPrint('1행 높이 설정 실패: $e');
    }

    // 제목 행 높이 설정 (1.3배 증가)
    try {
      sheet.setRowHeight(1, 2.6);
    } catch (e) {
      debugPrint('제목 행 높이 설정 실패: $e');
    }

    // 시트 제목
    _createAdvancedMergedHeader(sheet, 'B2', 'D2', '💰 세트 판매 상세 분석', _titleStyle);
    row += 2;

    // 기간 정보
    if (dateRange != null) {
      final startStr = '${dateRange.start.year}.${dateRange.start.month.toString().padLeft(2, '0')}.${dateRange.start.day.toString().padLeft(2, '0')}';
      final endStr = '${dateRange.end.year}.${dateRange.end.month.toString().padLeft(2, '0')}.${dateRange.end.day.toString().padLeft(2, '0')}';
      sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue('📅 분석 기간: $startStr ~ $endStr');
      sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _subtitleStyle;
      row += 2;
    }

    // 테이블 헤더
    final headers = ['세트명', '적용 횟수', '총 할인액'];
    for (int i = 0; i < headers.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(headers[i]);
      cell.cellStyle = _headerStyle;
    }
    row++;

    // 세트별 데이터 (할인액 기준 정렬)
    final sortedSets = setDiscountStats.entries.toList()
      ..sort((a, b) => (b.value['totalDiscount'] ?? 0).compareTo(a.value['totalDiscount'] ?? 0));

    for (final entry in sortedSets) {
      final setData = [
        entry.key,
        '${entry.value['count'] ?? 0}회',
        CurrencyUtils.formatCurrency(entry.value['totalDiscount'] ?? 0),
      ];

      for (int i = 0; i < setData.length; i++) {
        final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(setData[i]);
        sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = i == 0 ? _dataStyle : _numberStyle;
        _setCellBorder(sheet, cellAddress);
      }
      row++;
    }

    // 합계 행
    if (sortedSets.isNotEmpty) {
      row++;
      sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue('📊 전체 합계');
      sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _headerStyle;
      
      final totalCount = sortedSets.fold<int>(0, (sum, entry) => sum + (entry.value['count'] ?? 0));
      sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.TextCellValue('${totalCount}회');
      sheet.cell(excel.CellIndex.indexByString('C$row')).cellStyle = _headerStyle;
      
      final totalDiscount = sortedSets.fold<int>(0, (sum, entry) => sum + (entry.value['totalDiscount'] ?? 0));
      sheet.cell(excel.CellIndex.indexByString('D$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(totalDiscount));
      sheet.cell(excel.CellIndex.indexByString('D$row')).cellStyle = _headerStyle;
    }
  }

  /// 셀에 테두리 설정하는 헬퍼 메서드
  static void _setCellBorder(excel.Sheet sheet, String cellAddress) {
    try {
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      
      // 간단한 테두리가 있는 스타일 적용
      final borderStyle = excel.CellStyle(
        leftBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
        rightBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
        topBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
        bottomBorder: excel.Border(borderStyle: excel.BorderStyle.Thin),
      );
      
      cell.cellStyle = borderStyle;
    } catch (e) {
      debugPrint('셀 테두리 설정 실패 ($cellAddress): $e');
    }
  }

  /// 상품별 상세 시트 생성
  static void _createProductDetailSheet(
    excel.Sheet sheet,
    List<Map<String, dynamic>> productStats,
    DateTimeRange? dateRange,
  ) {
    int row = 2; // 1행은 스페이서로 사용, 2행부터 데이터 시작

    // 1행 높이 설정 (스페이서)
    try {
      sheet.setRowHeight(0, 2.0);
    } catch (e) {
      debugPrint('1행 높이 설정 실패: $e');
    }

    // 제목 행 높이 설정 (1.3배 증가)
    try {
      sheet.setRowHeight(1, 2.6);
    } catch (e) {
      debugPrint('제목 행 높이 설정 실패: $e');
    }

    // 시트 제목 (개선된 병합)
    _createAdvancedMergedHeader(sheet, 'B2', 'D2', '🛍️ 상품별 매출 상세 분석', _titleStyle);
    row += 2;

    // 기간 정보
    if (dateRange != null) {
      final startStr = '${dateRange.start.year}.${dateRange.start.month.toString().padLeft(2, '0')}.${dateRange.start.day.toString().padLeft(2, '0')}';
      final endStr = '${dateRange.end.year}.${dateRange.end.month.toString().padLeft(2, '0')}.${dateRange.end.day.toString().padLeft(2, '0')}';
      sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📅 분석 기간: $startStr ~ $endStr');
      sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _subtitleStyle;
      row += 2;
    }

    // 상품별 통계 테이블
    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('🎯 상품별 판매 성과');
    sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _subtitleStyle;
    row += 2;

    // 테이블 헤더
    final headers = ['순위', '상품명', '판매 수량', '매출액', '비중 (%)', '평균 단가'];
    for (int i = 0; i < headers.length; i++) {
      final cellAddress = String.fromCharCode(66 + i) + '$row'; // B열부터 시작 (A열은 스페이서)
      sheet.cell(excel.CellIndex.indexByString(cellAddress)).value = excel.TextCellValue(headers[i]);
      sheet.cell(excel.CellIndex.indexByString(cellAddress)).cellStyle = _headerStyle;
    }
    row++;

    // 상품 데이터 정렬 (매출액 기준)
    productStats.sort((a, b) => _safeToInt(b['totalSales']).compareTo(_safeToInt(a['totalSales'])));

    final totalSales = productStats.fold<int>(0, (sum, product) => sum + _safeToInt(product['totalSales']));

    for (int i = 0; i < productStats.length; i++) {
      final product = productStats[i];
      final rank = i + 1;
      
      sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.IntCellValue(rank);
      sheet.cell(excel.CellIndex.indexByString('B$row')).cellStyle = _dataStyle;
      
      sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.TextCellValue(product['productName'] ?? '');
      sheet.cell(excel.CellIndex.indexByString('C$row')).cellStyle = _dataStyle;
      
      sheet.cell(excel.CellIndex.indexByString('D$row')).value = excel.IntCellValue(_safeToInt(product['quantity']));
      sheet.cell(excel.CellIndex.indexByString('D$row')).cellStyle = _numberStyle;
      
      sheet.cell(excel.CellIndex.indexByString('E$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(_safeToInt(product['totalSales'])));
      sheet.cell(excel.CellIndex.indexByString('E$row')).cellStyle = _numberStyle;
      
      final productSales = _safeToDouble(product['totalSales']);
      final percentage = totalSales > 0 
        ? (productSales / totalSales * 100).toStringAsFixed(1)
        : '0.0';
      sheet.cell(excel.CellIndex.indexByString('F$row')).value = excel.TextCellValue('$percentage%');
      sheet.cell(excel.CellIndex.indexByString('F$row')).cellStyle = _numberStyle;
      
      final quantity = _safeToInt(product['quantity']);
      final avgPrice = quantity > 0 ? productSales / quantity : 0.0;
      sheet.cell(excel.CellIndex.indexByString('G$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(avgPrice.round()));
      sheet.cell(excel.CellIndex.indexByString('G$row')).cellStyle = _numberStyle;
      
      row++;
    }

    // 총합 행 추가
    row++;
    sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue('📊 전체 합계');
    sheet.cell(excel.CellIndex.indexByString('A$row')).cellStyle = _headerStyle;
    
    final totalQuantity = productStats.fold<int>(0, (sum, product) => sum + _safeToInt(product['quantity']));
    sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.IntCellValue(totalQuantity);
    sheet.cell(excel.CellIndex.indexByString('C$row')).cellStyle = _headerStyle;
    
    sheet.cell(excel.CellIndex.indexByString('D$row')).value = excel.TextCellValue(CurrencyUtils.formatCurrency(totalSales));
    sheet.cell(excel.CellIndex.indexByString('D$row')).cellStyle = _headerStyle;
    
    sheet.cell(excel.CellIndex.indexByString('E$row')).value = excel.TextCellValue('100.0%');
    sheet.cell(excel.CellIndex.indexByString('E$row')).cellStyle = _headerStyle;
  }

  /// 개선된 병합 헤더 생성 (더 나은 병합 및 스타일링)
  static void _createAdvancedMergedHeader(excel.Sheet sheet, String startCell, String endCell, String text, excel.CellStyle style) {
    try {
      // 병합 범위 설정
      final startIndex = excel.CellIndex.indexByString(startCell);
      final endIndex = excel.CellIndex.indexByString(endCell);
      
      sheet.merge(startIndex, endIndex);
      
      // 첫 번째 셀에 값과 스타일 적용
      final cell = sheet.cell(startIndex);
      cell.value = excel.TextCellValue(text);
      cell.cellStyle = style;
      
      // 서브타이틀 행 높이를 1.2배로 설정 (기본 15에서 18로)
      try {
        // Excel 패키지의 올바른 행 높이 설정 방법 시도
        sheet.setRowHeight(startIndex.rowIndex, 18.0);
      } catch (e) {
        debugPrint('행 높이 설정 실패: $e');
      }
      
      // 병합된 모든 셀에 스타일 적용 (더 나은 렌더링을 위해)
      for (int row = startIndex.rowIndex; row <= endIndex.rowIndex; row++) {
        for (int col = startIndex.columnIndex; col <= endIndex.columnIndex; col++) {
          if (row != startIndex.rowIndex || col != startIndex.columnIndex) {
            final cellIndex = excel.CellIndex.indexByColumnRow(columnIndex: col, rowIndex: row);
            sheet.cell(cellIndex).cellStyle = style;
          }
        }
      }
    } catch (e) {
      // 병합 실패시 일반 헤더로 대체
      _createSimpleMergedHeader(sheet, startCell, endCell, text, style);
    }
  }

  /// 간단한 병합 헤더 생성 (백업용)
  static void _createSimpleMergedHeader(excel.Sheet sheet, String startCell, String endCell, String text, excel.CellStyle style) {
    // 병합 범위 설정
    sheet.merge(excel.CellIndex.indexByString(startCell), excel.CellIndex.indexByString(endCell));
    
    // 첫 번째 셀에 값과 스타일 적용
    final cell = sheet.cell(excel.CellIndex.indexByString(startCell));
    cell.value = excel.TextCellValue(text);
    cell.cellStyle = style;
  }

  /// 향상된 열 크기 설정 (3가지 방법으로 시도)
  static void _setColumnWidths(excel.Sheet sheet) {
    final columnWidths = [2.0, 25.0, 15.0, 20.0, 15.0, 20.0]; // A열을 2.0으로 설정 (스페이서), 나머지는 B열부터 데이터
    
    try {
      // 방법 1: setColumnWidth 메서드
      for (int i = 0; i < columnWidths.length; i++) {
        sheet.setColumnWidth(i, columnWidths[i]);
      }
    } catch (e1) {
      try {
        // 방법 2: setDefaultColumnWidth 메서드 (매개변수 수정)
        for (int i = 0; i < columnWidths.length; i++) {
          sheet.setDefaultColumnWidth(columnWidths[i]);
        }
      } catch (e2) {
        try {
          // 방법 3: 각 열에 최소 너비를 보장하는 투명 스페이서 셀
          _setColumnWidthsWithSpacers(sheet, columnWidths);
        } catch (e3) {
          // 모든 방법 실패시 디버그 출력
          debugPrint('모든 열 너비 설정 방법이 실패했습니다: $e1, $e2, $e3');
        }
      }
    }
  }

  /// 스페이서를 이용한 열 너비 설정
  static void _setColumnWidthsWithSpacers(excel.Sheet sheet, List<double> widths) {
    for (int i = 0; i < widths.length; i++) {
      final spacerLength = (widths[i] * 0.8).round(); // 대략적인 문자 수
      final spacer = ' ' * spacerLength;
      final cellAddress = String.fromCharCode(66 + i) + '999'; // B열부터 시작, 마지막 행에 배치 (A열은 스페이서)
      
      final cell = sheet.cell(excel.CellIndex.indexByString(cellAddress));
      cell.value = excel.TextCellValue(spacer);
      cell.cellStyle = excel.CellStyle(
        fontSize: 1, // 매우 작은 폰트
        fontColorHex: excel.ExcelColor.white, // 투명하게
      );
    }
  }

  /// 요약 통계 계산
  static Map<String, int> _calculateSummaryStats(Map<DateTime, Map<String, dynamic>> dailyStats) {
    int totalSales = 0;
    int totalOrders = 0;
    int totalQuantity = 0;

    for (final stats in dailyStats.values) {
      totalSales += _safeToInt(stats['totalSales']);
      totalOrders += _safeToInt(stats['orderCount']);
      totalQuantity += _safeToInt(stats['totalQuantity']);
    }

    final averageOrderValue = totalOrders > 0 ? (totalSales / totalOrders).round() : 0;

    return {
      'totalSales': totalSales,
      'totalOrders': totalOrders,
      'totalQuantity': totalQuantity,
      'averageOrderValue': averageOrderValue,
    };
  }

  /// 엑셀 파일 저장
  static Future<File> _saveExcelFile(excel.Excel excelFile, String eventName) async {
    final directory = await getApplicationDocumentsDirectory();
    final now = DateTime.now();
    final dateString = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    final fileName = '바라 부스 매니저_${eventName}_$dateString.xlsx';
    final file = File('${directory.path}/$fileName');

    final bytes = excelFile.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
      return file;
    } else {
      throw Exception('엑셀 파일 생성에 실패했습니다.');
    }
  }
}
