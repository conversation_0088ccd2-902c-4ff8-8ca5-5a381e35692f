/// 바라 부스 매니저 - 구글 드라이브 백업 서비스
///
/// 구글 드라이브 API를 사용하여 사용자 데이터를 백업하고 복원하는 서비스입니다.
/// - 구글 계정 인증
/// - 데이터 백업 (JSON 형태)
/// - 데이터 복원
/// - 백업 파일 관리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;

import 'package:google_sign_in/google_sign_in.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../utils/logger_utils.dart';

/// 구글 드라이브 서비스
class GoogleDriveService {
  static const String _tag = 'GoogleDriveService';
  static const String _backupFolderName = 'BaraBackup';


  // 구글 드라이브 API 스코프
  static const List<String> _scopes = [
    'email',
    'https://www.googleapis.com/auth/drive.file',
  ];

  drive.DriveApi? _driveApi;
  GoogleSignIn? _googleSignIn;
  String? _accessToken;

  /// 구글 계정 인증 상태 확인
  bool get isAuthenticated => _accessToken != null && _driveApi != null;

  /// 현재 인증된 구글 계정 정보
  Future<GoogleSignInAccount?> get currentAccount async {
    try {
      _googleSignIn ??= GoogleSignIn(scopes: _scopes);
      // 이미 로그인되어 있으면 그대로 반환
      final current = _googleSignIn!.currentUser;
      if (current != null) return current;
      // 조용히 계정 정보 시도 (팝업 없음)
      return await _googleSignIn!.signInSilently(suppressErrors: true);
    } catch (_) {
      return null;
    }
  }

  /// 조용한 인증(팝업 없이). 성공 시 true 반환
  Future<bool> authenticateSilently() async {
    try {
      if (_driveApi != null) return true;
      // Firebase 사용자가 구글 로그인이라면 기존 인증 재사용 시도
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser?.providerData.any((p) => p.providerId == 'google.com') ?? false) {
        final reused = await _tryUseExistingGoogleAuth();
        if (reused) return true;
      }
      // signInSilently 시도
      _googleSignIn ??= GoogleSignIn(scopes: _scopes);
      final account = await _googleSignIn!.signInSilently(suppressErrors: true);
      if (account == null) return false;
      final headers = await account.authHeaders;
      final authHeader = headers['Authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) return false;
      _accessToken = authHeader.substring('Bearer '.length);
      _driveApi = drive.DriveApi(_GoogleAuthClient(headers));
      await _saveAuthInfo(account.email, 'managed_by_client');
      return true;
    } catch (_) {
      return false;
    }
  }

  /// 계정 선택 창을 강제로 띄우는 인증 플로우
  Future<bool> authenticateWithAccountPicker() async {
    try {
      LoggerUtils.logInfo('구글 드라이브 인증(계정 선택) 시작', tag: _tag);
      _googleSignIn ??= GoogleSignIn(scopes: _scopes);

      // 현재 캐시된 계정/사일런트 로그인 상태를 비워서 계정 선택 창이 뜨도록 유도
      try { await _googleSignIn!.signOut(); } catch (_) {}
      try { await _googleSignIn!.disconnect(); } catch (_) {}

      // 계정 선택 창 표시
      final account = await _googleSignIn!.signIn();
      if (account == null) {
        LoggerUtils.logWarning('사용자가 구글 로그인/권한 요청을 취소함', tag: _tag);
        return false;
      }

      // 필요한 스코프 보장
      final granted = await _googleSignIn!.requestScopes(_scopes);
      if (!granted) {
        LoggerUtils.logError('구글 드라이브 스코프 권한이 거부됨', tag: _tag);
        return false;
      }

      final headers = await account.authHeaders;
      final authHeader = headers['Authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) {
        LoggerUtils.logError('Authorization 헤더 없음', tag: _tag);
        return false;
      }
      _accessToken = authHeader.substring('Bearer '.length);
      _driveApi = drive.DriveApi(_GoogleAuthClient(headers));
      await _saveAuthInfo(account.email, 'managed_by_client');

      LoggerUtils.logInfo('구글 드라이브 인증(계정 선택) 성공: ${account.email}', tag: _tag);
      return true;
    } catch (e, st) {
      LoggerUtils.logError('구글 드라이브 인증(계정 선택) 실패', tag: _tag, error: e, stackTrace: st);
      return false;
    }
  }

  /// 구글 계정으로 인증 (google_sign_in 6.x 방식)
  Future<bool> authenticate() async {
    try {
      LoggerUtils.logInfo('구글 드라이브 인증 시작', tag: _tag);

      // 현재 Firebase 사용자가 구글로 로그인했는지 확인 후, 사일런트 시도
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser != null) {
        final isGoogleUser = firebaseUser.providerData.any((p) => p.providerId == 'google.com');
        if (isGoogleUser) {
          final reused = await _tryUseExistingGoogleAuth();
          if (reused) {
            LoggerUtils.logInfo('기존 구글 인증 정보 사용 성공', tag: _tag);
            return true;
          }
        }
      }

      // 1) Drive 스코프 포함한 GoogleSignIn 구성
      _googleSignIn = GoogleSignIn(scopes: _scopes);

      // 2) 사용자 로그인(계정 선택). null이면 사용자가 취소한 것
      final account = await _googleSignIn!.signIn();
      if (account == null) {
        LoggerUtils.logWarning('사용자가 구글 로그인/권한 요청을 취소함', tag: _tag);
        return false;
      }

      // 2-1) 필요한 스코프가 미부여 상태면 즉시 요청
      final granted = await _googleSignIn!.requestScopes(_scopes);
      if (!granted) {
        LoggerUtils.logError('구글 드라이브 스코프 권한이 거부됨', tag: _tag);
        return false;
      }

      // 3) authHeaders에서 Authorization 헤더 획득 → Bearer accessToken
      final headers = await account.authHeaders;
      final authHeader = headers['Authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) {
        LoggerUtils.logError('Authorization 헤더 없음(스코프 미승인/PlayServices 문제 가능)', tag: _tag);
        return false;
      }
      _accessToken = authHeader.substring('Bearer '.length);

      // 4) 인증 헤더를 부착하는 HTTP 클라이언트 생성 후 DriveApi 생성
      final authClient = _GoogleAuthClient(headers);
      _driveApi = drive.DriveApi(authClient);

      // 5) 인증 정보 저장(토큰은 클라이언트가 자동 관리하므로 마커 값 저장)
      await _saveAuthInfo(account.email, 'managed_by_client');

      LoggerUtils.logInfo('구글 드라이브 인증 성공: ${account.email}', tag: _tag);
      return true;
    } catch (e, stackTrace) {
      LoggerUtils.logError('구글 드라이브 인증 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 기존 구글 인증 정보 사용 시도 (signInSilently + authHeaders)
  Future<bool> _tryUseExistingGoogleAuth() async {
    try {
      _googleSignIn = GoogleSignIn(scopes: _scopes);

      // 이미 권한이 있는 경우 사일런트 로그인으로 account 획득
      final account = await _googleSignIn!.signInSilently(suppressErrors: true);
      if (account == null) return false;

      final headers = await account.authHeaders;
      final authHeader = headers['Authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) return false;

      _accessToken = authHeader.substring('Bearer '.length);
      final authClient = _GoogleAuthClient(headers);
      _driveApi = drive.DriveApi(authClient);
      return true;
    } catch (e) {
      LoggerUtils.logWarning('기존 구글 인증 정보 사용 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 인증 정보 저장
  Future<void> _saveAuthInfo(String email, String accessToken) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('google_drive_email', email);
      await prefs.setString('google_drive_access_token', accessToken);
      await prefs.setInt('google_drive_auth_time', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      LoggerUtils.logWarning('구글 드라이브 인증 정보 저장 실패', tag: _tag, error: e);
    }
  }

  /// 저장된 인증 정보 로드
  Future<Map<String, String>?> getStoredAuthInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString('google_drive_email');
      final accessToken = prefs.getString('google_drive_access_token');
      final authTime = prefs.getInt('google_drive_auth_time');

      if (email != null && accessToken != null && authTime != null) {
        // 토큰이 24시간 이내인지 확인
        final authDateTime = DateTime.fromMillisecondsSinceEpoch(authTime);
        final isValid = DateTime.now().difference(authDateTime).inHours < 24;

        if (isValid) {
          return {'email': email, 'accessToken': accessToken};
        }
      }
      return null;
    } catch (e) {
      LoggerUtils.logWarning('구글 드라이브 인증 정보 로드 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 로그아웃
  Future<void> signOut() async {
    try {
      await _googleSignIn?.signOut();
      _driveApi = null;
      _accessToken = null;

      // 저장된 인증 정보 삭제
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('google_drive_email');
      await prefs.remove('google_drive_access_token');
      await prefs.remove('google_drive_auth_time');

      LoggerUtils.logInfo('구글 드라이브 로그아웃 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구글 드라이브 로그아웃 실패', tag: _tag, error: e);
    }
  }

  /// 백업 폴더 찾기 또는 생성
  Future<String> _getOrCreateBackupFolder() async {
    if (_driveApi == null) throw Exception('구글 드라이브 인증이 필요합니다');

    try {
      // 기존 백업 폴더 검색
      final query = "name='$_backupFolderName' and mimeType='application/vnd.google-apps.folder' and trashed=false";
      final fileList = await _driveApi!.files.list(
        q: query,
        $fields: 'files(id,name)',
        spaces: 'drive',
        pageSize: 1,
      );

      if (fileList.files != null && fileList.files!.isNotEmpty) {
        final folderId = fileList.files!.first.id!;
        LoggerUtils.logInfo('기존 백업 폴더 발견: $folderId', tag: _tag);
        return folderId;
      }

      // 백업 폴더 생성
      final folder = drive.File()
        ..name = _backupFolderName
        ..mimeType = 'application/vnd.google-apps.folder';

      final createdFolder = await _driveApi!.files.create(
        folder,
        $fields: 'id',
      );
      final folderId = createdFolder.id!;

      LoggerUtils.logInfo('새 백업 폴더 생성: $folderId', tag: _tag);
      return folderId;

    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 폴더 생성/검색 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Timestamp/DateTime 등 JSON 비호환 객체를 직렬화 가능한 형태로 변환
  dynamic _sanitizeForJson(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value.toIso8601String();
    // Firestore Timestamp 대응 (런타임 타입 이름으로 체크)
    if (value.runtimeType.toString() == 'Timestamp') {
      try {
        final seconds = value.seconds as int?;
        final nanoseconds = value.nanoseconds as int? ?? 0;
        final dt = DateTime.fromMillisecondsSinceEpoch(seconds! * 1000 + (nanoseconds ~/ 1000000));
        return dt.toIso8601String();
      } catch (_) {
        return value.toString();
      }
    }
    if (value is Map) {
      return value.map((k, v) => MapEntry(k.toString(), _sanitizeForJson(v)));
    }
    if (value is Iterable) {
      return value.map(_sanitizeForJson).toList();
    }
    return value;
  }

  /// 데이터를 구글 드라이브에 백업 (단일 파일 덮어쓰기 전략)
  Future<bool> backupData(Map<String, dynamic> data) async {
    if (_driveApi == null) {
      LoggerUtils.logError('구글 드라이브 인증이 필요합니다', tag: _tag);
      return false;
    }

    try {
      LoggerUtils.logInfo('구글 드라이브 백업 시작', tag: _tag);

      final folderId = await _getOrCreateBackupFolder();
      // Timestamp 등 직렬화 불가 객체 정리
      final sanitized = _sanitizeForJson(data) as Map<String, dynamic>;
      final jsonData = jsonEncode(sanitized);
      const fileName = 'bara_backup.json';

      // 동일 이름 파일 존재 여부 확인
      final query = "parents in '$folderId' and name = '$fileName' and trashed=false";
      final existList = await _driveApi!.files.list(q: query, $fields: 'files(id)', spaces: 'drive', pageSize: 1);

      final bytes = utf8.encode(jsonData);
      final media = drive.Media(Stream.value(bytes), bytes.length);

      if (existList.files != null && existList.files!.isNotEmpty) {
        // 기존 파일 갱신
        final fileId = existList.files!.first.id!;
        final updated = await _driveApi!.files.update(drive.File()..name = fileName..mimeType = 'application/json', fileId, uploadMedia: media);
        LoggerUtils.logInfo('구글 드라이브 백업 업데이트 완료: ${updated.id}', tag: _tag);
      } else {
        // 새 파일 생성
        final meta = drive.File()
          ..name = fileName
          ..mimeType = 'application/json'
          ..parents = [folderId];
        final created = await _driveApi!.files.create(meta, uploadMedia: media);
        LoggerUtils.logInfo('구글 드라이브 백업 생성 완료: ${created.id}', tag: _tag);
      }

      return true;

    } catch (e, stackTrace) {
      LoggerUtils.logError('구글 드라이브 백업 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 구글 드라이브에서 백업 파일 목록 가져오기 (단일 파일 정책)
  Future<List<Map<String, dynamic>>> getBackupFiles() async {
    if (_driveApi == null) throw Exception('구글 드라이브 인증이 필요합니다');

    try {
      final folderId = await _getOrCreateBackupFolder();

      const fileName = 'bara_backup.json';
      final query = "parents in '$folderId' and name = '$fileName' and trashed=false";
      final fileList = await _driveApi!.files.list(
        q: query,
        $fields: 'files(id,name,createdTime,size)',
        spaces: 'drive',
        pageSize: 1,
      );

      final backupFiles = <Map<String, dynamic>>[];

      if (fileList.files != null) {
        for (final file in fileList.files!) {
          backupFiles.add({
            'id': file.id,
            'name': file.name,
            'createdTime': file.createdTime?.toIso8601String(),
            'size': file.size,
          });
        }
      }

      LoggerUtils.logInfo('백업 파일 목록 조회 완료: ${backupFiles.length}개', tag: _tag);
      return backupFiles;

    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 파일 목록 조회 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 구글 드라이브에서 백업 데이터 복원
  Future<Map<String, dynamic>?> restoreData(String fileId) async {
    if (_driveApi == null) throw Exception('구글 드라이브 인증이 필요합니다');

    try {
      LoggerUtils.logInfo('구글 드라이브에서 데이터 복원 시작: $fileId', tag: _tag);

      final media = await _driveApi!.files.get(fileId, downloadOptions: drive.DownloadOptions.fullMedia);

      if (media is! drive.Media) {
        throw Exception('파일 다운로드 실패');
      }

      final bytes = <int>[];
      await for (final chunk in media.stream) {
        bytes.addAll(chunk);
      }

      final jsonString = utf8.decode(bytes);
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      LoggerUtils.logInfo('구글 드라이브 데이터 복원 완료', tag: _tag);
      return data;

    } catch (e, stackTrace) {
      LoggerUtils.logError('구글 드라이브 데이터 복원 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 백업 파일 삭제
  Future<bool> deleteBackupFile(String fileId) async {
    if (_driveApi == null) return false;

    try {
      await _driveApi!.files.delete(fileId);
      LoggerUtils.logInfo('백업 파일 삭제 완료: $fileId', tag: _tag);
      return true;
    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 파일 삭제 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }
}

/// Authorization 헤더를 모든 요청에 부착하는 간단한 HTTP 클라이언트
class _GoogleAuthClient extends http.BaseClient {
  final Map<String, String> _headers;
  final http.Client _inner = http.Client();
  _GoogleAuthClient(this._headers);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    request.headers.addAll(_headers);
    return _inner.send(request);
  }
}



