/// 바라 부스 매니저 - 인앱 구독 서비스
///
/// iOS App Store와 Google Play Store의 인앱 구독을 관리하는 서비스입니다.
/// - 플랫폼별 인앱 구독 상품 관리
/// - 구독 상태 확인 및 복원
/// - Firebase와 구독 상태 동기화
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 1월
library;

import 'dart:async';
import 'dart:io';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/logger_utils.dart';
import '../models/subscription_plan.dart';
import 'subscription_service.dart';

/// 인앱 구독 서비스
class InAppPurchaseService {
  static const String _tag = 'InAppPurchaseService';
  
  // 인앱 구독 상품 ID들
  static const String _plusPlanProductId = 'parabara_plus_monthly';
  
  // Firebase 인스턴스
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // InAppPurchase 인스턴스
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  
  // 구독 상태 스트림
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  /// 싱글톤 인스턴스
  static final InAppPurchaseService _instance = InAppPurchaseService._internal();
  factory InAppPurchaseService() => _instance;
  InAppPurchaseService._internal();

  /// 현재 로그인된 사용자 ID 가져오기
  String? get _currentUserId => _auth.currentUser?.uid;

  /// 서비스 초기화
  Future<void> initialize() async {
    try {
      LoggerUtils.logInfo('인앱 구독 서비스 초기화 시작', tag: _tag);
      
      // 인앱 구매 사용 가능 여부 확인
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        LoggerUtils.logError('인앱 구매를 사용할 수 없습니다', tag: _tag);
        return;
      }
      
      // 플랫폼별 설정
      if (Platform.isAndroid) {
        // Android 특정 설정 (enablePendingPurchases는 더 이상 필요하지 않음)
        LoggerUtils.logInfo('Android 플랫폼 감지됨', tag: _tag);
      }
      
      // 구매 상태 변경 리스너 설정
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdated,
        onDone: () => LoggerUtils.logInfo('구매 스트림 종료', tag: _tag),
        onError: (error) => LoggerUtils.logError('구매 스트림 오류', tag: _tag, error: error),
      );
      
      LoggerUtils.logInfo('인앱 구독 서비스 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('인앱 구독 서비스 초기화 실패', tag: _tag, error: e);
    }
  }

  /// 서비스 종료
  void dispose() {
    _subscription.cancel();
  }

  /// 사용 가능한 구독 상품 조회
  Future<List<ProductDetails>> getAvailableProducts() async {
    try {
      LoggerUtils.logInfo('사용 가능한 구독 상품 조회 시작', tag: _tag);
      
      const Set<String> productIds = {_plusPlanProductId};
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);
      
      if (response.error != null) {
        LoggerUtils.logError('상품 조회 실패', tag: _tag, error: response.error);
        return [];
      }
      
      LoggerUtils.logInfo('조회된 상품 수: ${response.productDetails.length}', tag: _tag);
      return response.productDetails;
    } catch (e) {
      LoggerUtils.logError('상품 조회 중 오류 발생', tag: _tag, error: e);
      return [];
    }
  }

  /// 플러스 플랜 구독 시작
  Future<bool> subscribeToPlusPlan() async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('로그인이 필요합니다', tag: _tag);
        return false;
      }
      
      LoggerUtils.logInfo('플러스 플랜 구독 시작', tag: _tag);
      
      // 상품 정보 조회
      final products = await getAvailableProducts();
      final plusProduct = products.where((product) => product.id == _plusPlanProductId).firstOrNull;
      
      if (plusProduct == null) {
        LoggerUtils.logError('플러스 플랜 상품을 찾을 수 없습니다', tag: _tag);
        return false;
      }
      
      // 구매 요청 (구독의 경우 buyNonConsumable 사용)
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: plusProduct);
      final bool success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      
      LoggerUtils.logInfo('구매 요청 결과: $success', tag: _tag);
      return success;
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 구독 복원
  Future<void> restorePurchases() async {
    try {
      LoggerUtils.logInfo('구독 복원 시작', tag: _tag);
      await _inAppPurchase.restorePurchases();
      LoggerUtils.logInfo('구독 복원 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구독 복원 실패', tag: _tag, error: e);
    }
  }

  /// 현재 활성 구독 조회
  Future<List<PurchaseDetails>> getActiveSubscriptions() async {
    try {
      LoggerUtils.logInfo('활성 구독 조회 시작', tag: _tag);
      
      // 과거 구매 내역 조회
      await _inAppPurchase.restorePurchases();
      
      // 현재 활성 구독만 필터링하여 반환
      // 실제 구현에서는 구매 상태를 확인하여 활성 구독만 반환해야 함
      return [];
    } catch (e) {
      LoggerUtils.logError('활성 구독 조회 실패', tag: _tag, error: e);
      return [];
    }
  }

  /// 구매 상태 변경 처리
  void _onPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      LoggerUtils.logInfo('구매 상태 변경: ${purchaseDetails.status}', tag: _tag);
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          LoggerUtils.logInfo('구매 대기 중', tag: _tag);
          break;
        case PurchaseStatus.purchased:
          await _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.error:
          LoggerUtils.logError('구매 실패', tag: _tag, error: purchaseDetails.error);
          break;
        case PurchaseStatus.restored:
          await _handleRestoredPurchase(purchaseDetails);
          break;
        case PurchaseStatus.canceled:
          LoggerUtils.logInfo('구매 취소됨', tag: _tag);
          break;
      }
      
      // 구매 완료 처리
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// 성공한 구매 처리
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      LoggerUtils.logInfo('구매 성공 처리 시작: ${purchaseDetails.productID}', tag: _tag);
      
      if (purchaseDetails.productID == _plusPlanProductId) {
        // 플러스 플랜 구독 활성화
        await _activatePlusSubscription(purchaseDetails);
      }
      
      LoggerUtils.logInfo('구매 성공 처리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구매 성공 처리 실패', tag: _tag, error: e);
    }
  }

  /// 복원된 구매 처리
  Future<void> _handleRestoredPurchase(PurchaseDetails purchaseDetails) async {
    try {
      LoggerUtils.logInfo('구매 복원 처리 시작: ${purchaseDetails.productID}', tag: _tag);
      
      if (purchaseDetails.productID == _plusPlanProductId) {
        // 플러스 플랜 구독 복원
        await _activatePlusSubscription(purchaseDetails);
      }
      
      LoggerUtils.logInfo('구매 복원 처리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구매 복원 처리 실패', tag: _tag, error: e);
    }
  }

  /// 플러스 플랜 구독 활성화
  Future<void> _activatePlusSubscription(PurchaseDetails purchaseDetails) async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('사용자 ID가 없습니다', tag: _tag);
        return;
      }
      
      LoggerUtils.logInfo('플러스 플랜 구독 활성화 시작', tag: _tag);
      
      final now = DateTime.now();
      final endDate = DateTime(now.year, now.month + 1, now.day); // 1개월 후
      
      // Firebase에 구독 정보 저장
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .set({
        'userId': _currentUserId!,
        'planType': SubscriptionPlanType.plus.toString(),
        'subscriptionStartDate': now.toIso8601String(),
        'subscriptionEndDate': endDate.toIso8601String(),
        'isActive': true,
        'createdAt': now.toIso8601String(),
        'updatedAt': now.toIso8601String(),
        'purchaseId': purchaseDetails.purchaseID,
        'productId': purchaseDetails.productID,
        'transactionDate': purchaseDetails.transactionDate,
        'source': 'in_app_purchase',
      });
      
      // 기존 SubscriptionService도 업데이트
      final subscriptionService = SubscriptionService();
      await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.plus);
      
      LoggerUtils.logInfo('플러스 플랜 구독 활성화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 활성화 실패', tag: _tag, error: e);
    }
  }
}
