import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../models/prepayment_product_link.dart';
import '../models/product.dart';
import '../utils/logger_utils.dart';
import '../utils/excel_processor.dart';


class LinkService {
  final DatabaseService databaseService;

  LinkService({required this.databaseService});

  /// 상품-가상상품 연동 처리
  Future<int> linkProductsWithPrepayments({
    required List<ExcelPrepaymentData> prepaymentDataList,
    required BuildContext context,
    required int eventId, // 행사 ID 추가
  }) async {
    LoggerUtils.logInfo('상품-가상상품 연동 시작', tag: 'LinkService');
    final db = await databaseService.database;
    int linkedCount = 0;
    await db.transaction((txn) async {
      // 실제 DB에 저장된 현재 행사의 상품들의 ID 가져오기 (이름 일관성 적용)
      final productIds = <String, int>{};
      final allProducts = await txn.query(
        'products',
        where: 'eventId = ?',
        whereArgs: [eventId],
      );
      for (final productRow in allProducts) {
        final productName = (productRow['name'] as String).trim().toLowerCase();
        final productId = productRow['id'] as int;
        productIds[productName] = productId;
      }
      // 실제 DB에 저장된 현재 행사의 가상상품들의 ID 가져오기 (이름 일관성 적용)
      final virtualProductIds = <String, int>{};
      final allVirtualProducts = await txn.query(
        'prepayment_virtual_product',
        where: 'eventId = ?',
        whereArgs: [eventId],
      );
      LoggerUtils.logInfo('DB에 저장된 현재 행사(eventId: $eventId)의 가상상품 목록:', tag: 'LinkService');
      for (final vpRow in allVirtualProducts) {
        final vpName = (vpRow['name'] as String).trim().toLowerCase();
        final vpId = vpRow['id'] as int;
        virtualProductIds[vpName] = vpId;
        LoggerUtils.logInfo('  - id: $vpId, name: $vpName', tag: 'LinkService');
      }
      // 1. 엑셀에서 (가상상품명, 상품명) 쌍을 중복 없이 추출 (이름 일관성 적용)
      final Set<String> linkedPairs = {};
      for (final prepayment in prepaymentDataList) {
        for (final vp in prepayment.purchasedProducts.keys) {
          final vpName = vp.name.trim().toLowerCase();
          final productName = vpName;
          if (vpName.isEmpty || productName.isEmpty) continue;
          linkedPairs.add('$vpName||$productName');
        }
      }
      LoggerUtils.logInfo('엑셀에서 추출된 (가상상품, 상품) 쌍: ${linkedPairs.length}개', tag: 'LinkService');
      // 2. 중복 없이 1:1로만 연동 생성
      for (final pair in linkedPairs) {
        final split = pair.split('||');
        if (split.length != 2) continue;
        final vpName = split[0];
        final productName = split[1];
        // 반드시 DB에서 이름 기준으로 id를 찾아서 저장
        final virtualProductId = virtualProductIds[vpName];
        final productId = productIds[productName];
        LoggerUtils.logInfo('연동 시도: 가상상품명=$vpName, 상품명=$productName, 찾은 virtualProductId=$virtualProductId, productId=$productId', tag: 'LinkService');
        if (virtualProductId == null || virtualProductId == 0) {
          LoggerUtils.logWarning('연동 실패: id를 찾을 수 없음 (가상상품: $vpName, 상품: $productName)', tag: 'LinkService');
          continue;
        }
        if (productId == null || productId == 0) {
          LoggerUtils.logWarning('연동 실패: id를 찾을 수 없음 (상품: $productName)', tag: 'LinkService');
          continue;
        }
        // 이미 연동되어 있는지 확인 (행사별로 구분)
        final existing = await txn.query(
          'prepayment_product_link',
          where: 'virtualProductId = ? AND productId = ? AND eventId = ?',
          whereArgs: [virtualProductId, productId, eventId],
        );
        if (existing.isNotEmpty) {
          LoggerUtils.logInfo('이미 연동된 쌍: $vpName <-> $productName', tag: 'LinkService');
          continue;
        }
        await txn.insert('prepayment_product_link', {
          'virtualProductId': virtualProductId,
          'productId': productId,
          'linkedAt': DateTime.now().toIso8601String(),
          'eventId': eventId,
        });
        linkedCount++;
        LoggerUtils.logInfo('연동 생성: $vpName <-> $productName (id: $virtualProductId <-> $productId)', tag: 'LinkService');
      }
    });
    LoggerUtils.logInfo('상품-가상상품 연동 완료: $linkedCount개 생성', tag: 'LinkService');
    return linkedCount;
  }

  /// 상품-가상상품 연동 해제 및 재고 복구 처리
  Future<int> unlinkProductsWithPrepayments({
    required List<PrepaymentProductLink> linksToUnlink,
    required BuildContext context,
  }) async {
    LoggerUtils.logInfo('상품-가상상품 연동 해제 및 재고 복구 시작', tag: 'LinkService');
    final db = await databaseService.database;
    int unlinkedCount = 0;
    await db.transaction((txn) async {
      for (final link in linksToUnlink) {
        // 1. 연동 해제
        final deleted = await txn.delete(
          'prepayment_product_link',
          where: 'virtualProductId = ? AND productId = ?',
          whereArgs: [link.virtualProductId, link.productId],
        );
        if (deleted > 0) {
          LoggerUtils.logInfo('연동 해제: virtualProductId=${link.virtualProductId}, productId=${link.productId}', tag: 'LinkService');
          unlinkedCount++;
          // 2. 재고 복구 (상품 수량 증가)
          final productRows = await txn.query(
            'products',
            where: 'id = ?',
            whereArgs: [link.productId],
          );
          if (productRows.isNotEmpty) {
            final product = Product.fromMap(productRows.first);
            final updatedProduct = product.copyWith(quantity: product.quantity + 1); // 1개 복구(필요시 수량 파라미터화)
            await txn.update(
              'products',
              updatedProduct.toMap(),
              where: 'id = ?',
              whereArgs: [link.productId],
            );
            LoggerUtils.logInfo('재고 복구: 상품ID=${link.productId}, 복구 후 수량=${updatedProduct.quantity}', tag: 'LinkService');
          } else {
            LoggerUtils.logWarning('재고 복구 실패: 상품ID=${link.productId}를 찾을 수 없음', tag: 'LinkService');
          }
        } else {
          LoggerUtils.logWarning('연동 해제 실패: virtualProductId=${link.virtualProductId}, productId=${link.productId}', tag: 'LinkService');
        }
      }
    });
    LoggerUtils.logInfo('상품-가상상품 연동 해제 및 재고 복구 완료: $unlinkedCount개 해제', tag: 'LinkService');
    return unlinkedCount;
  }

}
