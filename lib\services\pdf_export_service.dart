import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../utils/currency_utils.dart';

/// PDF 내보내기 타입
enum PdfExportType {
  summary,   // 요약본
  detailed,  // 상세본 (모든 상품 포함)
}

class PdfExportService {
  // --------------------------------------------------------------
  // Font embedding (필수: 한글 깨짐 방지)
  // --------------------------------------------------------------
  // pdf 패키지는 기본 내장 폰트에 한글 글리프가 없어 반드시 한글 글리프가
  // 포함된 TrueType(OpenType) 폰트를 직접 임베드해야 합니다.
  // 현재 프로젝트에는 Pretendard *.otf 가 존재하므로 우선 이를 시도하고
  // (pdf 패키지는 TrueType outline 기반 OTF 는 로드 가능)
  // 실패 시 NotoSansKR (TTF) 추가를 권장합니다.
  // - 권장 파일 (Google Fonts):
  //   assets/fonts/NotoSansKR-Regular.ttf
  //   assets/fonts/NotoSansKR-Bold.ttf
  //   pubspec.yaml 내 fonts 섹션에 family: NotoSansKR 로 등록 후
  //   아래 _tryLoadFont 에서 경로만 교체하면 됩니다.

  static pw.Font? _regularFont; // Pretendard Regular or fallback
  static pw.Font? _boldFont;    // Pretendard Bold or fallback
  static bool _fontTried = false; // 중복 로드 방지

  static Future<void> _ensureFontsLoaded() async {
    if (_regularFont != null && _boldFont != null) return;
    if (_fontTried) return;
    _fontTried = true;

    Future<pw.Font?> _tryLoadTtf(String assetPath) async {
      try {
        final data = await rootBundle.load(assetPath);
        return pw.Font.ttf(data);
      } catch (e) {
        debugPrint('PDF TTF 폰트 로드 실패 ($assetPath): $e');
        return null;
      }
    }

    // 한글 깨짐 방지를 위해 TTF 기반 NotoSansKR 를 최우선 사용
    _regularFont = await _tryLoadTtf('assets/fonts/NotoSansKR-Regular.ttf');
    _boldFont    = await _tryLoadTtf('assets/fonts/NotoSansKR-Bold.ttf');

    // (선택) Pretendard OTF 는 CFF 기반이라 dart_pdf 에서 글리프 파싱 실패 가능성 -> 기본 비활성화
    // 필요 시 Pretendard 의 TTF 변환본(공식 배포 TTF) 을 추가한 뒤 아래 주석을 해제하세요.
    // if (_regularFont == null) {
    //   _regularFont = await _tryLoadTtf('assets/fonts/Pretendard-Regular.ttf');
    // }
    // if (_boldFont == null) {
    //   _boldFont = await _tryLoadTtf('assets/fonts/Pretendard-Bold.ttf');
    // }
  }

  /// 한글 지원 텍스트 스타일 생성 (임베드된 폰트 적용)
  static pw.TextStyle _createTextStyle({
    required double fontSize,
    pw.FontWeight? fontWeight,
    PdfColor? color,
  }) {
  final isBold = fontWeight == pw.FontWeight.bold; // pdf FontWeight 는 normal/bold 만 지원
    final pw.Font? selected = isBold ? (_boldFont ?? _regularFont) : _regularFont;
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight ?? pw.FontWeight.normal,
      color: color ?? PdfColors.black,
      font: selected, // null 이면 내장 폰트 -> 한글 깨짐 가능 (로그로 인지)
    );
  }

  /// 차트 이미지 캡처
  static Future<Uint8List?> _captureChart(GlobalKey? chartKey) async {
    if (chartKey?.currentContext == null) return null;
    
    try {
      RenderRepaintBoundary? boundary = chartKey!.currentContext!
          .findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return null;
      
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } catch (e) {
      print('차트 캡처 실패: $e');
      return null;
    }
  }

  /// 매출 통계를 PDF 파일로 내보내는 메인 메서드
  static Future<File> exportSalesData({
    required Map<DateTime, Map<String, dynamic>> dailyStats,
    required List<Map<String, dynamic>> productStats,
    required Map<String, dynamic> fullStats, // 전체 통계 데이터
    required DateTimeRange? dateRange,
    required String eventName,
    required Map<String, GlobalKey> chartKeys, // 다중 차트 키
    PdfExportType exportType = PdfExportType.summary, // 내보내기 타입
  }) async {
    await _ensureFontsLoaded();

    if (_regularFont == null) {
      // 여기 도달하면 _ensureFontsLoaded 가 성공하지 못한 것
      throw StateError('PDF 폰트 로딩 실패: 앱 완전 재시작 및 폰트 경로 확인 필요');
    }

    final pdf = pw.Document();
    final theme = pw.ThemeData.withFont(
      base: _regularFont!,
      bold: _boldFont ?? _regularFont!,
    );
    
    // 다중 차트 이미지 캡처
    final chartImages = <String, Uint8List?>{};
    for (final entry in chartKeys.entries) {
      chartImages[entry.key] = await _captureChart(entry.value);
    }

    // 표지 페이지
    _addCoverPage(pdf, theme, eventName, dateRange);

    // 내보내기 타입에 따른 페이지 구성
    if (exportType == PdfExportType.detailed) {
      // 상세 통계: 상품별 판매 현황만
      _addDetailedProductAnalysisPage(pdf, theme, fullStats, productStats);
    } else {
      // 요약 통계: 모든 페이지 포함
      // 통계 요약 페이지
      _addExecutiveSummary(pdf, theme, fullStats, dailyStats);

      // 매출 추이 페이지
      _addSalesTrendPage(pdf, theme, dailyStats, chartImages['main']);

      // 선입금 현황 페이지 
      _addPrepaymentStatusPage(pdf, theme, fullStats);

      // 서비스 현황 페이지
      _addServiceStatusPage(pdf, theme, fullStats);

      // 상품 분석 페이지 (TOP 10만)
      _addProductAnalysisPage(pdf, theme, fullStats, productStats);

      // 거래 유형 분석 페이지
      _addTransactionTypePage(pdf, theme, fullStats);

      // 판매자 성과 페이지 (해당하는 경우)
      if (_shouldShowSellerStats(fullStats)) {
        _addSellerPerformancePage(pdf, theme, fullStats);
      }
    }

    // PDF 파일 저장 - 의미 있는 파일명 생성
    final fileName = _generateFileName(eventName, dateRange);
    final output = await _getOutputFile(fileName);
    await output.writeAsBytes(await pdf.save());

    return output;
  }

  /// 표지 페이지
  static void _addCoverPage(pw.Document pdf, pw.ThemeData theme, String eventName, DateTimeRange? dateRange) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          final content = pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  '바라 부스 매니저 통계 리포트',
                  style: _createTextStyle(
                    fontSize: 32,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 30),
                pw.Text(
                  eventName.isNotEmpty ? '행사: $eventName' : '바라 부스 매니저 통계',
                  style: _createTextStyle(fontSize: 24),
                ),
                pw.SizedBox(height: 50),
                if (dateRange != null) ...[
                  pw.Text(
                    '기간: ${_formatDate(dateRange.start)} ~ ${_formatDate(dateRange.end)}',
                    style: _createTextStyle(fontSize: 16),
                  ),
                ],
                pw.SizedBox(height: 80),
                pw.Text(
                  '생성일: ${_formatDate(DateTime.now())}',
                  style: _createTextStyle(fontSize: 12),
                ),
              ],
            ),
          );
          return pw.Theme(data: theme, child: content);
        },
      ),
    );
  }

  /// 통계 요약 페이지
  static void _addExecutiveSummary(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats, Map<DateTime, Map<String, dynamic>> dailyStats) {
    final totalRevenue = fullStats['totalRevenue'] ?? 0;
    final totalTransactions = fullStats['totalTransactions'] ?? 0;
    final averageTransaction = fullStats['averageTransaction'] ?? 0;
    final totalQuantity = fullStats['totalQuantity'] ?? 0;
    final avgDailyRevenue = dailyStats.isNotEmpty ? totalRevenue / dailyStats.length : 0;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            pw.Header(
              level: 0,
              child: pw.Text(
                '기본 지표',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // 기본 지표 테이블 - 헤더와 데이터가 분리되지 않도록
            pw.Container(
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: pw.Column(
                children: [
                  // 테이블 헤더
                  pw.Container(
                    padding: const pw.EdgeInsets.all(12),
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey200,
                      borderRadius: pw.BorderRadius.only(
                        topLeft: pw.Radius.circular(4),
                        topRight: pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Row(
                      children: [
                        pw.Expanded(
                          child: pw.Text(
                            '항목',
                            style: _createTextStyle(
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Expanded(
                          child: pw.Text(
                            '값',
                            style: _createTextStyle(
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 테이블 데이터
                  _buildMetricRow('총 매출', CurrencyUtils.formatCurrency(totalRevenue)),
                  _buildMetricRow('총 거래 건수', '${totalTransactions}건'),
                  _buildMetricRow('평균 거래액', CurrencyUtils.formatCurrency(averageTransaction)),
                  _buildMetricRow('총 판매량', '${totalQuantity}개'),
                ],
              ),
            ),
            
            pw.SizedBox(height: 30),
            
            // 추가 분석 지표
            pw.Header(
              level: 1,
              child: pw.Text(
                '추가 분석 지표',
                style: _createTextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 15),
            
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
              children: [
                pw.Expanded(child: _buildSummaryCard('판매일수', '${dailyStats.length}일')),
                pw.SizedBox(width: 20),
                pw.Expanded(child: _buildSummaryCard('일평균 매출', CurrencyUtils.formatCurrency(avgDailyRevenue.round()))),
              ],
            ),
            
            pw.SizedBox(height: 30),
            
            // 핵심 인사이트
            pw.Header(
              level: 1,
              child: pw.Text(
                '주요 인사이트',
                style: _createTextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 15),
            
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                _buildInsightItem('▲', '일평균 매출', '${CurrencyUtils.formatCurrency(avgDailyRevenue.round())} (총 ${dailyStats.length}일간)'),
                pw.SizedBox(height: 8),
                _buildInsightItem('●', '평균 거래 규모', '건당 ${CurrencyUtils.formatCurrency(averageTransaction)}'),
                pw.SizedBox(height: 8),
                _buildInsightItem('□', '상품당 평균 판매량', totalTransactions > 0 ? '${(totalQuantity / totalTransactions).toStringAsFixed(1)}개' : '0개'),
              ],
            ),
          ];
        },
      ),
    );
  }

  /// 매출 추이 페이지
  static void _addSalesTrendPage(pw.Document pdf, pw.ThemeData theme, Map<DateTime, Map<String, dynamic>> dailyStats, Uint8List? chartImage) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            pw.Header(
              level: 0,
              child: pw.Text(
                '매출 추이 분석',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // 차트 - 페이지에 맞는 적절한 크기
            if (chartImage != null) ...[
              pw.Container(
                width: double.infinity,
                height: 280, // 페이지 높이를 고려한 적절한 크기
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                ),
                child: pw.Image(
                  pw.MemoryImage(chartImage),
                  fit: pw.BoxFit.contain,
                ),
              ),
              pw.SizedBox(height: 25),
            ],
            
            // 일별 매출 TOP 5
            pw.Header(
              level: 1,
              child: pw.Text(
                '최고 매출 TOP 5일',
                style: _createTextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 10),
            
            _buildTopDaysTable(dailyStats),
          ];
        },
      ),
    );
  }

  /// 상품 분석 페이지
  static void _addProductAnalysisPage(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats, List<Map<String, dynamic>> productStats) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            pw.Header(
              level: 0,
              child: pw.Text(
                '상품 분석',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // 상품별 매출 TOP 10
            pw.Header(
              level: 1,
              child: pw.Text(
                '상품별 매출 TOP 10',
                style: _createTextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 10),
            
            _buildTopProductsTable(productStats.take(10).toList()),
            
            pw.SizedBox(height: 25),
            
            // 카테고리별 통계 (있는 경우)
            if (fullStats['categoryStats'] != null) ...[
              pw.Header(
                level: 1,
                child: pw.Text(
                  '카테고리별 매출',
                  style: _createTextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 10),
              _buildCategoryTable(fullStats['categoryStats']),
            ],
          ];
        },
      ),
    );
  }

  /// 거래 유형 분석 페이지
  static void _addTransactionTypePage(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats) {
    final transactionTypeStats = fullStats['transactionTypeStats'] as Map<dynamic, Map<String, int>>? ?? {};
    
    if (transactionTypeStats.isEmpty) return;

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          final content = pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                '거래 유형 분석',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              
              _buildTransactionTypeTable(transactionTypeStats),
            ],
          );
          return pw.Theme(data: theme, child: content);
        },
      ),
    );
  }

  /// 판매자 성과 페이지
  static void _addSellerPerformancePage(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats) {
    final sellerStats = fullStats['sellerStats'] as Map<String, Map<String, int>>? ?? {};

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          final content = pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                '판매자 성과 분석',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              
              _buildSellerTable(sellerStats),
            ],
          );
          return pw.Theme(data: theme, child: content);
        },
      ),
    );
  }

  /// 세트할인 분석 페이지
  /// 요약 카드 생성
  static pw.Widget _buildSummaryCard(String title, String value) {
    return pw.Container(
      width: 200,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: _createTextStyle(
              fontSize: 12, 
              color: PdfColors.grey600,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            value,
            style: _createTextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 인기 상품 테이블 생성
  static pw.Widget _buildTopProductsTable(List<Map<String, dynamic>> products) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1),
      },
      children: [
        // 헤더
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('순위', isHeader: true),
            _buildTableCell('상품명', isHeader: true),
            _buildTableCell('수량', isHeader: true),
            _buildTableCell('매출', isHeader: true),
          ],
        ),
        // 데이터
        for (int i = 0; i < products.length; i++)
          pw.TableRow(
            children: [
              _buildTableCell('${i + 1}'),
              _buildTableCell(products[i]['productName']?.toString() ?? '-'),
              _buildTableCell('${products[i]['totalQuantity'] ?? 0}'),
              _buildTableCell(CurrencyUtils.formatCurrency(products[i]['totalRevenue'] ?? 0)),
            ],
          ),
      ],
    );
  }

  /// 테이블 셀 생성
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: _createTextStyle(
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.black : PdfColors.grey800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// 출력 파일 경로 생성
  static Future<File> _getOutputFile(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    return file;
  }

  /// 파일명 생성
  static String _generateFileName(String eventName, DateTimeRange? dateRange) {
    final today = DateTime.now();
    final dateStr = _formatDate(today);
    
    // 행사명을 파일명으로 사용 가능하게 정리
    final cleanEventName = eventName.isNotEmpty 
        ? _sanitizeFileName(eventName)
        : '통계';
    
    return '바라부스매니저_${cleanEventName}_$dateStr.pdf';
  }
  
  /// 파일명에서 사용할 수 없는 문자 제거/치환
  static String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')  // 파일명 금지문자를 _로 치환
        .replaceAll(RegExp(r'\s+'), '_')           // 연속 공백을 _로 치환
        .replaceAll(RegExp(r'_+'), '_')            // 연속 _를 하나로 통합
        .replaceAll(RegExp(r'^_|_$'), '');         // 앞뒤 _제거
  }

  /// 날짜 포맷 헬퍼
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 판매자 통계 표시 여부 확인
  static bool _shouldShowSellerStats(Map<String, dynamic> fullStats) {
    final sellerStats = fullStats['sellerStats'] as Map<String, Map<String, int>>? ?? {};
    return sellerStats.length > 1; // 판매자가 2명 이상일 때만 표시
  }

  /// 인사이트 아이템 생성
  static pw.Widget _buildInsightItem(String icon, String title, String value) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 12),
      child: pw.Row(
        children: [
          pw.Text(
            icon,
            style: _createTextStyle(fontSize: 16),
          ),
          pw.SizedBox(width: 10),
          pw.Expanded(
            child: pw.Text(
              title,
              style: _createTextStyle(
                fontSize: 14,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          pw.Text(
            value,
            style: _createTextStyle(
              fontSize: 14,
              color: PdfColors.blue800,
            ),
          ),
        ],
      ),
    );
  }

  /// 최고 매출일 테이블 생성
  static pw.Widget _buildTopDaysTable(Map<DateTime, Map<String, dynamic>> dailyStats) {
    final sortedDays = dailyStats.entries.toList()
      ..sort((a, b) => (b.value['totalRevenue'] as int).compareTo(a.value['totalRevenue'] as int));
    
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      children: [
        // 헤더
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('순위', isHeader: true),
            _buildTableCell('날짜', isHeader: true),
            _buildTableCell('매출', isHeader: true),
          ],
        ),
        // 데이터 (TOP 5)
        for (int i = 0; i < 5 && i < sortedDays.length; i++)
          pw.TableRow(
            children: [
              _buildTableCell('${i + 1}'),
              _buildTableCell(_formatDate(sortedDays[i].key)),
              _buildTableCell(CurrencyUtils.formatCurrency(sortedDays[i].value['totalRevenue'] ?? 0)),
            ],
          ),
      ],
    );
  }

  /// 카테고리별 테이블 생성
  static pw.Widget _buildCategoryTable(Map<String, Map<String, int>> categoryStats) {
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      children: [
        // 헤더
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('카테고리', isHeader: true),
            _buildTableCell('수량', isHeader: true),
            _buildTableCell('매출', isHeader: true),
          ],
        ),
        // 데이터
        for (final entry in sortedCategories.take(10))
          pw.TableRow(
            children: [
              _buildTableCell(entry.key),
              _buildTableCell('${entry.value['quantity'] ?? 0}'),
              _buildTableCell(CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0)),
            ],
          ),
      ],
    );
  }

  /// 거래 유형별 테이블 생성
  static pw.Widget _buildTransactionTypeTable(Map<dynamic, Map<String, int>> transactionTypeStats) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      children: [
        // 헤더
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('거래 유형', isHeader: true),
            _buildTableCell('건수', isHeader: true),
            _buildTableCell('금액', isHeader: true),
          ],
        ),
        // 데이터
        for (final entry in transactionTypeStats.entries)
          pw.TableRow(
            children: [
              _buildTableCell(_getTransactionTypeDisplayName(entry.key.toString())),
              _buildTableCell('${entry.value['count'] ?? 0}건'),
              _buildTableCell(CurrencyUtils.formatCurrency(entry.value['amount'] ?? 0)),
            ],
          ),
      ],
    );
  }

  /// 판매자별 테이블 생성
  static pw.Widget _buildSellerTable(Map<String, Map<String, int>> sellerStats) {
    final sortedSellers = sellerStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      children: [
        // 헤더
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('판매자', isHeader: true),
            _buildTableCell('건수', isHeader: true),
            _buildTableCell('매출', isHeader: true),
          ],
        ),
        // 데이터
        for (final entry in sortedSellers)
          pw.TableRow(
            children: [
              _buildTableCell(entry.key),
              _buildTableCell('${entry.value['count'] ?? 0}건'),
              _buildTableCell(CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0)),
            ],
          ),
      ],
    );
  }

  /// 선입금 현황 페이지
  static void _addPrepaymentStatusPage(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          final totalPrepaymentAmount = fullStats['totalPrepaymentAmount'] ?? 0;
          final receivedPrepaymentAmount = fullStats['receivedPrepaymentAmount'] ?? 0;
          final pendingPrepaymentAmount = fullStats['pendingPrepaymentAmount'] ?? 0;
          final totalPrepaymentCount = fullStats['totalPrepaymentCount'] ?? 0;
          final receivedPrepaymentCount = fullStats['receivedPrepaymentCount'] ?? 0;
          final pendingPrepaymentCount = fullStats['pendingPrepaymentCount'] ?? 0;
          final receivedRate = totalPrepaymentAmount > 0 ? (receivedPrepaymentAmount / totalPrepaymentAmount * 100) : 0.0;

          final content = pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                '선입금 현황 분석',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 30),
              
              // 선입금 요약 카드들
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                children: [
                  pw.Expanded(child: _buildSummaryCard('총 선입금', '${CurrencyUtils.formatCurrency(totalPrepaymentAmount)}')),
                  pw.SizedBox(width: 10),
                  pw.Expanded(child: _buildSummaryCard('수령 완료', '${CurrencyUtils.formatCurrency(receivedPrepaymentAmount)}')),
                  pw.SizedBox(width: 10),
                  pw.Expanded(child: _buildSummaryCard('미수령', '${CurrencyUtils.formatCurrency(pendingPrepaymentAmount)}')),
                ],
              ),
              
              pw.SizedBox(height: 30),
              
              // 수령률 표시
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.blue50,
                  border: pw.Border.all(color: PdfColors.blue200),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      '선입금 수령률: ${receivedRate.toStringAsFixed(1)}%',
                      style: _createTextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.blue800,
                      ),
                    ),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              // 선입금 상세 테이블
              pw.Text(
                '선입금 상세 현황',
                style: _createTextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 15),
              
              pw.Table(
                border: pw.TableBorder.all(color: PdfColors.grey400),
                columnWidths: {
                  0: const pw.FlexColumnWidth(2),
                  1: const pw.FlexColumnWidth(2),
                  2: const pw.FlexColumnWidth(1),
                },
                children: [
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                    children: [
                      _buildTableCell('구분', isHeader: true),
                      _buildTableCell('금액', isHeader: true),
                      _buildTableCell('건수', isHeader: true),
                    ],
                  ),
                  pw.TableRow(
                    children: [
                      _buildTableCell('총 선입금'),
                      _buildTableCell(CurrencyUtils.formatCurrency(totalPrepaymentAmount)),
                      _buildTableCell('${totalPrepaymentCount}건'),
                    ],
                  ),
                  pw.TableRow(
                    children: [
                      _buildTableCell('수령 완료'),
                      _buildTableCell(CurrencyUtils.formatCurrency(receivedPrepaymentAmount)),
                      _buildTableCell('${receivedPrepaymentCount}건'),
                    ],
                  ),
                  pw.TableRow(
                    children: [
                      _buildTableCell('미수령'),
                      _buildTableCell(CurrencyUtils.formatCurrency(pendingPrepaymentAmount)),
                      _buildTableCell('${pendingPrepaymentCount}건'),
                    ],
                  ),
                ],
              ),
            ],
          );
          return pw.Theme(data: theme, child: content);
        },
      ),
    );
  }

  /// 서비스 현황 페이지
  static void _addServiceStatusPage(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats) {
    final serviceProductTypes = fullStats['serviceProductTypes'] ?? 0;
    final serviceCount = fullStats['serviceCount'] ?? 0;
    final totalServiceQuantity = fullStats['totalServiceQuantity'] ?? 0;
    
    // 할인 데이터 (세트 할인 + 수동 할인)
    final totalSetDiscountAmount = fullStats['totalSetDiscountAmount'] ?? 0;
    final setDiscountCount = fullStats['setDiscountCount'] ?? 0;
    final totalManualDiscountAmount = fullStats['totalManualDiscountAmount'] ?? 0;
    final manualDiscountCount = fullStats['manualDiscountCount'] ?? 0;
    final totalDiscountAmount = fullStats['totalDiscountAmount'] ?? 0;
    final totalDiscountCount = fullStats['totalDiscountCount'] ?? 0;
    final setStats = fullStats['setDiscountStats'] as Map<String, Map<String, int>>? ?? {};

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          final content = pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                '서비스 현황 및 세트할인 분석',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              
              // 서비스 현황 요약
              pw.Text(
                '서비스 제공 현황',
                style: _createTextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 15),
              
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                children: [
                  pw.Expanded(child: _buildSummaryCard('서비스 종류', '${serviceProductTypes}종류')),
                  pw.SizedBox(width: 10),
                  pw.Expanded(child: _buildSummaryCard('제공 횟수', '${serviceCount}건')),
                  pw.SizedBox(width: 10),
                  pw.Expanded(child: _buildSummaryCard('제공 수량', '${totalServiceQuantity}개')),
                ],
              ),
              
              pw.SizedBox(height: 25),
              
              // 할인 분석 (세트 할인 + 수동 할인)
              if (totalDiscountAmount > 0 || totalDiscountCount > 0) ...[
                pw.Text(
                  '할인 분석',
                  style: _createTextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 15),

                // 전체 할인 요약
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                  children: [
                    pw.Expanded(child: _buildSummaryCard('총 할인 금액', CurrencyUtils.formatCurrency(totalDiscountAmount))),
                    pw.SizedBox(width: 10),
                    pw.Expanded(child: _buildSummaryCard('총 할인 건수', '${totalDiscountCount}건')),
                    pw.SizedBox(width: 10),
                    pw.Expanded(child: _buildSummaryCard('세트 종류', '${setStats.length}개')),
                  ],
                ),
                pw.SizedBox(height: 15),

                // 할인 유형별 상세
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                  children: [
                    pw.Expanded(child: _buildSummaryCard('세트 할인', '${CurrencyUtils.formatCurrency(totalSetDiscountAmount)} (${setDiscountCount}건)')),
                    pw.SizedBox(width: 10),
                    pw.Expanded(child: _buildSummaryCard('수동 할인', '${CurrencyUtils.formatCurrency(totalManualDiscountAmount)} (${manualDiscountCount}건)')),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // 세트별 상세 테이블
                if (setStats.isNotEmpty) ...[
                  pw.Text(
                    '세트별 할인 현황 TOP 5',
                    style: _createTextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  
                  _buildSetTable(setStats),
                ],
              ],
            ],
          );
          return pw.Theme(data: theme, child: content);
        },
      ),
    );
  }

  /// 상세 상품 분석 페이지 (모든 상품 포함)
  static void _addDetailedProductAnalysisPage(pw.Document pdf, pw.ThemeData theme, Map<String, dynamic> fullStats, List<Map<String, dynamic>> productStats) {
    // 상품별 통계를 매출순으로 정렬
    final sortedProducts = List<Map<String, dynamic>>.from(productStats);
    sortedProducts.sort((a, b) => (b['totalRevenue'] ?? 0).compareTo(a['totalRevenue'] ?? 0));

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        header: (pw.Context context) {
          return pw.Container(
            alignment: pw.Alignment.centerLeft,
            margin: const pw.EdgeInsets.only(bottom: 20),
            child: pw.Text(
              '상품별 상세 통계',
              style: _createTextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
          );
        },
        footer: (pw.Context context) {
          return pw.Container(
            alignment: pw.Alignment.centerRight,
            margin: const pw.EdgeInsets.only(top: 20),
            child: pw.Text(
              'Page ${context.pageNumber}/${context.pagesCount}',
              style: _createTextStyle(fontSize: 10, color: PdfColors.grey600),
            ),
          );
        },
        build: (pw.Context context) {
          return [
            // 제목과 설명
            pw.Header(
              level: 0,
              child: pw.Text(
                '전체 상품 판매 현황',
                style: _createTextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.Paragraph(
              text: '전체 ${sortedProducts.length}개 상품의 판매 현황을 매출 순으로 정리했습니다.',
              style: _createTextStyle(fontSize: 14, color: PdfColors.grey700),
            ),
            
            pw.SizedBox(height: 20),
            
            // 상품 테이블 - 자동으로 페이지 분할됨
            pw.TableHelper.fromTextArray(
              border: pw.TableBorder.all(color: PdfColors.grey400),
              headerDecoration: const pw.BoxDecoration(color: PdfColors.grey200),
              headerStyle: _createTextStyle(
                fontSize: 11,
                fontWeight: pw.FontWeight.bold,
              ),
              cellStyle: _createTextStyle(fontSize: 10),
              columnWidths: {
                0: const pw.FlexColumnWidth(3),
                1: const pw.FlexColumnWidth(1),
                2: const pw.FlexColumnWidth(2),
                3: const pw.FlexColumnWidth(1),
              },
              cellAlignment: pw.Alignment.center,
              headers: ['상품명', '수량', '매출', '순위'],
              data: sortedProducts.asMap().entries.map((entry) {
                final index = entry.key;
                final product = entry.value;
                return [
                  product['productName'] ?? '알 수 없음',
                  '${product['totalQuantity'] ?? 0}개',
                  CurrencyUtils.formatCurrency(product['totalRevenue'] ?? 0),
                  '${index + 1}위',
                ];
              }).toList(),
            ),
          ];
        },
      ),
    );
  }

  /// 거래 유형 표시명 반환
  static String _getTransactionTypeDisplayName(String type) {
    switch (type) {
      case 'TransactionType.sale':
        return '일반 판매';
      case 'TransactionType.discount':
        return '할인 판매';
      case 'TransactionType.refund':
        return '환불';
      default:
        return type;
    }
  }

  /// 세트별 테이블 생성
  static pw.Widget _buildSetTable(Map<String, Map<String, int>> setStats) {
    final sortedSets = setStats.entries.toList()
      ..sort((a, b) => (b.value['discountAmount'] ?? 0).compareTo(a.value['discountAmount'] ?? 0));

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      children: [
        // 헤더
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('세트명', isHeader: true),
            _buildTableCell('적용 횟수', isHeader: true),
            _buildTableCell('총 할인액', isHeader: true),
          ],
        ),
        // 데이터
        for (final entry in sortedSets.take(10))
          pw.TableRow(
            children: [
              _buildTableCell(entry.key),
              _buildTableCell('${entry.value['count'] ?? 0}회'),
              _buildTableCell(CurrencyUtils.formatCurrency(entry.value['discountAmount'] ?? 0)),
            ],
          ),
      ],
    );
  }

  /// 메트릭 행 생성 (테이블 행 대신 사용)
  static pw.Widget _buildMetricRow(String label, String value, {bool isLast = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: isLast ? pw.BorderSide.none : const pw.BorderSide(color: PdfColors.grey300),
        ),
      ),
      child: pw.Row(
        children: [
          pw.Expanded(
            child: pw.Text(
              label,
              style: _createTextStyle(fontSize: 11, color: PdfColors.grey700),
            ),
          ),
          pw.Expanded(
            child: pw.Text(
              value,
              style: _createTextStyle(fontSize: 11, fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

}
