/// 바라 부스 매니저 - 권한 관리 서비스
///
/// 행사 권한 관련 비즈니스 로직을 처리하는 서비스 클래스입니다.
/// - 사용자 권한 체크 및 검증
/// - 초대받은 사용자 식별
/// - 권한별 기능 제한 처리
/// - Firebase Firestore 연동
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_permission.dart';
import '../models/permission.dart';
import '../repositories/event_repository.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 권한 관리 서비스 클래스
class PermissionService {
  static const String _tag = 'PermissionService';

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DatabaseService _databaseService;

  PermissionService(this._databaseService);

  /// 현재 사용자의 특정 행사에 대한 권한 정보 조회 (로컬 기반)
  ///
  /// [eventId] 행사 ID
  /// 반환값: 권한 정보 (권한이 없으면 null)
  Future<EventPermission?> getUserPermission(int eventId) async {
    LoggerUtils.methodStart('getUserPermission', tag: _tag, data: {
      'eventId': eventId,
    });

    final user = _auth.currentUser;
    if (user == null) {
      LoggerUtils.logWarning('로그인되지 않은 사용자', tag: _tag);
      return null;
    }

    try {
      // 로컬 DB에서 행사 정보 조회
      final eventRepository = EventRepository(_databaseService);
      final event = await eventRepository.getEventById(eventId);

      if (event == null) {
        LoggerUtils.logInfo('행사를 찾을 수 없음: eventId=$eventId', tag: _tag);
        return null;
      }

      // 소유자인지 확인 (로컬 기반)
      if (event.isOwner && (event.ownerUserId == null || event.ownerUserId == user.uid)) {
        // 소유자 권한 반환
        final permission = EventPermission.createOwner(
          eventId: eventId,
          userId: user.uid,
          userNickname: await _getUserNickname(user.uid),
        );
        LoggerUtils.logInfo('소유자 권한 확인 (로컬): ${permission.userNickname}', tag: _tag);
        return permission;
      }

      // 게스트 초대/권한 기능 제거됨 (로컬 전용 모드). 소유자가 아니면 권한 없음으로 처리합니다.

      LoggerUtils.logInfo('권한 없음: eventId=$eventId, userId=${user.uid}', tag: _tag);
      return null;
    } catch (e) {
      LoggerUtils.logError('권한 정보 조회 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 사용자 닉네임 조회 (서버 조회 제거, 로컬/기본 처리)
  Future<String> _getUserNickname(String userId) async {
    try {
      // 서버 조회 제거됨: 로컬 저장소 혹은 기본 값으로 처리
      return '사용자';
    } catch (e) {
      LoggerUtils.logWarning('사용자 닉네임 조회 실패: $userId', tag: _tag, error: e);
      return '사용자';
    }
  }

  /// 현재 사용자가 특정 행사의 소유자인지 확인
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 소유자 여부
  Future<bool> isEventOwner(int eventId) async {
    final permission = await getUserPermission(eventId);
    return permission?.isOwner ?? false;
  }



  /// 특정 권한을 가지고 있는지 확인
  /// 
  /// [eventId] 행사 ID
  /// [permission] 확인할 권한
  /// 반환값: 권한 보유 여부
  Future<bool> hasPermission(int eventId, Permission permission) async {
    final userPermission = await getUserPermission(eventId);
    return userPermission?.hasPermission(permission) ?? false;
  }

  /// 읽기 권한 확인
  Future<bool> canRead(int eventId) async {
    return await hasPermission(eventId, Permission.read);
  }

  /// 다운로드 권한 확인
  Future<bool> canDownload(int eventId) async {
    return await hasPermission(eventId, Permission.download);
  }

  /// 선입금 관리 권한 확인
  Future<bool> canManagePrepayment(int eventId) async {
    return await hasPermission(eventId, Permission.prepaymentManage);
  }

  /// 생성/수정/삭제 권한 확인 (소유자만 가능)
  Future<bool> canModify(int eventId) async {
    return await isEventOwner(eventId);
  }

  /// 권한 없음 에러 메시지 반환
  String getPermissionDeniedMessage(Permission permission) {
    switch (permission) {
      case Permission.read:
        return '이 행사의 데이터를 볼 권한이 없습니다';
      case Permission.download:
        return '다운로드 권한이 없습니다';
      case Permission.prepaymentManage:
        return '선입금 관리 권한이 없습니다';
    }
  }

  /// 수정 권한 없음 에러 메시지
  String get modifyPermissionDeniedMessage => '초대받은 사용자는 데이터를 수정할 수 없습니다';



  /// 권한 제거 (추방)
  /// 
  /// [eventId] 행사 ID
  /// [userId] 제거할 사용자 ID
  Future<void> revokePermission(int eventId, String userId) async {
    LoggerUtils.methodStart('revokePermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
    });

    try {
      // 게스트 권한 시스템 제거됨: 더 이상 서버 권한 삭제를 수행하지 않습니다.
      LoggerUtils.logInfo('권한 제거 API는 비활성화됨 (게스트 기능 삭제)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('권한 제거 처리 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 행사의 모든 권한 사용자 목록 조회 (게스트 기능 삭제에 따라 항상 빈 목록 반환)
  Future<List<EventPermission>> getEventPermissions(int eventId) async {
    LoggerUtils.methodStart('getEventPermissions', tag: _tag, data: {
      'eventId': eventId,
    });
    LoggerUtils.logInfo('게스트 권한 사용자 목록 조회는 비활성화됨', tag: _tag);
    return [];
  }

  /// 현재 사용자가 접근 가능한 행사 ID 목록 조회 (게스트 기능 삭제)
  /// 소유한 행사만 로컬 DB 기준으로 반환
  Future<List<int>> getAccessibleEventIds() async {
    LoggerUtils.methodStart('getAccessibleEventIds', tag: _tag);

    final user = _auth.currentUser;
    if (user == null) {
      return [];
    }

    try {
      final eventIds = <int>[];

      // 로컬 DB에서 ownerUserId가 현재 사용자이거나 isOwner가 true인 행사만 수집
      // 간단 구현: 앱 전체 행사 로드 후 필터 (성능 문제 없을 정도 규모 가정)
      // 필요 시 Repository에 쿼리 메서드 추가 고려
      final db = await _databaseService.database;
      final rows = await db.query(DatabaseServiceImpl.eventsTable);
      for (final row in rows) {
        try {
          final ownerUserId = row['ownerUserId'] as String?;
          final isOwner = (row['isOwner'] as int? ?? 1) == 1;
          final id = row['id'] as int;
          if (isOwner && (ownerUserId == null || ownerUserId == user.uid)) {
            eventIds.add(id);
          }
        } catch (e) {
          LoggerUtils.logWarning('행사 ID 수집 실패: $row', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('접근 가능한 행사 ${eventIds.length}개 (소유 행사만)', tag: _tag);
      return eventIds;
    } catch (e) {
      LoggerUtils.logError('접근 가능한 행사 목록 조회 실패', tag: _tag, error: e);
      return [];
    }
  }
}
