import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger_utils.dart';

/// 전화번호 인증 제한 관리 서비스
/// 
/// 클라이언트 측에서 다음 제한사항을 관리합니다:
/// - 10분마다 한 번 SMS 발송 가능
/// - 같은 전화번호로 하루에 3번까지만 SMS 발송 가능
class PhoneVerificationLimitService {
  static const String _tag = 'PhoneVerificationLimitService';
  
  // SharedPreferences 키
  static const String _lastSmsTimeKey = 'last_sms_time';
  static const String _dailyCountPrefix = 'daily_sms_count_';
  static const String _dailyDatePrefix = 'daily_sms_date_';
  
  // 제한 설정
  static const int _smsIntervalMinutes = 10; // 10분 간격
  static const int _dailyMaxCount = 3; // 하루 최대 3번
  
  /// 10분 제한 확인
  /// 
  /// 반환값: true면 발송 가능, false면 아직 시간이 안됨
  static Future<bool> canSendSms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSmsTime = prefs.getInt(_lastSmsTimeKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = currentTime - lastSmsTime;
      final requiredInterval = _smsIntervalMinutes * 60 * 1000; // 밀리초로 변환
      
      final canSend = timeDifference >= requiredInterval;
      
      LoggerUtils.logInfo(
        '10분 제한 확인: ${canSend ? "발송 가능" : "대기 필요"}',
        tag: _tag,
        data: {
          'lastSmsTime': DateTime.fromMillisecondsSinceEpoch(lastSmsTime).toString(),
          'currentTime': DateTime.fromMillisecondsSinceEpoch(currentTime).toString(),
          'timeDifferenceMinutes': (timeDifference / (60 * 1000)).toStringAsFixed(1),
          'requiredIntervalMinutes': _smsIntervalMinutes,
        },
      );
      
      return canSend;
    } catch (e) {
      LoggerUtils.logError('10분 제한 확인 오류', error: e, tag: _tag);
      return false; // 오류 시 안전하게 false 반환
    }
  }
  
  /// 남은 대기 시간(분) 반환
  /// 
  /// 반환값: 남은 대기 시간(분), 0이면 즉시 발송 가능
  static Future<int> getRemainingWaitMinutes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSmsTime = prefs.getInt(_lastSmsTimeKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = currentTime - lastSmsTime;
      final requiredInterval = _smsIntervalMinutes * 60 * 1000; // 밀리초로 변환
      
      if (timeDifference >= requiredInterval) {
        return 0; // 즉시 발송 가능
      }
      
      final remainingMs = requiredInterval - timeDifference;
      final remainingMinutes = (remainingMs / (60 * 1000)).ceil();
      
      return remainingMinutes;
    } catch (e) {
      LoggerUtils.logError('남은 대기 시간 계산 오류', error: e, tag: _tag);
      return _smsIntervalMinutes; // 오류 시 전체 대기 시간 반환
    }
  }
  
  /// 하루 제한 확인
  /// 
  /// [phoneNumber] 확인할 전화번호
  /// 반환값: true면 발송 가능, false면 하루 제한 초과
  static Future<bool> canSendSmsToday(String phoneNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = _getTodayString();
      final countKey = '$_dailyCountPrefix$phoneNumber';
      final dateKey = '$_dailyDatePrefix$phoneNumber';
      
      final savedDate = prefs.getString(dateKey);
      final currentCount = prefs.getInt(countKey) ?? 0;
      
      // 날짜가 다르면 카운트 리셋
      if (savedDate != today) {
        await prefs.setString(dateKey, today);
        await prefs.setInt(countKey, 0);
        
        LoggerUtils.logInfo(
          '하루 제한 카운트 리셋',
          tag: _tag,
          data: {
            'phoneNumber': phoneNumber,
            'previousDate': savedDate,
            'currentDate': today,
          },
        );
        
        return true; // 새로운 날이므로 발송 가능
      }
      
      final canSend = currentCount < _dailyMaxCount;
      
      LoggerUtils.logInfo(
        '하루 제한 확인: ${canSend ? "발송 가능" : "제한 초과"}',
        tag: _tag,
        data: {
          'phoneNumber': phoneNumber,
          'currentCount': currentCount,
          'maxCount': _dailyMaxCount,
          'date': today,
        },
      );
      
      return canSend;
    } catch (e) {
      LoggerUtils.logError('하루 제한 확인 오류', error: e, tag: _tag);
      return false; // 오류 시 안전하게 false 반환
    }
  }
  
  /// 오늘 남은 SMS 발송 횟수 반환
  /// 
  /// [phoneNumber] 확인할 전화번호
  /// 반환값: 남은 발송 횟수
  static Future<int> getRemainingDailyCount(String phoneNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = _getTodayString();
      final countKey = '$_dailyCountPrefix$phoneNumber';
      final dateKey = '$_dailyDatePrefix$phoneNumber';
      
      final savedDate = prefs.getString(dateKey);
      final currentCount = prefs.getInt(countKey) ?? 0;
      
      // 날짜가 다르면 전체 횟수 반환
      if (savedDate != today) {
        return _dailyMaxCount;
      }
      
      return (_dailyMaxCount - currentCount).clamp(0, _dailyMaxCount);
    } catch (e) {
      LoggerUtils.logError('남은 일일 횟수 계산 오류', error: e, tag: _tag);
      return 0; // 오류 시 안전하게 0 반환
    }
  }
  
  /// SMS 발송 기록
  /// 
  /// [phoneNumber] 발송한 전화번호
  /// SMS 발송 성공 시 호출하여 제한 카운터를 업데이트합니다.
  static Future<void> recordSmsSent(String phoneNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final today = _getTodayString();
      
      // 10분 제한용 마지막 발송 시간 기록
      await prefs.setInt(_lastSmsTimeKey, currentTime);
      
      // 하루 제한용 카운트 증가
      final countKey = '$_dailyCountPrefix$phoneNumber';
      final dateKey = '$_dailyDatePrefix$phoneNumber';
      
      final savedDate = prefs.getString(dateKey);
      final currentCount = prefs.getInt(countKey) ?? 0;
      
      // 날짜가 다르면 카운트 리셋 후 1로 설정
      if (savedDate != today) {
        await prefs.setString(dateKey, today);
        await prefs.setInt(countKey, 1);
      } else {
        // 같은 날이면 카운트 증가
        await prefs.setInt(countKey, currentCount + 1);
      }
      
      LoggerUtils.logInfo(
        'SMS 발송 기록 완료',
        tag: _tag,
        data: {
          'phoneNumber': phoneNumber,
          'timestamp': DateTime.fromMillisecondsSinceEpoch(currentTime).toString(),
          'dailyCount': savedDate != today ? 1 : currentCount + 1,
          'date': today,
        },
      );
    } catch (e) {
      LoggerUtils.logError('SMS 발송 기록 오류', error: e, tag: _tag);
    }
  }
  
  /// 전체 제한 확인 (10분 + 하루 제한 모두 확인)
  /// 
  /// [phoneNumber] 확인할 전화번호
  /// 반환값: {canSend: bool, reason: String?, remainingMinutes: int?, remainingDailyCount: int?}
  static Future<Map<String, dynamic>> checkAllLimits(String phoneNumber) async {
    try {
      final canSendTime = await canSendSms();
      final canSendDaily = await canSendSmsToday(phoneNumber);
      
      if (!canSendTime) {
        final remainingMinutes = await getRemainingWaitMinutes();
        return {
          'canSend': false,
          'reason': '${remainingMinutes}분 후에 다시 시도해주세요.',
          'remainingMinutes': remainingMinutes,
          'remainingDailyCount': await getRemainingDailyCount(phoneNumber),
        };
      }
      
      if (!canSendDaily) {
        return {
          'canSend': false,
          'reason': '오늘 해당 전화번호로 최대 발송 횟수를 초과했습니다.',
          'remainingMinutes': 0,
          'remainingDailyCount': 0,
        };
      }
      
      return {
        'canSend': true,
        'reason': null,
        'remainingMinutes': 0,
        'remainingDailyCount': await getRemainingDailyCount(phoneNumber),
      };
    } catch (e) {
      LoggerUtils.logError('전체 제한 확인 오류', error: e, tag: _tag);
      return {
        'canSend': false,
        'reason': '제한 확인 중 오류가 발생했습니다.',
        'remainingMinutes': _smsIntervalMinutes,
        'remainingDailyCount': 0,
      };
    }
  }
  
  /// 오늘 날짜 문자열 반환 (YYYY-MM-DD 형식)
  static String _getTodayString() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }
  
  /// 디버그용: 모든 제한 초기화
  /// 
  /// 개발/테스트 목적으로만 사용해야 합니다.
  static Future<void> resetAllLimits() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 모든 관련 키 삭제
      final keys = prefs.getKeys();
      final keysToRemove = keys.where((key) => 
        key == _lastSmsTimeKey || 
        key.startsWith(_dailyCountPrefix) || 
        key.startsWith(_dailyDatePrefix)
      ).toList();
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
      
      LoggerUtils.logInfo('모든 SMS 제한 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('제한 초기화 오류', error: e, tag: _tag);
    }
  }
}
