import '../models/set_discount.dart';
import '../models/product.dart';
import '../models/set_discount_transaction.dart';
import '../utils/logger_utils.dart';

/// 세트 할인 적용 결과를 나타내는 클래스
class SetDiscountResult {
  final List<AppliedSetDiscount> appliedDiscounts;
  final int totalDiscountAmount;
  final Map<int, int> usedProductQuantities; // productId -> 사용된 수량

  const SetDiscountResult({
    required this.appliedDiscounts,
    required this.totalDiscountAmount,
    required this.usedProductQuantities,
  });

  SetDiscountResult.empty()
      : appliedDiscounts = [],
        totalDiscountAmount = 0,
        usedProductQuantities = {};
}



/// 세트 할인 비즈니스 로직을 처리하는 서비스 클래스
class SetDiscountService {
  /// 선택된 상품들에 대해 최적의 세트 할인을 계산합니다.
  ///
  /// [selectedProducts]: 선택된 상품 ID와 수량의 맵 (productId -> quantity)
  /// [availableSetDiscounts]: 사용 가능한 세트 할인 목록
  /// [allProducts]: 모든 상품 정보 (카테고리별 할인 계산용)
  /// [totalAmount]: 총 구매 금액 (최소 금액 할인 계산용)
  ///
  /// 반환값: 최적의 세트 할인 적용 결과
  static SetDiscountResult calculateOptimalDiscount(
    Map<int, int> selectedProducts,
    List<SetDiscount> availableSetDiscounts, {
    List<Product>? allProducts,
    int? totalAmount,
  }) {
    try {
      if (selectedProducts.isEmpty || availableSetDiscounts.isEmpty) {
        return SetDiscountResult.empty();
      }

      LoggerUtils.logDebug(
        'Calculating optimal discount for products: $selectedProducts',
        tag: 'SetDiscountService',
      );

      // 적용 가능한 세트 할인들을 필터링
      final applicableDiscounts = availableSetDiscounts
          .where((discount) => _canApplySetDiscount(
                selectedProducts,
                discount,
                allProducts: allProducts,
                totalAmount: totalAmount,
              ))
          .toList();

      if (applicableDiscounts.isEmpty) {
        LoggerUtils.logDebug('No applicable set discounts found', tag: 'SetDiscountService');
        return SetDiscountResult.empty();
      }

      // 할인 금액 기준으로 내림차순 정렬 (그리디 알고리즘)
      applicableDiscounts.sort((a, b) => b.discountAmount.compareTo(a.discountAmount));

      // 최적 조합 계산
      final result = _findOptimalCombination(selectedProducts, applicableDiscounts, allProducts ?? []);

      LoggerUtils.logInfo(
        'Optimal discount calculated: ${result.totalDiscountAmount}원, ${result.appliedDiscounts.length} sets applied',
        tag: 'SetDiscountService',
      );

      return result;
    } catch (e) {
      LoggerUtils.logError('Failed to calculate optimal discount', error: e, tag: 'SetDiscountService');
      return SetDiscountResult.empty();
    }
  }

  /// 세트 할인이 적용 가능한지 확인
  static bool _canApplySetDiscount(
    Map<int, int> selectedProducts,
    SetDiscount setDiscount, {
    List<Product>? allProducts,
    int? totalAmount,
  }) {
    switch (setDiscount.conditionType) {
      case SetDiscountConditionType.productCombination:
        return _canApplyProductCombination(selectedProducts, setDiscount);

      case SetDiscountConditionType.minimumAmount:
        return _canApplyMinimumAmount(totalAmount, setDiscount);

      case SetDiscountConditionType.categoryQuantity:
        return _canApplyCategoryQuantity(selectedProducts, setDiscount, allProducts);

      case SetDiscountConditionType.productGroupQuantity:
        return _canApplyProductGroupQuantity(selectedProducts, setDiscount);
    }
  }

  /// 상품 조합 할인 적용 가능 여부 확인
  static bool _canApplyProductCombination(Map<int, int> selectedProducts, SetDiscount setDiscount) {
    for (final productId in setDiscount.productIds) {
      if (!selectedProducts.containsKey(productId) || selectedProducts[productId]! <= 0) {
        return false;
      }
    }
    return true;
  }

  /// 최소 구매 금액 할인 적용 가능 여부 확인
  static bool _canApplyMinimumAmount(int? totalAmount, SetDiscount setDiscount) {
    if (totalAmount == null) return false;
    return totalAmount >= setDiscount.minimumAmount;
  }

  /// 카테고리별 수량 할인 적용 가능 여부 확인
  static bool _canApplyCategoryQuantity(
    Map<int, int> selectedProducts,
    SetDiscount setDiscount,
    List<Product>? allProducts,
  ) {
    if (setDiscount.categoryCondition == null || allProducts == null) return false;

    final categoryCondition = setDiscount.categoryCondition!;
    int categoryQuantity = 0;

    for (final entry in selectedProducts.entries) {
      final product = allProducts.firstWhere(
        (p) => p.id == entry.key,
        orElse: () => Product.create(
          name: '',
          quantity: 0,
          price: 0,
          eventId: 1,
          categoryId: 0,
        ),
      );

      if (product.categoryId == categoryCondition.categoryId) {
        categoryQuantity += entry.value;
      }
    }

    return categoryQuantity >= categoryCondition.minimumQuantity;
  }

  /// 상품군 수량 할인 적용 가능 여부 확인
  static bool _canApplyProductGroupQuantity(Map<int, int> selectedProducts, SetDiscount setDiscount) {
    if (setDiscount.productGroupCondition == null) return false;

    final productGroupCondition = setDiscount.productGroupCondition!;
    int groupQuantity = 0;

    for (final productId in productGroupCondition.productIds) {
      if (selectedProducts.containsKey(productId)) {
        groupQuantity += selectedProducts[productId]!;
      }
    }

    return groupQuantity >= productGroupCondition.minimumQuantity;
  }

  /// 최적의 세트 할인 조합을 찾습니다 (그리디 알고리즘)
  static SetDiscountResult _findOptimalCombination(
    Map<int, int> selectedProducts,
    List<SetDiscount> applicableDiscounts,
    List<Product> allProducts,
  ) {
    final appliedDiscounts = <AppliedSetDiscount>[];
    final remainingProducts = Map<int, int>.from(selectedProducts);
    int totalDiscountAmount = 0;

    // 각 세트 할인에 대해 최대한 많이 적용
    for (final setDiscount in applicableDiscounts) {
      int maxApplicableCount = 0;

      switch (setDiscount.conditionType) {
        case SetDiscountConditionType.productCombination:
          maxApplicableCount = _getMaxApplicableCount(remainingProducts, setDiscount);
          break;
        case SetDiscountConditionType.minimumAmount:
          // 최소 구매 금액 할인은 항상 한 번만 적용
          maxApplicableCount = 1;
          break;
        case SetDiscountConditionType.categoryQuantity:
          maxApplicableCount = _getMaxCategoryQuantityCount(selectedProducts, setDiscount, allProducts);
          break;
        case SetDiscountConditionType.productGroupQuantity:
          maxApplicableCount = _getMaxProductGroupQuantityCount(selectedProducts, setDiscount);
          break;
      }

      if (maxApplicableCount > 0) {
        // 세트 할인 적용
        appliedDiscounts.add(AppliedSetDiscount(
          setDiscount: setDiscount,
          appliedCount: maxApplicableCount,
        ));

        // 상품 조합 할인의 경우에만 사용된 상품 수량 차감
        if (setDiscount.conditionType == SetDiscountConditionType.productCombination) {
          for (final productId in setDiscount.productIds) {
            remainingProducts[productId] = remainingProducts[productId]! - maxApplicableCount;
          }
        }

        totalDiscountAmount += setDiscount.discountAmount * maxApplicableCount;

        LoggerUtils.logDebug(
          'Applied set discount: ${setDiscount.name} x$maxApplicableCount (${setDiscount.discountAmount * maxApplicableCount}원)',
          tag: 'SetDiscountService',
        );
      }
    }

    // 사용된 상품 수량 계산 (조건 타입별로 적절히 처리)
    final usedProductQuantities = <int, int>{};

    for (final applied in appliedDiscounts) {
      switch (applied.setDiscount.conditionType) {
        case SetDiscountConditionType.productCombination:
          // 상품 조합 할인: remainingProducts에서 차감된 수량 사용
          for (final productId in applied.setDiscount.productIds) {
            final used = selectedProducts[productId]! - (remainingProducts[productId] ?? 0);
            if (used > 0) {
              usedProductQuantities[productId] = (usedProductQuantities[productId] ?? 0) + used;
            }
          }
          break;
        case SetDiscountConditionType.minimumAmount:
          // 최소 구매 금액 할인: 모든 선택된 상품이 사용됨
          for (final entry in selectedProducts.entries) {
            usedProductQuantities[entry.key] = entry.value;
          }
          break;
        case SetDiscountConditionType.categoryQuantity:
          // 카테고리별 할인: 해당 카테고리의 상품들만 사용됨
          if (applied.setDiscount.categoryCondition != null) {
            final categoryId = applied.setDiscount.categoryCondition!.categoryId;
            for (final entry in selectedProducts.entries) {
              final product = allProducts.firstWhere(
                (p) => p.id == entry.key,
                orElse: () => Product.create(
                  name: '',
                  quantity: 0,
                  price: 0,
                  eventId: 1,
                  categoryId: 0,
                ),
              );
              if (product.categoryId == categoryId) {
                usedProductQuantities[entry.key] = entry.value;
              }
            }
          }
          break;
        case SetDiscountConditionType.productGroupQuantity:
          // 상품군 할인: 해당 상품군의 상품들만 사용됨
          if (applied.setDiscount.productGroupCondition != null) {
            for (final productId in applied.setDiscount.productGroupCondition!.productIds) {
              if (selectedProducts.containsKey(productId)) {
                usedProductQuantities[productId] = selectedProducts[productId]!;
              }
            }
          }
          break;
      }
    }

    return SetDiscountResult(
      appliedDiscounts: appliedDiscounts,
      totalDiscountAmount: totalDiscountAmount,
      usedProductQuantities: usedProductQuantities,
    );
  }

  /// 특정 세트 할인을 최대 몇 번 적용할 수 있는지 계산
  static int _getMaxApplicableCount(Map<int, int> remainingProducts, SetDiscount setDiscount) {
    int maxCount = 999999; // 충분히 큰 수

    for (final productId in setDiscount.productIds) {
      final availableQuantity = remainingProducts[productId] ?? 0;
      if (availableQuantity <= 0) {
        return 0; // 하나라도 없으면 적용 불가
      }
      maxCount = maxCount < availableQuantity ? maxCount : availableQuantity;
    }

    return maxCount == 999999 ? 0 : maxCount;
  }

  // formatDiscountSummary와 _formatCurrency 메서드 제거됨
  // 새로운 시스템에서는 UI에서 직접 포맷팅 처리

  /// 세트 할인이 적용된 상품들의 목록을 반환 (조건 타입별로 적절히 처리)
  static List<int> getDiscountedProductIds(SetDiscountResult result, {List<Product>? allProducts}) {
    final productIds = <int>{};

    for (final applied in result.appliedDiscounts) {
      switch (applied.setDiscount.conditionType) {
        case SetDiscountConditionType.productCombination:
          productIds.addAll(applied.setDiscount.productIds);
          break;
        case SetDiscountConditionType.minimumAmount:
          // 최소 구매 금액 할인은 모든 상품에 적용되므로 result.usedProductQuantities 사용
          productIds.addAll(result.usedProductQuantities.keys);
          break;
        case SetDiscountConditionType.categoryQuantity:
          // 카테고리별 할인의 경우 해당 카테고리의 상품들 추가
          if (applied.setDiscount.categoryCondition != null && allProducts != null) {
            final categoryId = applied.setDiscount.categoryCondition!.categoryId;
            final categoryProductIds = allProducts
                .where((p) => p.categoryId == categoryId)
                .map((p) => p.id!)
                .where((id) => result.usedProductQuantities.containsKey(id));
            productIds.addAll(categoryProductIds);
          }
          break;
        case SetDiscountConditionType.productGroupQuantity:
          // 상품군 할인의 경우 해당 상품군의 상품들 추가
          if (applied.setDiscount.productGroupCondition != null) {
            final groupProductIds = applied.setDiscount.productGroupCondition!.productIds
                .where((id) => result.usedProductQuantities.containsKey(id));
            productIds.addAll(groupProductIds);
          }
          break;
      }
    }

    return productIds.toList();
  }

  /// 특정 상품이 세트 할인에 포함되는지 확인
  static bool isProductInDiscount(int productId, SetDiscountResult result) {
    return result.usedProductQuantities.containsKey(productId) &&
           result.usedProductQuantities[productId]! > 0;
  }

  /// 세트 할인 검증 (조건 타입별로 적절한 검증 수행)
  static bool validateSetDiscount(SetDiscount setDiscount, List<int> availableProductIds) {
    switch (setDiscount.conditionType) {
      case SetDiscountConditionType.productCombination:
        // 상품 조합 할인: 상품 ID들이 유효한지 확인
        if (setDiscount.productIds.isEmpty) {
          return false;
        }

        // 중복 상품 ID 확인
        final uniqueIds = setDiscount.productIds.toSet();
        if (uniqueIds.length != setDiscount.productIds.length) {
          return false;
        }

        // 모든 상품 ID가 유효한지 확인
        for (final productId in setDiscount.productIds) {
          if (!availableProductIds.contains(productId)) {
            return false;
          }
        }
        return true;

      case SetDiscountConditionType.minimumAmount:
        // 최소 구매 금액 할인: 최소 금액이 유효한지 확인
        return setDiscount.minimumAmount > 0;

      case SetDiscountConditionType.categoryQuantity:
        // 카테고리별 수량 할인: 카테고리 조건이 유효한지 확인
        return setDiscount.categoryCondition != null &&
               setDiscount.categoryCondition!.categoryId > 0 &&
               setDiscount.categoryCondition!.minimumQuantity > 0;

      case SetDiscountConditionType.productGroupQuantity:
        // 상품군 수량 할인: 상품군 조건이 유효한지 확인
        if (setDiscount.productGroupCondition == null ||
            setDiscount.productGroupCondition!.productIds.isEmpty ||
            setDiscount.productGroupCondition!.minimumQuantity <= 0) {
          return false;
        }

        // 상품군의 모든 상품 ID가 유효한지 확인
        for (final productId in setDiscount.productGroupCondition!.productIds) {
          if (!availableProductIds.contains(productId)) {
            return false;
          }
        }
        return true;
    }
  }

  /// 카테고리별 수량 할인의 최대 적용 횟수 계산
  static int _getMaxCategoryQuantityCount(
    Map<int, int> selectedProducts,
    SetDiscount setDiscount,
    List<Product> allProducts,
  ) {
    if (setDiscount.categoryCondition == null || !setDiscount.allowMultipleApplications) {
      return 1; // 반복 적용이 비활성화되어 있으면 최대 1회
    }

    final categoryCondition = setDiscount.categoryCondition!;
    int categoryQuantity = 0;

    for (final entry in selectedProducts.entries) {
      final product = allProducts.firstWhere(
        (p) => p.id == entry.key,
        orElse: () => Product.create(
          name: '',
          quantity: 0,
          price: 0,
          eventId: 1,
          categoryId: 0,
        ),
      );

      if (product.categoryId == categoryCondition.categoryId) {
        categoryQuantity += entry.value;
      }
    }

    // 조건 개수로 나누어 최대 적용 횟수 계산
    return categoryQuantity ~/ categoryCondition.minimumQuantity;
  }

  /// 상품군 수량 할인의 최대 적용 횟수 계산
  static int _getMaxProductGroupQuantityCount(
    Map<int, int> selectedProducts,
    SetDiscount setDiscount,
  ) {
    if (setDiscount.productGroupCondition == null || !setDiscount.allowMultipleApplications) {
      return 1; // 반복 적용이 비활성화되어 있으면 최대 1회
    }

    final productGroupCondition = setDiscount.productGroupCondition!;
    int groupQuantity = 0;

    for (final productId in productGroupCondition.productIds) {
      if (selectedProducts.containsKey(productId)) {
        groupQuantity += selectedProducts[productId]!;
      }
    }

    // 조건 개수로 나누어 최대 적용 횟수 계산
    return groupQuantity ~/ productGroupCondition.minimumQuantity;
  }
}
