/// Firebase App Check 관련 유틸리티
/// 
/// 디버그 토큰 확인, App Check 상태 모니터링 등의 기능을 제공합니다.
library;

import 'dart:io';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'logger_utils.dart';

class AppCheckUtils {
  static const String _tag = 'AppCheckUtils';

  /// App Check 상태 확인 및 디버그 토큰 로깅 (단순화)
  static Future<void> checkAppCheckStatus() async {
    try {
      LoggerUtils.logInfo('App Check 상태 확인 (토큰 요청 없이)', tag: _tag);
      // Too many attempts 오류 방지를 위해 실제 토큰 요청은 하지 않음
      LoggerUtils.logInfo('App Check가 활성화되어 있습니다', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('App Check 상태 확인 실패', tag: _tag, error: e);
    }
  }

  /// 디버그 토큰을 콘솔에서 확인하는 방법 안내
  static void printDebugTokenInstructions() {
    LoggerUtils.logInfo('=== Firebase App Check 디버그 토큰 확인 방법 ===', tag: _tag);
    LoggerUtils.logInfo('1. 앱을 실행하고 Firebase와 연결을 시도합니다.', tag: _tag);
    LoggerUtils.logInfo('2. 로그에서 디버그 토큰을 확인합니다.', tag: _tag);
    LoggerUtils.logInfo('3. Firebase Console > App Check > Apps 탭에서 토큰을 등록합니다.', tag: _tag);
    
    if (Platform.isAndroid) {
      LoggerUtils.logInfo('Android용 디버그 토큰: 783023AA-6A8F-47D1-8D0A-F4F3CBE6C5BC', tag: _tag);
    } else if (Platform.isIOS) {
      LoggerUtils.logInfo('iOS용 디버그 토큰: 94180427-6269-4224-87D6-DC182E09EA70', tag: _tag);
    }
    LoggerUtils.logInfo('================================================', tag: _tag);
  }

  /// Firebase Console에서 App Check 상태 확인
  static Future<void> verifyAppCheckConfiguration() async {
    try {
      LoggerUtils.logInfo('App Check 설정 확인 중...', tag: _tag);
      
      // 토큰 자동 갱신 상태 확인
      await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
      LoggerUtils.logInfo('토큰 자동 갱신이 활성화되었습니다.', tag: _tag);
      
      // 플랫폼별 정보 출력
      final platform = Platform.isAndroid ? 'Android' : 
                      Platform.isIOS ? 'iOS' : 'Unknown';
      LoggerUtils.logInfo('현재 플랫폼: $platform', tag: _tag);
      
      // App Check 상태 확인
      await checkAppCheckStatus();
      
    } catch (e) {
      LoggerUtils.logError('App Check 설정 확인 실패', tag: _tag, error: e);
    }
  }

  /// App Check 토큰 리스너 등록
  static void setupTokenListener() {
    LoggerUtils.logInfo('App Check 토큰 리스너 설정 중...', tag: _tag);
    
    // 토큰 변경 감지 (현재 Firebase App Check Flutter 플러그인에서는 직접적인 리스너가 제한적)
    // 주기적으로 토큰 상태를 확인하는 방식으로 구현
    Future.delayed(const Duration(seconds: 5), () async {
      await checkAppCheckStatus();
    });
  }

  /// 프로덕션 환경에서 App Check 설정 가이드
  static void printProductionSetupGuide() {
    LoggerUtils.logInfo('=== 프로덕션 App Check 설정 가이드 ===', tag: _tag);
    LoggerUtils.logInfo('1. Firebase Console에서 App Attest (iOS) 또는 Play Integrity (Android) 활성화', tag: _tag);
    LoggerUtils.logInfo('2. main.dart에서 AndroidProvider.debug → AndroidProvider.playIntegrity', tag: _tag);
    LoggerUtils.logInfo('3. main.dart에서 AppleProvider.debug → AppleProvider.appAttest', tag: _tag);
    LoggerUtils.logInfo('4. 앱 서명 인증서가 Firebase Console에 등록되어 있는지 확인', tag: _tag);
    LoggerUtils.logInfo('=========================================', tag: _tag);
  }
}
