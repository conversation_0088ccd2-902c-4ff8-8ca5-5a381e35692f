import 'package:flutter/material.dart';

/// 통합 색상 시스템 - Material Design 3 Expressive 2025 기반
/// 
/// AppColors와 OnboardingColors를 통합하여 일관된 색상 시스템 제공
/// 기존 코드의 완전한 호환성을 보장하면서 중복을 제거
class AppColors {
  AppColors._();

  // ============================================================================
  // === 기존 원본 색상 (호환성 유지) ===
  // ============================================================================
  
  static const Color black = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);

  // 품절 관련 색상
  static const Color outOfStockBackground = Color(0xFFfe5b52);
  static const Color defaultItemBackground = Colors.transparent;
  static const Color outOfStockImageOverlay = Color(0x80FF0000);

  // 텍스트 색상
  static const Color textColorError = Color(0xFFFF0000);

  // 판매/선택 오버레이 색상
  static const Color saleItemImageOverlay = Color(0x80ADD8E6);
  static const Color selectedItemImageOverlay = Color(0x66000000);

  // 수령 완료 배경색
  static const Color receivedItemBackground = Color(0xFFE0E0E0);

  // 그룹 판매 테두리 색상
  static const Color groupedSaleBorderColor = Color(0xFFFFCC80);

  // 구분선 색상
  static const Color dividerColor = Color(0x1F000000);

  // 간편 보기 모드용 색상
  static const Color outOfStockBackgroundSimple = Color(0x4DCCCCCC);
  static const Color defaultItemBackgroundSimple = Colors.transparent;
  static const Color disabledTextColor = Color(0xFF888888);
  static const Color primaryTextColor = Color(0xFF000000);
  static const Color secondaryTextColor = Color(0xFF444444);

  // ============================================================================
  // === OnboardingColors 호환 색상 (통합) ===
  // ============================================================================

  /// 기존 OnboardingColors에서 사용되던 색상들
  static const Color onboardingPrimary = Color(0xFFF4A261);
  static const Color onboardingPrimaryLight = Color(0xFFF7B885);
  static const Color onboardingPrimaryDark = Color(0xFFE8956C);
  static const Color onboardingAccent = Color(0xFFFFB366);
  static const Color onboardingAccentLight = Color(0xFFFFC285);
  static const Color onboardingAccentDark = Color(0xFFE6A159);
  static const Color onboardingDarkBrown = neutral80;
  static const Color onboardingDarkBrownLight = Color(0xFFA0522D);

  /// OnboardingColors 텍스트 색상 매핑
  static const Color onboardingTextPrimary = onSurface;
  static const Color onboardingTextSecondary = onSurfaceVariant;
  static const Color onboardingTextTertiary = neutral60;
  static const Color onboardingTextOnPrimary = onPrimary;
  static const Color onboardingTextOnDark = neutral10;

  // ============================================================================
  // === 카테고리 색상 시스템 (CategoryColors에서 가져옴) ===
  // ============================================================================

  /// 카테고리용 색상들 - 기존 CategoryColors에서 가져옴
  static const Color categoryDefaultOrange = Color(0xFFFF8C42);
  static const Color categoryVibrantPink = Color(0xFFFF69B4);
  static const Color categoryElectricBlue = Color(0xFF1E90FF);
  static const Color categoryLimeGreen = Color(0xFF32CD32);
  static const Color categoryPurple = Color(0xFF9370DB);
  static const Color categoryTeal = Color(0xFF20B2AA);
  static const Color categoryRed = Color(0xFFDC143C);
  static const Color categoryGold = Color(0xFFFFD700);
  static const Color categoryNavy = Color(0xFF191970);
  static const Color categoryMaroon = Color(0xFF800000);

  /// 사용 가능한 모든 카테고리 색상 목록
  static const List<Color> categoryAvailableColors = [
    categoryDefaultOrange,
    categoryVibrantPink,
    categoryElectricBlue,
    categoryLimeGreen,
    categoryPurple,
    categoryTeal,
    categoryRed,
    categoryGold,
    categoryNavy,
    categoryMaroon,
  ];

  /// 기본 카테고리 색상 값 (정수)
  static const int categoryDefaultColorValue = 0xFFFF8C42; // categoryDefaultOrange

  // ============================================================================
  // === Material Design 3 기반 메인 색상 시스템 ===
  // ============================================================================

  // Primary Color Palette
  static const Color primarySeed = Color(0xFFE09A74); // 웜 테라코타 - Material Design 3 기본
  static const Color primaryLight = Color(0xFFE8B296);
  static const Color primaryDark = Color(0xFFD08052);
  static const Color onPrimary = Color(0xFFFFFFFF);

  // Secondary Color Palette
  static const Color secondary = Color(0xFFE8E2D5);
  static const Color secondaryLight = Color(0xFFEDE8DC);
  static const Color secondaryDark = Color(0xFFD4CFC2);
  static const Color onSecondary = Color(0xFF2C1810);

  // Surface Colors
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8F6F0);
  static const Color surfaceTint = Color(0xFFE09A74);
  static const Color onSurface = Color(0xFF2C1810);
  static const Color onSurfaceVariant = Color(0xFF5D4E42);

  // Background Colors
  static const Color background = Color(0xFFFAF7F0);
  static const Color backgroundLight = Color(0xFFFCFAF5);
  static const Color onBackground = Color(0xFF2C1810);

  // Accent Colors
  static const Color accent = Color(0xFFF2B57A);
  static const Color accentLight = Color(0xFFF5C896);
  static const Color accentDark = Color(0xFFE8A05E);

  // Status Colors
  static const Color success = Color(0xFF6B8E23);
  static const Color successLight = Color(0xFF8FBC8F);
  static const Color successDark = Color(0xFF556B2F);

  static const Color warning = Color(0xFFDAA520);
  static const Color warningLight = Color(0xFFF0E68C);
  static const Color warningDark = Color(0xFFB8860B);

  static const Color error = Color(0xFFCD5C5C);
  static const Color errorLight = Color(0xFFF08080);
  static const Color errorDark = Color(0xFFA0522D);

  static const Color info = Color(0xFF4682B4);
  static const Color infoLight = Color(0xFF87CEEB);
  static const Color infoDark = Color(0xFF2F4F4F);

  // Neutral Colors
  static const Color neutral0 = Color(0xFFFFFFFF);
  static const Color neutral10 = Color(0xFFFCFAF5);
  static const Color neutral20 = Color(0xFFF8F6F0);
  static const Color neutral30 = Color(0xFFEDE8DC);
  static const Color neutral40 = Color(0xFFE8E2D5);
  static const Color neutral50 = Color(0xFFD4CFC2);
  static const Color neutral60 = Color(0xFF8B7355);
  static const Color neutral70 = Color(0xFF5D4E42);
  static const Color neutral80 = Color(0xFF2C1810);
  static const Color neutral90 = Color(0xFF1A0F08);
  static const Color neutral100 = Color(0xFF000000);

  // Elevation and Shadow
  static const Color elevation1 = Color(0x0D8B4513);
  static const Color elevation2 = Color(0x1A8B4513);
  static const Color elevation3 = Color(0x268B4513);
  static const Color elevation4 = Color(0x338B4513);
  static const Color elevation5 = Color(0x408B4513);

  // Glass Effect Colors (2025 Trend)
  static const Color glassEffect = Color(0x1AFFFFFF);
  static const Color glassBackground = Color(0xF2FFFFFF);
  static const Color glassBorder = Color(0x33FFFFFF);

  // ============================================================================
  // === OnboardingColors 호환성 네임스페이스 ===
  // ============================================================================



  // ============================================================================
  // === 그라데이션 시스템 (OnboardingColors에서 가져옴) ===
  // ============================================================================

  /// 메인 그라데이션 - OnboardingColors 호환
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [onboardingPrimary, onboardingPrimaryDark],
  );

  /// 배경 그라데이션 - 크림 톤
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [backgroundLight, background],
  );

  /// 카드 그라데이션 - OnboardingColors 호환
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFDF8F4), // 아주 연한 피치 크림
      Color(0xFFFAF6F1), // 연한 따뜻한 베이지 크림
    ],
  );

  /// 액센트 그라데이션 - OnboardingColors 호환
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [onboardingAccent, onboardingAccentDark],
  );

  // ============================================================================
  // === 그림자 색상 ===
  // ============================================================================

  static const Color shadow = Color(0x1A8B4513);
  static const Color shadowLight = Color(0x0D8B4513);
  static const Color shadowDark = Color(0x268B4513);

  // ============================================================================
  // === 헬퍼 메서드 (OnboardingColors에서 가져옴) ===
  // ============================================================================

  /// 투명도 변형 색상들
  static Color get primaryOverlay => primarySeed.withValues(alpha: 0.1);
  static Color get darkBrownOverlay => neutral80.withValues(alpha: 0.1);
  static Color get secondaryOverlay => secondary.withValues(alpha: 0.5);
  static Color get accentOverlay => accent.withValues(alpha: 0.2);

  /// 밝기에 따른 텍스트 색상 반환
  static Color getTextColorForBackground(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? onSurface : neutral0;
  }

  /// 호버 효과를 위한 색상 변형
  static Color getHoverColor(Color baseColor) {
    return Color.lerp(baseColor, Colors.white, 0.1) ?? baseColor;
  }

  /// 눌림 효과를 위한 색상 변형
  static Color getPressedColor(Color baseColor) {
    return Color.lerp(baseColor, Colors.black, 0.1) ?? baseColor;
  }

  // ============================================================================
  // === 카테고리 색상 헬퍼 메서드 ===
  // ============================================================================

  /// 카테고리 색상 인덱스로 색상 반환
  static Color getCategoryColorByIndex(int index) {
    if (index < 0 || index >= categoryAvailableColors.length) {
      return categoryDefaultOrange;
    }
    return categoryAvailableColors[index];
  }

  /// 카테고리 색상을 인덱스로 변환
  static int getCategoryColorIndex(Color color) {
    final index = categoryAvailableColors.indexOf(color);
    return index >= 0 ? index : 0;
  }

  /// 카테고리 색상 유효성 검사
  static bool isValidCategoryColor(Color color) {
    return categoryAvailableColors.contains(color);
  }

  /// 카테고리 색상 값(int)을 Color로 변환
  static Color getCategoryColorFromValue(int colorValue) {
    return Color(colorValue);
  }

  /// Color를 카테고리 색상 값(int)으로 변환
  static int getCategoryColorValue(Color color) {
    return color.toARGB32();
  }

  /// 동적 색상 적용을 위한 컨텍스트 기반 색상 가져오기
  static ColorScheme getColorScheme(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? darkColorScheme : lightColorScheme;
  }

  // ============================================================================
  // === 색상 스키마 정의 ===
  // ============================================================================

  /// 라이트 테마 색상 스키마
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primarySeed,
    onPrimary: onPrimary,
    primaryContainer: primaryLight,
    onPrimaryContainer: Color(0xFF001D36),
    secondary: secondary,
    onSecondary: onSecondary,
    secondaryContainer: secondaryLight,
    onSecondaryContainer: Color(0xFF002020),
    tertiary: accent,
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: accentLight,
    onTertiaryContainer: Color(0xFF2E1500),
    error: error,
    onError: Color(0xFFFFFFFF),
    errorContainer: errorLight,
    onErrorContainer: Color(0xFF410E0B),
    surface: surface,
    onSurface: onSurface,
    surfaceContainerHighest: surfaceVariant,
    onSurfaceVariant: onSurfaceVariant,
    outline: neutral60,
    outlineVariant: neutral30,
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: neutral90,
    onInverseSurface: neutral10,
    inversePrimary: primaryLight,
    surfaceTint: surfaceTint,
  );

  /// 다크 테마 색상 스키마
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primaryLight,
    onPrimary: Color(0xFF001D36),
    primaryContainer: primaryDark,
    onPrimaryContainer: primaryLight,
    secondary: secondaryLight,
    onSecondary: Color(0xFF002020),
    secondaryContainer: secondaryDark,
    onSecondaryContainer: secondaryLight,
    tertiary: accentLight,
    onTertiary: Color(0xFF2E1500),
    tertiaryContainer: accentDark,
    onTertiaryContainer: accentLight,
    error: errorLight,
    onError: Color(0xFF410E0B),
    errorContainer: errorDark,
    onErrorContainer: errorLight,
    surface: Color(0xFF101418),
    onSurface: Color(0xFFE2E2E9),
    surfaceContainerHighest: Color(0xFF43474E),
    onSurfaceVariant: Color(0xFFC4C7C5),
    outline: neutral70,
    outlineVariant: neutral80,
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE2E2E9),
    onInverseSurface: Color(0xFF2E3036),
    inversePrimary: primarySeed,
    surfaceTint: primaryLight,
  );
}
