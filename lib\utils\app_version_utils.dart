import 'package:package_info_plus/package_info_plus.dart';
import 'package:logger/logger.dart';

/// 앱 버전 관련 유틸리티
class AppVersionUtils {
  static final Logger _logger = Logger();
  static const String _tag = 'AppVersionUtils';
  
  static PackageInfo? _packageInfo;
  
  /// 패키지 정보 초기화
  static Future<void> initialize() async {
    try {
      _packageInfo = await PackageInfo.fromPlatform();
      _logger.i('[$_tag] 앱 버전 정보 로드 완료: ${_packageInfo?.version} (${_packageInfo?.buildNumber})');
    } catch (e) {
      _logger.e('[$_tag] 앱 버전 정보 로드 실패: $e');
    }
  }
  
  /// 현재 앱 버전 반환
  static String get currentVersion {
    return _packageInfo?.version ?? '알 수 없음';
  }
  
  /// 현재 빌드 번호 반환
  static String get currentBuildNumber {
    return _packageInfo?.buildNumber ?? '알 수 없음';
  }
  
  /// 앱 이름 반환
  static String get appName {
    return _packageInfo?.appName ?? '바라 부스 매니저';
  }
  
  /// 패키지 이름 반환
  static String get packageName {
    return _packageInfo?.packageName ?? 'com.blue.parabara';
  }
  
  /// 전체 버전 정보 반환 (버전 + 빌드번호)
  static String get fullVersionInfo {
    return '$currentVersion+$currentBuildNumber';
  }
  
  /// 버전 비교 (현재 버전이 요구 버전보다 낮으면 true)
  static bool isVersionLowerThan(String requiredVersion) {
    try {
      final currentVersionParts = currentVersion.split('.').map(int.parse).toList();
      final requiredVersionParts = requiredVersion.split('.').map(int.parse).toList();
      
      // 버전 부분 수를 맞춤 (예: 1.0 vs 1.0.0)
      while (currentVersionParts.length < requiredVersionParts.length) {
        currentVersionParts.add(0);
      }
      while (requiredVersionParts.length < currentVersionParts.length) {
        requiredVersionParts.add(0);
      }
      
      for (int i = 0; i < currentVersionParts.length; i++) {
        if (currentVersionParts[i] < requiredVersionParts[i]) {
          return true;
        } else if (currentVersionParts[i] > requiredVersionParts[i]) {
          return false;
        }
      }
      
      return false; // 같은 버전
    } catch (e) {
      _logger.e('[$_tag] 버전 비교 실패: $e');
      return false; // 에러 시 업데이트 불필요로 처리
    }
  }
  
  /// 디버그용 버전 정보 출력
  static void printVersionInfo() {
    _logger.i('[$_tag] === 앱 버전 정보 ===');
    _logger.i('[$_tag] 앱 이름: $appName');
    _logger.i('[$_tag] 패키지 이름: $packageName');
    _logger.i('[$_tag] 버전: $currentVersion');
    _logger.i('[$_tag] 빌드 번호: $currentBuildNumber');
    _logger.i('[$_tag] 전체 버전: $fullVersionInfo');
  }
}
