import 'dart:async';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'logger_utils.dart';
import 'cancellation_token.dart';

/// 배치 처리 상태
@immutable
class BatchProcessingState<T> extends Equatable {
  const BatchProcessingState({
    this.items = const [],
    this.processedItems = 0,
    this.totalItems = 0,
    this.isProcessing = false,
    this.error,
    this.progress = 0.0,
    this.currentBatch = 0,
    this.totalBatches = 0,
    this.startTime,
    this.estimatedTimeRemaining,
    this.memoryUsage,
    this.cpuUsage,
    this.successfulItems = 0,
    this.failedItems = 0,
    this.retryCount = 0,
  });

  final List<T> items;
  final int processedItems;
  final int totalItems;
  final bool isProcessing;
  final String? error;
  final double progress;
  final int currentBatch;
  final int totalBatches;
  final DateTime? startTime;
  final Duration? estimatedTimeRemaining;
  final double? memoryUsage;
  final double? cpuUsage;
  final int successfulItems;
  final int failedItems;
  final int retryCount;

  BatchProcessingState<T> copyWith({
    List<T>? items,
    int? processedItems,
    int? totalItems,
    bool? isProcessing,
    String? error,
    double? progress,
    int? currentBatch,
    int? totalBatches,
    DateTime? startTime,
    Duration? estimatedTimeRemaining,
    double? memoryUsage,
    double? cpuUsage,
    int? successfulItems,
    int? failedItems,
    int? retryCount,
  }) {
    return BatchProcessingState<T>(
      items: items ?? this.items,
      processedItems: processedItems ?? this.processedItems,
      totalItems: totalItems ?? this.totalItems,
      isProcessing: isProcessing ?? this.isProcessing,
      error: error ?? this.error,
      progress: progress ?? this.progress,
      currentBatch: currentBatch ?? this.currentBatch,
      totalBatches: totalBatches ?? this.totalBatches,
      startTime: startTime ?? this.startTime,
      estimatedTimeRemaining:
          estimatedTimeRemaining ?? this.estimatedTimeRemaining,
      memoryUsage: memoryUsage ?? this.memoryUsage,
      cpuUsage: cpuUsage ?? this.cpuUsage,
      successfulItems: successfulItems ?? this.successfulItems,
      failedItems: failedItems ?? this.failedItems,
      retryCount: retryCount ?? this.retryCount,
    );
  }

  @override
  List<Object?> get props => [
    items,
    processedItems,
    totalItems,
    isProcessing,
    error,
    progress,
    currentBatch,
    totalBatches,
    startTime,
    estimatedTimeRemaining,
    memoryUsage,
    cpuUsage,
    successfulItems,
    failedItems,
    retryCount,
  ];
}

/// 배치 처리 설정
@immutable
class BatchProcessingConfig extends Equatable {
  const BatchProcessingConfig({
    this.batchSize = 100,
    this.concurrentBatches = 3,
    this.delayBetweenBatches = const Duration(milliseconds: 100),
    this.timeoutPerBatch = const Duration(seconds: 30),
    this.retryAttempts = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.maxMemoryUsage = 0.8, // 80% 메모리 사용률 제한
    this.enableParallelProcessing = true,
    this.enableMemoryOptimization = true,
    this.enableProgressTracking = true,
    this.enableErrorRecovery = true,
    this.chunkSize = 1000, // 청크 단위 처리 크기
  });

  final int batchSize;
  final int concurrentBatches;
  final Duration delayBetweenBatches;
  final Duration timeoutPerBatch;
  final int retryAttempts;
  final Duration retryDelay;
  final double maxMemoryUsage;
  final bool enableParallelProcessing;
  final bool enableMemoryOptimization;
  final bool enableProgressTracking;
  final bool enableErrorRecovery;
  final int chunkSize;

  @override
  List<Object?> get props => [
    batchSize,
    concurrentBatches,
    delayBetweenBatches,
    timeoutPerBatch,
    retryAttempts,
    retryDelay,
    maxMemoryUsage,
    enableParallelProcessing,
    enableMemoryOptimization,
    enableProgressTracking,
    enableErrorRecovery,
    chunkSize,
  ];
}

/// 배치 처리가 취소되었을 때 발생하는 예외
class BatchProcessingCancelledException implements Exception {
  @override
  String toString() => '배치 처리가 취소되었습니다.';
}

/// 배치 처리 결과
@immutable
class BatchResult<T> {
  final List<T> succeeded;
  final List<BatchError<T>> failed;
  final Duration processingTime;
  final double averageProcessingTimePerItem;
  final int totalRetries;

  const BatchResult({
    required this.succeeded,
    required this.failed,
    required this.processingTime,
    required this.averageProcessingTimePerItem,
    required this.totalRetries,
  });

  bool get hasErrors => failed.isNotEmpty;
  int get totalProcessed => succeeded.length + failed.length;
  double get successRate =>
      totalProcessed > 0 ? succeeded.length / totalProcessed : 0.0;
}

/// 배치 처리 에러
@immutable
class BatchError<T> {
  final T item;
  final Object error;
  final StackTrace stackTrace;
  final int retryCount;
  final DateTime timestamp;

  const BatchError({
    required this.item,
    required this.error,
    required this.stackTrace,
    required this.retryCount,
    required this.timestamp,
  });
}

/// 메모리 사용량 모니터링
class MemoryMonitor {
  static double? _lastMemoryUsage;

  static double? getCurrentMemoryUsage() {
    try {
      // 실제 메모리 사용량 측정 (플랫폼별 구현 필요)
      // 여기서는 간단한 추정치 반환
      return _lastMemoryUsage ?? 0.5;
    } catch (e) {
      LoggerUtils.logWarning('메모리 사용량 측정 실패: $e');
      return null;
    }
  }

  static void updateMemoryUsage(double usage) {
    _lastMemoryUsage = usage;
  }

  /// 메모리 모니터 정리
  static void shutdown() {
    _lastMemoryUsage = null;
  }
}

/// 대용량 데이터의 배치 처리를 위한 고급 유틸리티 클래스
class AdvancedBatchProcessor<T> {
  final List<T> items;
  final Future<void> Function(List<T> batch) processBatch;
  final BatchProcessingConfig config;
  final void Function(double progress)? onProgress;
  final void Function(T item, dynamic error)? onItemError;
  final void Function(BatchProcessingState<T> state)? onStateChange;

  AdvancedBatchProcessor({
    required this.items,
    required this.processBatch,
    this.config = const BatchProcessingConfig(),
    this.onProgress,
    this.onItemError,
    this.onStateChange,
  });

  /// 고급 배치 처리 실행
  Future<BatchResult<T>> process() async {
    return await _processWithAdvancedFeatures(null);
  }

  /// 취소 가능한 고급 배치 처리 실행
  Future<BatchResult<T>> processWithCancellation(
    CancellationToken token,
  ) async {
    return await _processWithAdvancedFeatures(token);
  }

  /// 고급 기능을 포함한 배치 처리 로직
  Future<BatchResult<T>> _processWithAdvancedFeatures(
    CancellationToken? token,
  ) async {
    final startTime = DateTime.now();
    final totalItems = items.length;
    var processedItems = 0;
    var successfulItems = 0;
    var failedItems = 0;
    var totalRetries = 0;

    final List<T> succeeded = [];
    final List<BatchError<T>> failed = [];

    // 상태 초기화
    _updateState(
      BatchProcessingState<T>(
        items: items,
        totalItems: totalItems,
        isProcessing: true,
        startTime: startTime,
        progress: 0.0,
      ),
    );

    try {
      // 청크 단위로 분할하여 메모리 최적화
      final chunks = _createChunks(items, config.chunkSize);

      for (var chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        if (token?.isCancelled == true) {
          throw BatchProcessingCancelledException();
        }

        final chunk = chunks[chunkIndex];

        // 메모리 사용량 확인
        if (config.enableMemoryOptimization) {
          await _checkMemoryUsage();
        }

        // 병렬 처리 또는 순차 처리
        if (config.enableParallelProcessing && config.concurrentBatches > 1) {
          await _processChunkParallel(
            chunk,
            token,
            succeeded,
            failed,
            processedItems,
            successfulItems,
            failedItems,
            totalRetries,
          );
        } else {
          await _processChunkSequential(
            chunk,
            token,
            succeeded,
            failed,
            processedItems,
            successfulItems,
            failedItems,
            totalRetries,
          );
        }

        // 진행률 업데이트
        processedItems += chunk.length;
        final progress = processedItems / totalItems;
        onProgress?.call(progress);

        _updateState(
          BatchProcessingState<T>(
            items: items,
            processedItems: processedItems,
            totalItems: totalItems,
            isProcessing: true,
            progress: progress,
            currentBatch: chunkIndex + 1,
            totalBatches: chunks.length,
            startTime: startTime,
            estimatedTimeRemaining: _calculateEstimatedTime(
              startTime,
              progress,
            ),
            successfulItems: successfulItems,
            failedItems: failedItems,
            retryCount: totalRetries,
          ),
        );

        // 배치 간 지연
        if (chunkIndex < chunks.length - 1) {
          await Future.delayed(config.delayBetweenBatches);
        }
      }
    } catch (e) {
      LoggerUtils.logError('배치 처리 중 오류 발생', error: e);
      _updateState(
        BatchProcessingState<T>(
          items: items,
          processedItems: processedItems,
          totalItems: totalItems,
          isProcessing: false,
          error: e.toString(),
          progress: processedItems / totalItems,
        ),
      );
      rethrow;
    }

    final processingTime = DateTime.now().difference(startTime);
    final averageTimePerItem = totalItems > 0
        ? processingTime.inMilliseconds / totalItems
        : 0.0;

    _updateState(
      BatchProcessingState<T>(
        items: items,
        processedItems: totalItems,
        totalItems: totalItems,
        isProcessing: false,
        progress: 1.0,
        startTime: startTime,
        successfulItems: successfulItems,
        failedItems: failedItems,
        retryCount: totalRetries,
      ),
    );

    return BatchResult<T>(
      succeeded: succeeded,
      failed: failed,
      processingTime: processingTime,
      averageProcessingTimePerItem: averageTimePerItem,
      totalRetries: totalRetries,
    );
  }

  /// 청크 생성
  List<List<T>> _createChunks(List<T> items, int chunkSize) {
    final chunks = <List<T>>[];
    for (var i = 0; i < items.length; i += chunkSize) {
      final end = (i + chunkSize < items.length) ? i + chunkSize : items.length;
      chunks.add(items.sublist(i, end));
    }
    return chunks;
  }

  /// 병렬 청크 처리
  Future<void> _processChunkParallel(
    List<T> chunk,
    CancellationToken? token,
    List<T> succeeded,
    List<BatchError<T>> failed,
    int processedItems,
    int successfulItems,
    int failedItems,
    int totalRetries,
  ) async {
    final batches = _createChunks(chunk, config.batchSize);
    final futures = <Future<void>>[];

    for (var i = 0; i < batches.length; i += config.concurrentBatches) {
      final batchGroup = batches.skip(i).take(config.concurrentBatches);

      for (final batch in batchGroup) {
        futures.add(_processBatchWithRetry(batch, token, succeeded, failed));
      }

      // 동시 실행 수 제한
      await Future.wait(futures);
      futures.clear();
    }
  }

  /// 순차 청크 처리
  Future<void> _processChunkSequential(
    List<T> chunk,
    CancellationToken? token,
    List<T> succeeded,
    List<BatchError<T>> failed,
    int processedItems,
    int successfulItems,
    int failedItems,
    int totalRetries,
  ) async {
    final batches = _createChunks(chunk, config.batchSize);

    for (final batch in batches) {
      await _processBatchWithRetry(batch, token, succeeded, failed);
    }
  }

  /// 재시도 로직을 포함한 배치 처리
  Future<void> _processBatchWithRetry(
    List<T> batch,
    CancellationToken? token,
    List<T> succeeded,
    List<BatchError<T>> failed,
  ) async {
    var retryCount = 0;

    while (retryCount <= config.retryAttempts) {
      try {
        if (token?.isCancelled == true) {
          throw BatchProcessingCancelledException();
        }

        await processBatch(batch).timeout(
          config.timeoutPerBatch,
          onTimeout: () {
            throw BatchProcessingTimeoutException(
              'Batch processing timed out after ${config.timeoutPerBatch.inSeconds} seconds',
            );
          },
        );

        // 성공 시 아이템들을 성공 목록에 추가
        succeeded.addAll(batch);
        return;
      } catch (e) {
        retryCount++;

        if (retryCount > config.retryAttempts) {
          // 최종 실패 시 각 아이템을 실패 목록에 추가
          for (final item in batch) {
            final error = BatchError<T>(
              item: item,
              error: e,
              stackTrace: StackTrace.current,
              retryCount: retryCount - 1,
              timestamp: DateTime.now(),
            );
            failed.add(error);
            onItemError?.call(item, e);
          }
          return;
        }

        // 재시도 대기
        await Future.delayed(config.retryDelay * retryCount);
      }
    }
  }

  /// 메모리 사용량 확인
  Future<void> _checkMemoryUsage() async {
    final memoryUsage = MemoryMonitor.getCurrentMemoryUsage();
    if (memoryUsage != null && memoryUsage > config.maxMemoryUsage) {
      LoggerUtils.logWarning(
        '메모리 사용량이 높습니다: ${(memoryUsage * 100).toStringAsFixed(1)}%',
      );
      // 가비지 컬렉션 요청
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// 예상 완료 시간 계산
  Duration? _calculateEstimatedTime(DateTime startTime, double progress) {
    if (progress <= 0) return null;

    final elapsed = DateTime.now().difference(startTime);
    final estimatedTotal = elapsed.inMilliseconds / progress;
    final remaining = estimatedTotal - elapsed.inMilliseconds;

    return remaining > 0 ? Duration(milliseconds: remaining.round()) : null;
  }

  /// 상태 업데이트
  void _updateState(BatchProcessingState<T> state) {
    onStateChange?.call(state);
  }
}

/// 대용량 데이터의 배치 처리를 위한 유틸리티 클래스 (기존 호환성 유지)
/// 대량 데이터/작업을 효율적으로 처리하기 위한 배치 프로세서 유틸리티 클래스입니다.
/// - 비동기 배치 처리, 타임아웃/재시도/취소/오류 처리 등 지원
/// - 성능 최적화, 메모리 관리, 대량 작업 안정성 확보 목적
class BatchProcessor<T> {
  final List<T> items;
  final Future<void> Function(List<T> batch) processBatch;
  final int batchSize;
  final Duration timeout;
  final Duration retryDelay;
  final int maxRetries;
  final void Function(double progress)? onProgress;
  final void Function(T item, dynamic error)? onItemError;

  BatchProcessor({
    required this.items,
    required this.processBatch,
    this.batchSize = 100,
    this.timeout = const Duration(seconds: 30),
    this.retryDelay = const Duration(seconds: 1),
    this.maxRetries = 3,
    this.onProgress,
    this.onItemError,
  });

  /// 배치 처리 실행
  Future<void> process() async {
    await _processBatches(null);
  }

  /// 취소 가능한 배치 처리 실행
  Future<void> processWithCancellation(CancellationToken token) async {
    await _processBatches(token);
  }

  /// 내부 배치 처리 로직
  Future<void> _processBatches(CancellationToken? token) async {
    final totalItems = items.length;
    var processedItems = 0;

    // 배치 단위로 처리
    for (var i = 0; i < items.length; i += batchSize) {
      // 취소 여부 확인
      if (token?.isCancelled == true) {
        throw BatchProcessingCancelledException();
      }

      // 현재 배치 추출
      final end = (i + batchSize < items.length) ? i + batchSize : items.length;
      final batch = items.sublist(i, end);

      // 재시도 로직
      var retryCount = 0;
      while (true) {
        try {
          // 타임아웃 처리
          await processBatch(batch).timeout(
            timeout,
            onTimeout: () {
              throw BatchProcessingTimeoutException(
                'Batch processing timed out after ${timeout.inSeconds} seconds',
              );
            },
          );
          break; // 성공하면 while 루프 종료
        } catch (e) {
          // 타임아웃 예외는 재시도하지 않고 바로 전파
          if (e is BatchProcessingTimeoutException) {
            rethrow;
          }

          // 마지막 재시도까지 실패한 경우
          if (retryCount >= maxRetries) {
            // 각 아이템별 오류 처리
            if (onItemError != null) {
              for (final item in batch) {
                onItemError!(item, e);
              }
            }
            throw BatchProcessingFailedException(
              'Failed to process batch after $maxRetries retries: $e',
            );
          }

          // 재시도 대기
          await Future.delayed(retryDelay);
          retryCount++;
        }
      }

      // 진행률 업데이트
      processedItems += batch.length;
      final progress = processedItems / totalItems;
      onProgress?.call(progress);
    }
  }

  /// 정적 리소스 정리 (앱 종료 시 호출)
  static void shutdown() {
    MemoryMonitor.shutdown();
    LoggerUtils.logInfo('BatchProcessor 정리 완료');
  }
}

/// 배치 처리 관련 예외 클래스들
class BatchProcessingException implements Exception {
  final String message;
  BatchProcessingException(this.message);
  @override
  String toString() => 'BatchProcessingException: $message';
}

class BatchProcessingTimeoutException extends BatchProcessingException {
  BatchProcessingTimeoutException(super.message);
}

class BatchProcessingFailedException extends BatchProcessingException {
  BatchProcessingFailedException(super.message);
}
