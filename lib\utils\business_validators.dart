import 'validation_result.dart';
import 'validators.dart';

/// 비즈니스 로직 검증을 지원하는 유틸리티 클래스입니다.
/// - 상품, 판매, 선불결제, 판매자 등 도메인별 비즈니스 규칙 검증
/// - 복잡한 비즈니스 로직과 규칙들을 포함
class BusinessValidators {
  BusinessValidators._();

  // ============ 비즈니스 로직 검증 메서드들 ============

  /// 비즈니스 로직 검증: 상품 재고와 판매 수량 비교
  static ValidationResult validateSaleQuantity({
    required int? saleQuantity,
    required int? availableStock,
    String? productName,
  }) {
    final errors = <String, String>{};

    if (saleQuantity == null || saleQuantity <= 0) {
      errors['saleQuantity'] = '판매 수량을 입력해주세요.';
    }

    if (availableStock == null || availableStock < 0) {
      errors['availableStock'] = '재고 정보가 올바르지 않습니다.';
    }

    if (saleQuantity != null &&
        availableStock != null &&
        saleQuantity > availableStock) {
      errors['saleQuantity'] = '판매 수량이 재고($availableStock개)를 초과했습니다.';
    }

    // 비즈니스 규칙: 대량 판매 제한
    if (saleQuantity != null && saleQuantity > 100) {
      errors['saleQuantity'] = '한 번에 100개를 초과하여 판매할 수 없습니다.';
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// 비즈니스 로직 검증: 선불 결제 금액과 상품 가격 비교
  static ValidationResult validatePrepaymentAmount({
    required int? prepaymentAmount,
    required List<int>? productPrices,
    double discountRate = 0.0,
  }) {
    final errors = <String, String>{};

    if (prepaymentAmount == null || prepaymentAmount <= 0) {
      errors['prepaymentAmount'] = '선불 결제 금액을 입력해주세요.';
    }

    if (productPrices == null || productPrices.isEmpty) {
      errors['productPrices'] = '상품을 선택해주세요.';
    }

    if (prepaymentAmount != null &&
        productPrices != null &&
        productPrices.isNotEmpty) {
      final totalPrice = productPrices.reduce((a, b) => a + b);
      final discountedPrice = (totalPrice * (1 - discountRate)).round();

      if (prepaymentAmount < discountedPrice) {
        errors['prepaymentAmount'] =
            '결제 금액이 상품 총액($discountedPrice원)보다 적습니다.';
      }

      // 비즈니스 규칙: 과도한 선불 결제 방지
      if (prepaymentAmount > discountedPrice * 2) {
        errors['prepaymentAmount'] = '결제 금액이 상품 총액의 2배를 초과할 수 없습니다.';
      }
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// 상품 데이터 전체 검증 - 강화됨
  static ValidationResult validateProduct({
    required String? name,
    required int? price,
    required int? quantity,
    String? sellerName,
    double? focusX,
    double? focusY,
    String? description,
    List<String>? tags,
  }) {
    final validations = <String, dynamic>{
      'name': !SecurityValidators.containsMaliciousInput(name) && InputValidators.isValidProductName(name)
          ? true
          : '상품명을 올바르게 입력해주세요 (최대 100자, 특수문자 제한)',
      'price': InputValidators.isValidPrice(price) ? true : '올바른 가격을 입력해주세요',
      'quantity': InputValidators.isValidQuantity(quantity) ? true : '올바른 수량을 입력해주세요',
      'sellerName':
          (sellerName == null ||
              (!SecurityValidators.containsMaliciousInput(sellerName) &&
                  InputValidators.isValidSellerName(sellerName)))
          ? true
          : '판매자명을 올바르게 입력해주세요 (최대 50자)',
      'focusX': (focusX == null || (focusX >= 0.0 && focusX <= 1.0))
          ? true
          : '포커스 X 좌표는 0.0~1.0 사이의 값이어야 합니다',
      'focusY': (focusY == null || (focusY >= 0.0 && focusY <= 1.0))
          ? true
          : '포커스 Y 좌표는 0.0~1.0 사이의 값이어야 합니다',
      'description':
          (description == null ||
              (!SecurityValidators.containsMaliciousInput(description) &&
                  InputValidators.isValidDescription(description)))
          ? true
          : '상품 설명을 올바르게 입력해주세요 (최대 1000자)',
    };

    // 비즈니스 로직 검증 추가
    if (price != null && price > 0 && quantity != null && quantity > 0) {
      final totalValue = price * quantity;
      if (totalValue > 1000000000) {
        // 10억원
        validations['totalValue'] = '상품의 총 가치가 너무 큽니다.';
      }
    }

    // 태그 검증
    if (tags != null && tags.isNotEmpty) {
      if (tags.length > 10) {
        validations['tags'] = '태그는 최대 10개까지 입력 가능합니다.';
      }

      for (final tag in tags) {
        if (SecurityValidators.containsMaliciousInput(tag) || tag.length > 20) {
          validations['tags'] = '태그를 올바르게 입력해주세요 (최대 20자, 특수문자 제한)';
          break;
        }
      }
    }

    return _validateAll(validations);
  }

  /// 선불결제 데이터 전체 검증 - 강화됨
  static ValidationResult validatePrepayment({
    required String? buyerName,
    String? buyerContact, // 선택사항으로 변경
    required int? amount,
    required String? productNameList,
    String? bankName,
    String? accountNumber,
    String? email,
    String? twitter,
    DateTime? deliveryDate,
    List<int>? productPrices,
  }) {
    final validations = <String, dynamic>{
      'buyerName':
          !SecurityValidators.containsMaliciousInput(buyerName) && InputValidators.isValidBuyerName(buyerName)
          ? true
          : '구매자명을 올바르게 입력해주세요 (최대 50자)',
      // buyerContact는 선택사항이므로 검증에서 제외
      'amount': InputValidators.isValidPrice(amount) ? true : '올바른 금액을 입력해주세요',
      'productNameList':
          !SecurityValidators.containsMaliciousInput(productNameList) &&
              InputValidators.isValidProductList(productNameList)
          ? true
          : '상품 목록을 올바르게 입력해주세요 (최대 500자)',
      'bankName':
          (bankName == null ||
              (!SecurityValidators.containsMaliciousInput(bankName) && InputValidators.isValidBankName(bankName)))
          ? true
          : '은행명을 올바르게 입력해주세요 (최대 30자)',
      'accountNumber':
          (accountNumber == null ||
              (!SecurityValidators.containsMaliciousInput(accountNumber) &&
                  InputValidators.isValidAccountNumber(accountNumber)))
          ? true
          : '올바른 계좌번호를 입력해주세요',
      'email':
          (email == null ||
              (!SecurityValidators.containsMaliciousInput(email) && InputValidators.isValidEmail(email)))
          ? true
          : '올바른 이메일 주소를 입력해주세요',
      'twitter':
          !SecurityValidators.containsMaliciousInput(twitter) && InputValidators.isValidTwitterAccount(twitter)
          ? true
          : '올바른 트위터 계정을 입력해주세요',
    };

    // 비즈니스 로직 검증 추가
    if (deliveryDate != null) {
      final now = DateTime.now();
      if (deliveryDate.isBefore(now)) {
        validations['deliveryDate'] = '배송일은 현재 시간 이후여야 합니다.';
      }

      final maxDeliveryDate = now.add(const Duration(days: 365));
      if (deliveryDate.isAfter(maxDeliveryDate)) {
        validations['deliveryDate'] = '배송일은 1년 이내로 설정해주세요.';
      }
    }

    // 선불 결제 금액 검증
    if (amount != null && productPrices != null && productPrices.isNotEmpty) {
      final amountValidation = validatePrepaymentAmount(
        prepaymentAmount: amount,
        productPrices: productPrices,
      );

      if (!amountValidation.isValid) {
        validations.addAll(amountValidation.errors);
      }
    }

    return _validateAll(validations);
  }

  /// 판매 데이터 전체 검증 (새로 추가)
  static ValidationResult validateSale({
    required String? productName,
    required int? quantity,
    required int? price,
    required String? buyerName,
    String? buyerContact,
    int? availableStock,
    DateTime? saleDate,
    String? notes,
  }) {
    final validations = <String, dynamic>{
      'productName':
          !SecurityValidators.containsMaliciousInput(productName) &&
              InputValidators.isValidProductName(productName)
          ? true
          : '상품명을 올바르게 입력해주세요',
      'quantity': InputValidators.isValidQuantity(quantity) ? true : '올바른 수량을 입력해주세요',
      'price': InputValidators.isValidPrice(price) ? true : '올바른 가격을 입력해주세요',
      'buyerName':
          !SecurityValidators.containsMaliciousInput(buyerName) && InputValidators.isValidBuyerName(buyerName)
          ? true
          : '구매자명을 올바르게 입력해주세요',
      'buyerContact': (buyerContact == null || InputValidators.isValidContact(buyerContact))
          ? true
          : '올바른 연락처를 입력해주세요',
      'notes':
          (notes == null ||
              (!SecurityValidators.containsMaliciousInput(notes) && InputValidators.isValidDescription(notes)))
          ? true
          : '메모를 올바르게 입력해주세요 (최대 1000자)',
    };

    // 재고 검증
    if (quantity != null && availableStock != null) {
      final stockValidation = validateSaleQuantity(
        saleQuantity: quantity,
        availableStock: availableStock,
        productName: productName,
      );

      if (!stockValidation.isValid) {
        validations.addAll(stockValidation.errors);
      }
    }

    // 판매일 검증
    if (saleDate != null) {
      final now = DateTime.now();
      final oneYearAgo = now.subtract(const Duration(days: 365));

      if (saleDate.isAfter(now)) {
        validations['saleDate'] = '판매일은 미래 날짜일 수 없습니다.';
      }

      if (saleDate.isBefore(oneYearAgo)) {
        validations['saleDate'] = '판매일은 1년 이내의 날짜여야 합니다.';
      }
    }

    return _validateAll(validations);
  }

  /// 판매자 데이터 전체 검증 (새로 추가)
  static ValidationResult validateSeller({
    required String? name,
    required String? contact,
    String? email,
    String? businessNumber,
    String? bankName,
    String? accountNumber,
    String? address,
  }) {
    final validations = <String, dynamic>{
      'name': !SecurityValidators.containsMaliciousInput(name) && InputValidators.isValidSellerName(name)
          ? true
          : '판매자명을 올바르게 입력해주세요',
      'contact': InputValidators.isValidContact(contact) ? true : '올바른 연락처를 입력해주세요',
      'email':
          (email == null ||
              (!SecurityValidators.containsMaliciousInput(email) && InputValidators.isValidEmail(email)))
          ? true
          : '올바른 이메일 주소를 입력해주세요',
      'businessNumber':
          (businessNumber == null ||
              (!SecurityValidators.containsMaliciousInput(businessNumber) &&
                  InputValidators.isValidBusinessNumber(businessNumber)))
          ? true
          : '올바른 사업자 등록번호를 입력해주세요 (000-00-00000)',
      'bankName':
          (bankName == null ||
              (!SecurityValidators.containsMaliciousInput(bankName) && InputValidators.isValidBankName(bankName)))
          ? true
          : '은행명을 올바르게 입력해주세요',
      'accountNumber':
          (accountNumber == null ||
              (!SecurityValidators.containsMaliciousInput(accountNumber) &&
                  InputValidators.isValidAccountNumber(accountNumber)))
          ? true
          : '올바른 계좌번호를 입력해주세요',
      'address':
          (address == null ||
              (!SecurityValidators.containsMaliciousInput(address) && InputValidators.hasMaxLength(address, 200)))
          ? true
          : '주소를 올바르게 입력해주세요 (최대 200자)',
    };

    return _validateAll(validations);
  }

  // ============ 내부 헬퍼 메서드들 ============

  /// 일괄 검증 결과를 담는 클래스
  static ValidationResult _validateAll(Map<String, dynamic> validations) {
    final errors = <String, String>{};

    validations.forEach((field, validation) {
      if (validation is bool && !validation) {
        errors[field] = '유효하지 않은 값입니다.';
      } else if (validation is String) {
        errors[field] = validation;
      }
    });

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }
} 
