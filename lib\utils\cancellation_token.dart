import 'dart:async';
import 'logger_utils.dart';

/// 취소 이유
enum CancellationReason {
  userRequested, // 사용자 요청
  timeout, // 타임아웃
  error, // 오류 발생
  memoryPressure, // 메모리 부족
  systemShutdown, // 시스템 종료
  parentCancelled, // 부모 토큰 취소
  unknown, // 알 수 없음
}

/// 취소 정보
class CancellationInfo {
  final CancellationReason reason;
  final String? message;
  final DateTime timestamp;
  final StackTrace? stackTrace;

  const CancellationInfo({
    required this.reason,
    this.message,
    required this.timestamp,
    this.stackTrace,
  });

  @override
  String toString() =>
      'CancellationInfo(reason: $reason, message: $message, timestamp: $timestamp)';
}

/// 취소 이벤트 리스너
typedef CancellationListener = void Function(CancellationInfo info);

/// 비동기 작업의 취소를 관리하는 고급 토큰 클래스
class CancellationToken {
  bool _isCancelled = false;
  CancellationInfo? _cancellationInfo;
  final _completer = Completer<void>();
  final List<CancellationListener> _listeners = [];
  final List<CancellationToken> _linkedTokens = [];
  Timer? _timeoutTimer;

  /// 토큰이 취소되었는지 여부
  bool get isCancelled => _isCancelled;

  /// 취소 정보
  CancellationInfo? get cancellationInfo => _cancellationInfo;

  /// 토큰이 취소될 때 완료되는 Future
  Future<void> get cancelled => _completer.future;

  /// 기본 생성자
  CancellationToken();

  /// 타임아웃이 설정된 토큰 생성
  factory CancellationToken.withTimeout(Duration timeout) {
    final token = CancellationToken();
    token.setTimeout(timeout);
    return token;
  }

  /// 부모 토큰과 연결된 토큰 생성
  factory CancellationToken.linkedTo(CancellationToken parent) {
    final token = CancellationToken();
    token.linkTo(parent);
    return token;
  }

  /// 타임아웃 설정
  void setTimeout(Duration timeout) {
    if (_isCancelled) return;

    _timeoutTimer?.cancel();
    _timeoutTimer = Timer(timeout, () {
      cancel(
        reason: CancellationReason.timeout,
        message: '타임아웃: ${timeout.inSeconds}초 초과',
      );
    });
  }

  /// 취소 이벤트 리스너 추가
  void addListener(CancellationListener listener) {
    if (_isCancelled) {
      // 이미 취소된 경우 즉시 호출
      listener(_cancellationInfo!);
    } else {
      _listeners.add(listener);
    }
  }

  /// 취소 이벤트 리스너 제거
  void removeListener(CancellationListener listener) {
    _listeners.remove(listener);
  }

  /// 다른 토큰과 연결 (연쇄 취소)
  void linkTo(CancellationToken other) {
    if (_isCancelled || other._isCancelled) return;

    _linkedTokens.add(other);
    other.addListener((info) {
      cancel(
        reason: CancellationReason.parentCancelled,
        message: '연결된 토큰이 취소됨: ${info.message}',
      );
    });
  }

  /// 연결된 토큰 해제
  void unlinkFrom(CancellationToken other) {
    _linkedTokens.remove(other);
    other.removeListener((info) {
      cancel(
        reason: CancellationReason.parentCancelled,
        message: '연결된 토큰이 취소됨: ${info.message}',
      );
    });
  }

  /// 토큰을 취소하고 관련된 모든 작업에 취소 신호를 보냄
  void cancel({
    CancellationReason reason = CancellationReason.userRequested,
    String? message,
    StackTrace? stackTrace,
  }) {
    if (_isCancelled) return;

    _isCancelled = true;
    _cancellationInfo = CancellationInfo(
      reason: reason,
      message: message,
      timestamp: DateTime.now(),
      stackTrace: stackTrace ?? StackTrace.current,
    );

    // 타임아웃 타이머 취소
    _timeoutTimer?.cancel();

    // 리스너들에게 알림
    for (final listener in _listeners) {
      try {
        listener(_cancellationInfo!);
      } catch (e) {
        // 리스너 오류는 무시
        LoggerUtils.logError('CancellationListener 오류: $e', tag: 'CancellationToken', error: e);
      }
    }

    // 연결된 토큰들도 취소
    for (final linkedToken in _linkedTokens) {
      linkedToken.cancel(
        reason: CancellationReason.parentCancelled,
        message: '연결된 토큰이 취소됨: $message',
      );
    }

    // Completer 완료
    if (!_completer.isCompleted) {
      _completer.complete();
    }

    LoggerUtils.logInfo('CancellationToken cancelled: $reason', tag: 'CancellationToken');
  }

  /// 토큰이 취소되었는지 확인하고, 취소된 경우 예외를 발생시킴
  void throwIfCancelled() {
    if (_isCancelled) {
      throw CancelledException(
        _cancellationInfo?.message ?? '작업이 취소되었습니다.',
        _cancellationInfo,
      );
    }
  }

  /// 주어진 Future가 완료되거나 토큰이 취소될 때까지 대기
  Future<T> whenCancelledOr<T>(Future<T> future) {
    return Future.any([
      future,
      cancelled.then(
        (_) => throw CancelledException(
          _cancellationInfo?.message ?? '작업이 취소되었습니다.',
          _cancellationInfo,
        ),
      ),
    ]);
  }

  /// 주어진 Future를 취소 가능하게 실행
  Future<T> runCancellable<T>(Future<T> Function() operation) async {
    return whenCancelledOr(operation());
  }

  /// 주어진 작업을 주기적으로 취소 확인하며 실행
  Future<T> runWithPeriodicCheck<T>(
    Future<T> Function() operation, {
    Duration checkInterval = const Duration(milliseconds: 100),
  }) async {
    return whenCancelledOr(_runWithPeriodicCheck(operation, checkInterval));
  }

  Future<T> _runWithPeriodicCheck<T>(
    Future<T> Function() operation,
    Duration checkInterval,
  ) async {
    final completer = Completer<T>();

    // 주기적 취소 확인
    Timer.periodic(checkInterval, (timer) {
      if (_isCancelled) {
        timer.cancel();
        if (!completer.isCompleted) {
          completer.completeError(
            CancelledException(
              _cancellationInfo?.message ?? '작업이 취소되었습니다.',
              _cancellationInfo,
            ),
          );
        }
      }
    });

    try {
      final result = await operation();
      if (!completer.isCompleted) {
        completer.complete(result);
      }
    } catch (e) {
      if (!completer.isCompleted) {
        completer.completeError(e);
      }
    }

    return completer.future;
  }

  /// 토큰 해제 (리소스 정리)
  void dispose() {
    _timeoutTimer?.cancel();
    _listeners.clear();
    _linkedTokens.clear();

    if (!_completer.isCompleted) {
      _completer.complete();
    }
  }

  @override
  String toString() {
    if (_isCancelled) {
      return 'CancellationToken(cancelled: true, reason: ${_cancellationInfo?.reason})';
    }
    return 'CancellationToken(cancelled: false)';
  }
}

/// 작업이 취소되었을 때 발생하는 예외
class CancelledException implements Exception {
  final String message;
  final CancellationInfo? cancellationInfo;

  CancelledException(this.message, [this.cancellationInfo]);

  @override
  String toString() {
    final info = cancellationInfo != null
        ? ' (${cancellationInfo!.reason})'
        : '';
    return 'CancelledException: $message$info';
  }
}

/// 취소 가능한 작업을 관리하는 매니저
class CancellationManager {
  static final Map<String, CancellationToken> _tokens = {};

  /// 토큰 생성 및 등록
  static CancellationToken createToken(String name) {
    final token = CancellationToken();
    _tokens[name] = token;
    return token;
  }

  /// 타임아웃 토큰 생성 및 등록
  static CancellationToken createTimeoutToken(String name, Duration timeout) {
    final token = CancellationToken.withTimeout(timeout);
    _tokens[name] = token;
    return token;
  }

  /// 등록된 토큰 가져오기
  static CancellationToken? getToken(String name) {
    return _tokens[name];
  }

  /// 토큰 취소
  static void cancelToken(
    String name, {
    CancellationReason reason = CancellationReason.userRequested,
    String? message,
  }) {
    final token = _tokens[name];
    if (token != null) {
      token.cancel(reason: reason, message: message);
    }
  }

  /// 모든 토큰 취소
  static void cancelAll({
    CancellationReason reason = CancellationReason.userRequested,
    String? message,
  }) {
    for (final token in _tokens.values) {
      token.cancel(reason: reason, message: message);
    }
  }

  /// 토큰 제거
  static void removeToken(String name) {
    final token = _tokens.remove(name);
    token?.dispose();
  }

  /// 모든 토큰 제거
  static void clear() {
    for (final token in _tokens.values) {
      token.dispose();
    }
    _tokens.clear();
  }

  /// 등록된 토큰 목록
  static List<String> get registeredTokens => _tokens.keys.toList();

  /// 활성 토큰 수
  static int get activeTokenCount =>
      _tokens.values.where((t) => !t.isCancelled).length;
}

// 주의: batch_processor.dart의 BatchCancellationToken과 이름이 다름. import 충돌 주의.

// BatchProcessor에서 요구하는 기능이 부족하다면 보완
