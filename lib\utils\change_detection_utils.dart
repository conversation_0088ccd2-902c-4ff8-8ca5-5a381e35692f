/// 바라 부스 매니저 - 변경 감지 유틸리티
///
/// 객체의 깊은 비교를 통한 정확한 변경 감지 기능을 제공하는 유틸리티 클래스입니다.
/// - 객체 필드별 변경 감지
/// - 배열/리스트 변경사항 세밀 추적
/// - 중첩 객체 변경 감지
/// - 성능 최적화된 비교 알고리즘
///
/// 주요 기능:
/// - 필드별 변경 감지 (detectFieldChanges)
/// - 리스트 변경 감지 (detectListChanges)
/// - 깊은 객체 비교 (deepCompare)
/// - 변경 요약 생성 (generateChangeSummary)
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import '../utils/logger_utils.dart';

/// 변경 감지 결과를 담는 클래스
class ChangeDetectionResult {
  final Map<String, dynamic> changedFields;
  final List<String> addedItems;
  final List<String> removedItems;
  final List<String> modifiedItems;
  final bool hasChanges;

  const ChangeDetectionResult({
    required this.changedFields,
    required this.addedItems,
    required this.removedItems,
    required this.modifiedItems,
    required this.hasChanges,
  });

  /// 변경사항이 없는 결과 생성
  factory ChangeDetectionResult.noChanges() {
    return const ChangeDetectionResult(
      changedFields: {},
      addedItems: [],
      removedItems: [],
      modifiedItems: [],
      hasChanges: false,
    );
  }

  /// 변경 요약 문자열 생성
  String get summary {
    if (!hasChanges) return '변경사항 없음';
    
    final parts = <String>[];
    if (changedFields.isNotEmpty) {
      parts.add('필드 변경: ${changedFields.keys.join(', ')}');
    }
    if (addedItems.isNotEmpty) {
      parts.add('추가: ${addedItems.length}개');
    }
    if (removedItems.isNotEmpty) {
      parts.add('삭제: ${removedItems.length}개');
    }
    if (modifiedItems.isNotEmpty) {
      parts.add('수정: ${modifiedItems.length}개');
    }
    
    return parts.join(', ');
  }
}

/// 변경 감지 유틸리티 클래스
class ChangeDetectionUtils {
  static const String _tag = 'ChangeDetectionUtils';

  /// 두 객체 간의 필드별 변경사항을 감지합니다
  static Map<String, dynamic> detectFieldChanges(
    Map<String, dynamic> oldData,
    Map<String, dynamic> newData, {
    List<String>? excludeFields,
  }) {
    try {
      final changedFields = <String, dynamic>{};
      final excludeSet = excludeFields?.toSet() ?? <String>{};

      // 새로운 필드나 변경된 필드 확인
      for (final entry in newData.entries) {
        final key = entry.key;
        final newValue = entry.value;

        if (excludeSet.contains(key)) continue;

        if (!oldData.containsKey(key)) {
          // 새로운 필드
          changedFields[key] = newValue;
        } else {
          final oldValue = oldData[key];
          if (!_deepEquals(oldValue, newValue)) {
            // 변경된 필드
            changedFields[key] = newValue;
          }
        }
      }

      // 삭제된 필드 확인 (null로 표시)
      for (final key in oldData.keys) {
        if (excludeSet.contains(key)) continue;
        
        if (!newData.containsKey(key)) {
          changedFields[key] = null;
        }
      }

      LoggerUtils.logDebug('필드 변경 감지 완료: ${changedFields.keys.length}개 변경', tag: _tag);
      return changedFields;
    } catch (e) {
      LoggerUtils.logError('필드 변경 감지 실패', tag: _tag, error: e);
      return {};
    }
  }

  /// 리스트의 변경사항을 감지합니다
  static ChangeDetectionResult detectListChanges<T>(
    List<T> oldList,
    List<T> newList, {
    String Function(T)? keyExtractor,
  }) {
    try {
      final addedItems = <String>[];
      final removedItems = <String>[];
      final modifiedItems = <String>[];

      // 키 추출 함수가 없으면 toString() 사용
      final getKey = keyExtractor ?? (item) => item.toString();

      // 이전 리스트를 맵으로 변환
      final oldMap = <String, T>{};
      for (final item in oldList) {
        oldMap[getKey(item)] = item;
      }

      // 새 리스트를 맵으로 변환
      final newMap = <String, T>{};
      for (final item in newList) {
        newMap[getKey(item)] = item;
      }

      // 추가된 항목 찾기
      for (final key in newMap.keys) {
        if (!oldMap.containsKey(key)) {
          addedItems.add(key);
        }
      }

      // 삭제된 항목 찾기
      for (final key in oldMap.keys) {
        if (!newMap.containsKey(key)) {
          removedItems.add(key);
        }
      }

      // 수정된 항목 찾기
      for (final key in newMap.keys) {
        if (oldMap.containsKey(key)) {
          final oldItem = oldMap[key];
          final newItem = newMap[key];
          if (!_deepEquals(oldItem, newItem)) {
            modifiedItems.add(key);
          }
        }
      }

      final hasChanges = addedItems.isNotEmpty || removedItems.isNotEmpty || modifiedItems.isNotEmpty;

      LoggerUtils.logDebug('리스트 변경 감지 완료: 추가 ${addedItems.length}, 삭제 ${removedItems.length}, 수정 ${modifiedItems.length}', tag: _tag);

      return ChangeDetectionResult(
        changedFields: {},
        addedItems: addedItems,
        removedItems: removedItems,
        modifiedItems: modifiedItems,
        hasChanges: hasChanges,
      );
    } catch (e) {
      LoggerUtils.logError('리스트 변경 감지 실패', tag: _tag, error: e);
      return ChangeDetectionResult.noChanges();
    }
  }

  /// 두 값의 깊은 비교를 수행합니다
  static bool _deepEquals(dynamic value1, dynamic value2) {
    try {
      // 동일한 참조인 경우
      if (identical(value1, value2)) return true;

      // null 체크
      if (value1 == null || value2 == null) {
        return value1 == value2;
      }

      // 타입이 다른 경우
      if (value1.runtimeType != value2.runtimeType) {
        return false;
      }

      // 기본 타입 비교
      if (value1 is String || value1 is num || value1 is bool) {
        return value1 == value2;
      }

      // DateTime 비교
      if (value1 is DateTime && value2 is DateTime) {
        return value1.millisecondsSinceEpoch == value2.millisecondsSinceEpoch;
      }

      // List 비교
      if (value1 is List && value2 is List) {
        if (value1.length != value2.length) return false;
        for (int i = 0; i < value1.length; i++) {
          if (!_deepEquals(value1[i], value2[i])) return false;
        }
        return true;
      }

      // Map 비교
      if (value1 is Map && value2 is Map) {
        if (value1.length != value2.length) return false;
        for (final key in value1.keys) {
          if (!value2.containsKey(key)) return false;
          if (!_deepEquals(value1[key], value2[key])) return false;
        }
        return true;
      }

      // JSON 직렬화를 통한 비교 (fallback)
      try {
        final json1 = jsonEncode(value1);
        final json2 = jsonEncode(value2);
        return json1 == json2;
      } catch (e) {
        // JSON 직렬화 실패 시 toString() 비교
        return value1.toString() == value2.toString();
      }
    } catch (e) {
      LoggerUtils.logWarning('깊은 비교 중 오류 발생, toString() 비교로 fallback', tag: _tag, error: e);
      return value1.toString() == value2.toString();
    }
  }

  /// 변경 요약을 생성합니다
  static String generateChangeSummary(ChangeDetectionResult result) {
    return result.summary;
  }

  /// 객체를 Map으로 변환합니다 (JSON 직렬화 가능한 객체용)
  static Map<String, dynamic> objectToMap(dynamic object) {
    try {
      if (object == null) return {};
      
      // 이미 Map인 경우
      if (object is Map<String, dynamic>) {
        return object;
      }

      // JSON 직렬화를 통한 변환
      final jsonString = jsonEncode(object);
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      LoggerUtils.logError('객체를 Map으로 변환 실패', tag: _tag, error: e);
      return {};
    }
  }
}
