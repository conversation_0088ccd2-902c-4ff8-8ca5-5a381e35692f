import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'logger_utils.dart';

/// 공통 유틸리티 함수들
/// 
/// 여러 곳에서 사용되는 공통 기능들을 모아놓은 유틸리티 클래스입니다.
class CommonUtils {
  static const String _tag = 'CommonUtils';

  /// 안전한 정수 변환
  static int safeParseInt(dynamic value, {int defaultValue = 0}) {
    if (value == null) return defaultValue;
    
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    
    return defaultValue;
  }

  /// 안전한 실수 변환
  static double safeParseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    
    return defaultValue;
  }

  /// 안전한 문자열 변환
  static String safeParseString(dynamic value, {String defaultValue = ''}) {
    if (value == null) return defaultValue;
    return value.toString();
  }

  /// 안전한 불린 변환
  static bool safeParseBool(dynamic value, {bool defaultValue = false}) {
    if (value == null) return defaultValue;
    
    if (value is bool) return value;
    if (value is int) return value != 0;
    if (value is String) {
      final lowerValue = value.toLowerCase();
      return lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes';
    }
    
    return defaultValue;
  }

  /// 날짜 포맷팅
  static String formatDate(DateTime? date, {String format = 'yyyy-MM-dd'}) {
    if (date == null) return '';
    
    try {
      // 간단한 포맷팅 (더 복잡한 포맷이 필요하면 intl 패키지 사용)
      switch (format) {
        case 'yyyy-MM-dd':
          return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        case 'MM/dd/yyyy':
          return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
        case 'yyyy년 MM월 dd일':
          return '${date.year}년 ${date.month}월 ${date.day}일';
        default:
          return date.toString();
      }
    } catch (e) {
      LoggerUtils.logError('Date formatting failed', tag: _tag, error: e);
      return '';
    }
  }

  /// 시간 포맷팅
  static String formatTime(DateTime? time, {bool includeSeconds = false}) {
    if (time == null) return '';
    
    try {
      final hour = time.hour.toString().padLeft(2, '0');
      final minute = time.minute.toString().padLeft(2, '0');
      
      if (includeSeconds) {
        final second = time.second.toString().padLeft(2, '0');
        return '$hour:$minute:$second';
      }
      
      return '$hour:$minute';
    } catch (e) {
      LoggerUtils.logError('Time formatting failed', tag: _tag, error: e);
      return '';
    }
  }

  /// 가격 포맷팅
  static String formatPrice(int? price, {String currency = '원'}) {
    if (price == null) return '0$currency';
    
    try {
      // 천 단위 구분자 추가
      final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
      final priceString = price.toString().replaceAllMapped(formatter, (Match m) => '${m[1]},');
      return '$priceString$currency';
    } catch (e) {
      LoggerUtils.logError('Price formatting failed', tag: _tag, error: e);
      return '$price$currency';
    }
  }

  /// 문자열 유효성 검사
  static bool isValidString(String? value, {int minLength = 1}) {
    return value != null && value.trim().length >= minLength;
  }

  /// 이메일 유효성 검사
  static bool isValidEmail(String? email) {
    if (email == null || email.isEmpty) return false;
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email);
  }

  /// 전화번호 유효성 검사 (한국 형식)
  static bool isValidPhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) return false;
    
    // 한국 전화번호 패턴 (010-1234-5678, 02-123-4567 등)
    final phoneRegex = RegExp(r'^(01[016789]|02|0[3-9][0-9])-?[0-9]{3,4}-?[0-9]{4}$');
    return phoneRegex.hasMatch(phone.replaceAll('-', '').replaceAll(' ', ''));
  }

  /// 랜덤 문자열 생성
  static String generateRandomString(int length, {bool includeNumbers = true, bool includeSymbols = false}) {
    const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    String chars = letters;
    if (includeNumbers) chars += numbers;
    if (includeSymbols) chars += symbols;
    
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// 디바운스 함수
  static Timer? _debounceTimer;
  static void debounce(VoidCallback callback, {Duration delay = const Duration(milliseconds: 300)}) {
    if (_debounceTimer != null) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(delay, callback);
  }

  /// 디바운스 타이머 정리 (앱 종료 시 호출)
  static void disposeDebounceTimer() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
  }

  /// 스로틀 함수
  static DateTime? _lastThrottleTime;
  static void throttle(VoidCallback callback, {Duration delay = const Duration(milliseconds: 300)}) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || now.difference(_lastThrottleTime!) >= delay) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// 리스트 청크 분할
  static List<List<T>> chunkList<T>(List<T> list, int chunkSize) {
    if (chunkSize <= 0) throw ArgumentError('Chunk size must be positive');
    
    final chunks = <List<T>>[];
    for (int i = 0; i < list.length; i += chunkSize) {
      final end = (i + chunkSize < list.length) ? i + chunkSize : list.length;
      chunks.add(list.sublist(i, end));
    }
    return chunks;
  }

  /// 안전한 비동기 실행
  static Future<T?> safeAsyncExecution<T>(
    Future<T> Function() operation, {
    String? operationName,
    T? defaultValue,
  }) async {
    try {
      return await operation();
    } catch (e) {
      LoggerUtils.logError(
        'Safe async execution failed: ${operationName ?? 'unknown'}',
        tag: _tag,
        error: e,
      );
      return defaultValue;
    }
  }

  /// 색상 유틸리티
  static Color darkenColor(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  static Color lightenColor(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  /// 파일 크기 포맷팅
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';
    
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    final i = (log(bytes) / log(1024)).floor();
    final size = bytes / pow(1024, i);
    
    return '${size.toStringAsFixed(1)} ${suffixes[i]}';
  }

  /// 퍼센트 계산
  static double calculatePercentage(num value, num total) {
    if (total == 0) return 0.0;
    return (value / total) * 100;
  }

  /// 안전한 나눗셈
  static double safeDivide(num dividend, num divisor, {double defaultValue = 0.0}) {
    if (divisor == 0) return defaultValue;
    return dividend / divisor;
  }

  /// 범위 내 값으로 제한
  static T clamp<T extends num>(T value, T min, T max) {
    if (value < min) return min;
    if (value > max) return max;
    return value;
  }
}

/// 앱에서 사용하는 문자열 상수들을 정의합니다.
class Strings {
  // 공통
  static const String appName = '블루부스 매니저';
  static const String ok = '확인';
  static const String cancel = '취소';
  static const String error = '오류';
  static const String retry = '다시 시도';
  static const String loading = '로딩 중...';
  static const String success = '성공';
  static const String warning = '경고';
  static const String info = '안내';

  // 판매 관련
  static const String saleCompleted = '판매가 완료되었습니다.';
  static const String saleError = '판매 처리 중 오류가 발생했습니다.';
  static const String noProductSelected = '선택된 상품이 없습니다.';
  static const String outOfStock = '재고가 부족합니다.';
  static const String invalidQuantity = '수량이 올바르지 않습니다.';
}
