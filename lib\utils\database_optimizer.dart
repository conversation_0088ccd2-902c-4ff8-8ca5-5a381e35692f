import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'logger_utils.dart';

/// 데이터베이스 성능 최적화 유틸리티
///
/// SQLite 데이터베이스의 성능을 최적화하기 위한 다양한 기능을 제공합니다.
class DatabaseOptimizer {
  static const String _tag = 'DatabaseOptimizer';
  static bool _isOptimized = false;
  static bool _periodicOptimizationScheduled = false;
  static DateTime? _lastOptimization;
  static StreamSubscription? _periodicSubscription; // 구독 관리용 추가

  /// 데이터베이스 성능 최적화 설정 적용 (중복 실행 방지)
  static Future<void> optimizeDatabase(Database db, {bool force = false}) async {
    // 이미 최적화되었고 강제 실행이 아닌 경우 스킵
    if (_isOptimized && !force) {
      LoggerUtils.logDebug('Database already optimized, skipping', tag: _tag);
      return;
    }

    // 최근에 최적화되었다면 스킵 (1시간 이내)
    if (_lastOptimization != null && !force) {
      final timeSinceLastOptimization = DateTime.now().difference(_lastOptimization!);
      if (timeSinceLastOptimization.inHours < 1) {
        LoggerUtils.logDebug('Database optimized recently, skipping', tag: _tag);
        return;
      }
    }

    try {
      LoggerUtils.logInfo('Starting database optimization', tag: _tag);

      // PRAGMA 설정으로 성능 최적화 (항상 실행)
      await _applyPragmaSettings(db);

      // 통계 정보 업데이트 (조건부 실행)
      if (force || _shouldUpdateStatistics()) {
        await _updateStatistics(db);
      }

      // 인덱스 최적화 (가벼운 작업이므로 항상 실행)
      await _optimizeIndexes(db);

      _isOptimized = true;
      _lastOptimization = DateTime.now();

      LoggerUtils.logInfo('Database optimization completed', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to optimize database', tag: _tag, error: e);
    }
  }

  /// 통계 정보 업데이트가 필요한지 확인
  static bool _shouldUpdateStatistics() {
    // 처음 실행이거나 6시간 이상 지났을 때만 실행
    if (_lastOptimization == null) return true;
    final timeSinceLastOptimization = DateTime.now().difference(_lastOptimization!);
    return timeSinceLastOptimization.inHours >= 6;
  }

  /// PRAGMA 설정 적용
  static Future<void> _applyPragmaSettings(Database db) async {
    try {
      // WAL 모드 활성화 (읽기/쓰기 성능 향상)
      await db.rawQuery('PRAGMA journal_mode = WAL');

      // WAL 체크포인트 자동 관리 설정
      await db.rawQuery('PRAGMA wal_autocheckpoint = 1000'); // 1000 페이지마다 체크포인트

      // 동기화 모드 최적화 (성능 vs 안정성 균형)
      await db.rawQuery('PRAGMA synchronous = NORMAL');

      // 캐시 크기 최적화 (메모리 사용량 vs 성능)
      await db.rawQuery('PRAGMA cache_size = -2000'); // 2MB 캐시

      // 임시 저장소를 메모리로 설정
      await db.rawQuery('PRAGMA temp_store = MEMORY');

      // 메모리 맵 크기 설정 (64MB)
      await db.rawQuery('PRAGMA mmap_size = 67108864');

      // 자동 VACUUM 활성화
      await db.rawQuery('PRAGMA auto_vacuum = INCREMENTAL');

      // 외래 키 제약 조건 활성화
      await db.rawQuery('PRAGMA foreign_keys = ON');

      // WAL 모드 최적화 설정
      await db.rawQuery('PRAGMA wal_checkpoint(TRUNCATE)'); // 초기 체크포인트 실행

      LoggerUtils.logDebug('PRAGMA settings applied with WAL optimization', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to apply PRAGMA settings', tag: _tag, error: e);
    }
  }

  /// 통계 정보 업데이트
  static Future<void> _updateStatistics(Database db) async {
    try {
      // SQLite 통계 정보 업데이트 (쿼리 최적화에 도움)
      await db.execute('ANALYZE');
      
      LoggerUtils.logDebug('Database statistics updated', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to update statistics', tag: _tag, error: e);
    }
  }

  /// 인덱스 최적화 및 성능 분석
  static Future<void> _optimizeIndexes(Database db) async {
    try {
      // 기존 인덱스 확인
      final indexes = await db.rawQuery(
        "SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND sql IS NOT NULL"
      );

      LoggerUtils.logDebug(
        'Found ${indexes.length} custom indexes',
        tag: _tag,
      );

      // 주요 테이블에 대한 성능 최적화 인덱스 확인 및 생성
      await _ensurePerformanceIndexes(db);

      // 인덱스 통계 업데이트 (가벼운 작업)
      for (final index in indexes) {
        final indexName = index['name'] as String;
        if (!indexName.startsWith('sqlite_')) {
          try {
            await db.execute('REINDEX $indexName');
          } catch (e) {
            LoggerUtils.logWarning('Failed to reindex $indexName', tag: _tag, error: e);
          }
        }
      }

      LoggerUtils.logDebug('Index optimization completed', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to optimize indexes', tag: _tag, error: e);
    }
  }

  /// 성능 최적화를 위한 필수 인덱스 확인 및 생성
  static Future<void> _ensurePerformanceIndexes(Database db) async {
    try {
      // 자주 사용되는 쿼리에 대한 인덱스 확인
      final requiredIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_products_eventId ON products(eventId)',
        'CREATE INDEX IF NOT EXISTS idx_products_active ON products(isActive)',
        'CREATE INDEX IF NOT EXISTS idx_products_event_active ON products(eventId, isActive)',
        'CREATE INDEX IF NOT EXISTS idx_sales_log_eventId ON sales_log(eventId)',
        'CREATE INDEX IF NOT EXISTS idx_sales_log_timestamp ON sales_log(saleTimestamp)',
        'CREATE INDEX IF NOT EXISTS idx_prepayments_eventId ON prepayments(eventId)',
        'CREATE INDEX IF NOT EXISTS idx_sellers_eventId ON sellers(eventId)',
      ];

      for (final indexSql in requiredIndexes) {
        try {
          await db.execute(indexSql);
        } catch (e) {
          LoggerUtils.logWarning('Failed to create index: $indexSql', tag: _tag, error: e);
        }
      }

      LoggerUtils.logDebug('Performance indexes ensured', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to ensure performance indexes', tag: _tag, error: e);
    }
  }

  /// 배치 삽입 최적화
  static Future<void> batchInsert(
    Database db,
    String table,
    List<Map<String, dynamic>> values, {
    int batchSize = 100,
  }) async {
    if (values.isEmpty) return;

    try {
      LoggerUtils.logDebug(
        'Starting batch insert: ${values.length} records',
        tag: _tag,
      );

      await db.transaction((txn) async {
        for (int i = 0; i < values.length; i += batchSize) {
          final batch = values.skip(i).take(batchSize).toList();
          
          for (final value in batch) {
            await txn.insert(table, value);
          }
        }
      });

      LoggerUtils.logDebug(
        'Batch insert completed: ${values.length} records',
        tag: _tag,
      );
    } catch (e) {
      LoggerUtils.logError('Batch insert failed', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 배치 업데이트 최적화
  static Future<void> batchUpdate(
    Database db,
    String table,
    List<Map<String, dynamic>> updates,
    String whereColumn, {
    int batchSize = 100,
  }) async {
    if (updates.isEmpty) return;

    try {
      LoggerUtils.logDebug(
        'Starting batch update: ${updates.length} records',
        tag: _tag,
      );

      await db.transaction((txn) async {
        for (int i = 0; i < updates.length; i += batchSize) {
          final batch = updates.skip(i).take(batchSize).toList();
          
          for (final update in batch) {
            final whereValue = update[whereColumn];
            final updateData = Map<String, dynamic>.from(update);
            updateData.remove(whereColumn);
            
            await txn.update(
              table,
              updateData,
              where: '$whereColumn = ?',
              whereArgs: [whereValue],
            );
          }
        }
      });

      LoggerUtils.logDebug(
        'Batch update completed: ${updates.length} records',
        tag: _tag,
      );
    } catch (e) {
      LoggerUtils.logError('Batch update failed', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 데이터베이스 정리 및 최적화
  static Future<void> vacuum(Database db) async {
    try {
      LoggerUtils.logInfo('Starting database vacuum', tag: _tag);
      
      // 증분 VACUUM 실행
      await db.rawQuery('PRAGMA incremental_vacuum');
      
      LoggerUtils.logInfo('Database vacuum completed', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Database vacuum failed', tag: _tag, error: e);
    }
  }

  /// 데이터베이스 상태 정보 가져오기
  static Future<Map<String, dynamic>> getDatabaseInfo(Database db) async {
    try {
      final info = <String, dynamic>{};
      
      // 데이터베이스 크기
      final sizeResult = await db.rawQuery('PRAGMA page_count');
      final pageSizeResult = await db.rawQuery('PRAGMA page_size');
      
      if (sizeResult.isNotEmpty && pageSizeResult.isNotEmpty) {
        final pageCount = sizeResult.first['page_count'] as int;
        final pageSize = pageSizeResult.first['page_size'] as int;
        info['database_size_bytes'] = pageCount * pageSize;
      }
      
      // 캐시 정보
      final cacheResult = await db.rawQuery('PRAGMA cache_size');
      if (cacheResult.isNotEmpty) {
        info['cache_size'] = cacheResult.first['cache_size'];
      }
      
      // 저널 모드
      final journalResult = await db.rawQuery('PRAGMA journal_mode');
      if (journalResult.isNotEmpty) {
        info['journal_mode'] = journalResult.first['journal_mode'];
      }
      
      // 동기화 모드
      final syncResult = await db.rawQuery('PRAGMA synchronous');
      if (syncResult.isNotEmpty) {
        info['synchronous'] = syncResult.first['synchronous'];
      }
      
      return info;
    } catch (e) {
      LoggerUtils.logError('Failed to get database info', tag: _tag, error: e);
      return {};
    }
  }

  /// 쿼리 성능 분석
  static Future<void> analyzeQuery(Database db, String query) async {
    try {
      // EXPLAIN QUERY PLAN으로 쿼리 실행 계획 분석
      final plan = await db.rawQuery('EXPLAIN QUERY PLAN $query');
      
      LoggerUtils.logDebug(
        'Query plan for: $query',
        tag: _tag,
      );
      
      for (final row in plan) {
        LoggerUtils.logDebug(
          'Plan: ${row.toString()}',
          tag: _tag,
        );
      }
    } catch (e) {
      LoggerUtils.logError('Failed to analyze query', tag: _tag, error: e);
    }
  }

  /// WAL 체크포인트 수동 실행
  static Future<void> checkpointWAL(Database db) async {
    try {
      LoggerUtils.logDebug('Running WAL checkpoint', tag: _tag);

      // WAL 파일 크기 확인
      final walInfo = await db.rawQuery('PRAGMA wal_checkpoint(PASSIVE)');
      if (walInfo.isNotEmpty) {
        final walSize = walInfo.first['wal'] as int? ?? 0;
        final checkpointed = walInfo.first['checkpointed'] as int? ?? 0;

        LoggerUtils.logDebug(
          'WAL checkpoint: $checkpointed pages checkpointed, $walSize pages remaining',
          tag: _tag,
        );

        // WAL 파일이 너무 크면 강제 체크포인트
        if (walSize > 1000) {
          await db.rawQuery('PRAGMA wal_checkpoint(TRUNCATE)');
          LoggerUtils.logInfo('Forced WAL checkpoint due to large WAL file', tag: _tag);
        }
      }
    } catch (e) {
      LoggerUtils.logError('WAL checkpoint failed', tag: _tag, error: e);
    }
  }

  /// 정기적인 데이터베이스 최적화 스케줄링 (중복 실행 방지)
  static void schedulePeriodicOptimization(Database db) {
    // 이미 스케줄링되었다면 중복 실행 방지
    if (_periodicOptimizationScheduled) {
      LoggerUtils.logDebug('Periodic optimization already scheduled', tag: _tag);
      return;
    }

    _periodicOptimizationScheduled = true;
    LoggerUtils.logInfo('Scheduling periodic database optimization', tag: _tag);

    // 6시간마다 최적화 실행 (1시간에서 6시간으로 변경하여 부하 감소)
    _periodicSubscription = Stream.periodic(const Duration(hours: 6)).listen((_) async {
      try {
        LoggerUtils.logInfo('Running periodic database optimization', tag: _tag);

        // WAL 체크포인트 실행
        await checkpointWAL(db);

        // 통계 정보 업데이트 (조건부)
        if (_shouldUpdateStatistics()) {
          await _updateStatistics(db);
        }

        // VACUUM 실행 (가벼운 증분 VACUUM)
        await vacuum(db);

        _lastOptimization = DateTime.now();
        LoggerUtils.logInfo('Periodic database optimization completed', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('Periodic database optimization failed', tag: _tag, error: e);
      }
    });
  }

  /// 앱 종료 시 모든 리소스 정리 (메모리 누수 방지)
  static void shutdown() {
    _periodicSubscription?.cancel();
    _periodicSubscription = null;
    _periodicOptimizationScheduled = false;
    _lastOptimization = null;
    _isOptimized = false;
    LoggerUtils.logInfo('DatabaseOptimizer shutdown 완료', tag: _tag);
  }
}
