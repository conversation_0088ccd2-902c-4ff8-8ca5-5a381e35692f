import 'package:intl/intl.dart';

/// 날짜/시간 처리 및 포맷팅을 지원하는 유틸리티 클래스입니다.
/// - 한국 표준시, 포맷 변환, 날짜 연산, 휴일/요일 계산 등 지원
/// - riverpod 3.x Provider/Repository와 연동, UX 개선 목적
class DateUtils {
  // 날짜 포맷터들
  static final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  static final DateFormat _timeFormat = DateFormat('HH:mm:ss');
  static final DateFormat _dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  static final DateFormat _displayDateFormat = DateFormat('M월 d일');
  static final DateFormat _displayDateTimeFormat = DateFormat('M월 d일 HH:mm');
  static final DateFormat _fullDisplayFormat = DateFormat(
    'yyyy년 M월 d일 (E)',
    'ko_KR',
  );
  static final DateFormat _dotDateFormat = DateFormat('yyyy.MM.dd');
  static final DateFormat _dotDateTimeFormat = DateFormat('yyyy.MM.dd HH:mm');
  static final DateFormat _korDateTimeFormat = DateFormat(
    'yyyy년 M월 d일 (E) a h:mm',
    'ko_KR',
  );
  static final DateFormat _korDateFormat = DateFormat(
    'yyyy년 M월 d일 (E)',
    'ko_KR',
  );
  static final DateFormat _korTimeFormat = DateFormat('a h:mm', 'ko_KR');

  /// 현재 타임스탬프 반환 (밀리초)
  static int getCurrentTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  /// 타임스탬프를 DateTime으로 변환
  static DateTime timestampToDateTime(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  /// DateTime을 타임스탬프로 변환
  static int dateTimeToTimestamp(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch;
  }

  /// 날짜를 'yyyy-MM-dd' 형식으로 포맷
  static String formatDate(DateTime date) {
    return _dateFormat.format(date);
  }

  /// 시간을 'HH:mm:ss' 형식으로 포맷
  static String formatTime(DateTime date) {
    return _timeFormat.format(date);
  }

  /// 날짜와 시간을 'yyyy-MM-dd HH:mm:ss' 형식으로 포맷
  static String formatDateTime(DateTime date) {
    return _dateTimeFormat.format(date);
  }

  /// 표시용 날짜 포맷 ('M월 d일')
  static String formatDisplayDate(DateTime date) {
    return _displayDateFormat.format(date);
  }

  /// 표시용 날짜시간 포맷 ('M월 d일 HH:mm')
  static String formatDisplayDateTime(DateTime date) {
    return _displayDateTimeFormat.format(date);
  }

  /// 전체 표시용 날짜 포맷 ('yyyy년 M월 d일 (요일)')
  static String formatFullDisplay(DateTime date) {
    return _fullDisplayFormat.format(date);
  }

  /// 타임스탬프를 표시용 날짜로 포맷
  static String formatTimestampToDisplayDate(int timestamp) {
    return formatDisplayDate(timestampToDateTime(timestamp));
  }

  /// 타임스탬프를 표시용 날짜시간으로 포맷
  static String formatTimestampToDisplayDateTime(int timestamp) {
    return formatDisplayDateTime(timestampToDateTime(timestamp));
  }

  /// 문자열을 DateTime으로 파싱 ('yyyy-MM-dd' 형식)
  static DateTime? parseDate(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// 문자열을 DateTime으로 파싱 ('yyyy-MM-dd HH:mm:ss' 형식)
  static DateTime? parseDateTime(String dateTimeString) {
    try {
      return _dateTimeFormat.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// 요일 번호를 한글 요일명으로 변환 (1=월요일, 7=일요일)
  static String getDayOfWeekName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return '월요일';
      case 2:
        return '화요일';
      case 3:
        return '수요일';
      case 4:
        return '목요일';
      case 5:
        return '금요일';
      case 6:
        return '토요일';
      case 7:
        return '일요일';
      default:
        return '알 수 없음';
    }
  }

  /// 요일 번호를 짧은 한글 요일명으로 변환 (1=월, 7=일)
  static String getDayOfWeekShortName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return '월';
      case 2:
        return '화';
      case 3:
        return '수';
      case 4:
        return '목';
      case 5:
        return '금';
      case 6:
        return '토';
      case 7:
        return '일';
      default:
        return '?';
    }
  }

  /// DateTime의 요일을 번호로 반환 (1=월요일, 7=일요일)
  static int getWeekdayNumber(DateTime date) {
    // DateTime.weekday는 1=월요일, 7=일요일이므로 그대로 반환
    return date.weekday;
  }

  /// 오늘이 특정 요일인지 확인
  static bool isTodayDayOfWeek(int dayOfWeek) {
    return getWeekdayNumber(DateTime.now()) == dayOfWeek;
  }

  /// 두 날짜가 같은 날인지 확인
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 오늘인지 확인
  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }

  /// 어제인지 확인
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  /// 내일인지 확인
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return isSameDay(date, tomorrow);
  }

  /// 상대적 날짜 표시 (오늘, 어제, 내일, 또는 날짜)
  static String getRelativeDateString(DateTime date) {
    if (isToday(date)) {
      return '오늘';
    } else if (isYesterday(date)) {
      return '어제';
    } else if (isTomorrow(date)) {
      return '내일';
    } else {
      return formatDisplayDate(date);
    }
  }

  /// 날짜 범위의 시작 시간 (00:00:00)
  static DateTime getStartOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// 날짜 범위의 끝 시간 (23:59:59.999)
  static DateTime getEndOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// 주의 시작일 (월요일) 반환
  static DateTime getStartOfWeek(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  /// 주의 끝일 (일요일) 반환
  static DateTime getEndOfWeek(DateTime date) {
    final weekday = date.weekday;
    return date.add(Duration(days: 7 - weekday));
  }

  /// 월의 시작일 반환
  static DateTime getStartOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// 월의 끝일 반환
  static DateTime getEndOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0);
  }

  /// 두 날짜 간의 일수 차이 계산
  static int daysBetween(DateTime date1, DateTime date2) {
    final difference = date2.difference(date1);
    return difference.inDays;
  }

  /// 경과 시간을 문자열로 표시
  static String getTimeAgoString(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}일 전';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}시간 전';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}분 전';
    } else {
      return '방금 전';
    }
  }

  /// 타임스탬프를 경과 시간 문자열로 표시
  static String getTimestampAgoString(int timestamp) {
    return getTimeAgoString(timestampToDateTime(timestamp));
  }

  /// 오전/오후 한글 표기 변환
  static String formatAmPm(DateTime date) {
    return date.hour < 12 ? '오전' : '오후';
  }

  /// yyyy.MM.dd 포맷
  static String formatDotDate(DateTime date) {
    return _dotDateFormat.format(date);
  }

  /// yyyy.MM.dd HH:mm 포맷
  static String formatDotDateTime(DateTime date) {
    return _dotDateTimeFormat.format(date);
  }

  /// yyyy년 M월 d일 (요일) a h:mm (오전/오후)
  static String formatKorDateTime(DateTime date) {
    return _korDateTimeFormat.format(date);
  }

  /// yyyy년 M월 d일 (요일)
  static String formatKorDate(DateTime date) {
    return _korDateFormat.format(date);
  }

  /// a h:mm (오전/오후 h:mm)
  static String formatKorTime(DateTime date) {
    return _korTimeFormat.format(date);
  }

  /// 기간 포맷: 2025.06.01 ~ 2025.06.30
  static String formatPeriod(DateTime start, DateTime end) {
    return '${formatDotDate(start)} ~ ${formatDotDate(end)}';
  }

  /// 기간 포맷: 6월 1일~30일
  static String formatMonthPeriod(DateTime start, DateTime end) {
    if (start.month == end.month && start.year == end.year) {
      return '${start.month}월 ${start.day}일~${end.day}일';
    } else {
      return '${formatDisplayDate(start)}~${formatDisplayDate(end)}';
    }
  }

  /// 상대적 시간(방금 전, 1분 전, 1시간 전, 어제, 3일 전, 1주 전, 1달 전, 1년 전)
  static String getRelativeTimeString(DateTime date) {
    final now = DateTime.now();
    final diff = now.difference(date);
    if (diff.inSeconds < 60) {
      return '방금 전';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}분 전';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}시간 전';
    } else if (diff.inDays == 1) {
      return '어제';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}일 전';
    } else if (diff.inDays < 30) {
      return '${(diff.inDays / 7).floor()}주 전';
    } else if (diff.inDays < 365) {
      return '${(diff.inDays / 30).floor()}달 전';
    } else {
      return '${(diff.inDays / 365).floor()}년 전';
    }
  }

  /// KST(한국 표준시)로 변환
  static DateTime toKst(DateTime date) {
    // DateTime은 기본적으로 local, UTC 구분. 한국은 UTC+9
    if (date.isUtc) {
      return date.add(const Duration(hours: 9));
    } else {
      return date;
    }
  }

  /// UTC → KST 변환 후 포맷
  static String formatUtcToKst(DateTime utcDate, {String? format}) {
    final kst = toKst(utcDate);
    if (format == null) {
      return formatKorDateTime(kst);
    } else {
      return DateFormat(format, 'ko_KR').format(kst);
    }
  }
}

/// 원본 앱과의 호환성을 위한 별칭
typedef AppDateUtils = DateUtils;
