import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/settings_provider.dart';

/// 디바이스 타입 감지 및 적응형 UI를 위한 유틸리티 클래스
/// 
/// 화면 크기, 픽셀 밀도, 가로세로 비율을 종합적으로 고려하여
/// 더 정확한 디바이스 타입 판별과 적응형 레이아웃을 제공합니다.
class DeviceUtils {
  DeviceUtils._();

  // === 디바이스 타입 판별 ===

  /// 종합적인 스마트폰 감지
  /// 화면 크기 + 픽셀 밀도 + 가로세로 비율을 모두 고려
  static bool isSmartphone(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;

    // 논리적 픽셀 기준으로 더 간단하고 정확한 판별
    final shortestSide = size.shortestSide;
    final longestSide = size.longestSide;

    // 정확한 타블렛 감지 기준 (7인치 이상 AND 최소 너비 600dp 이상)
    final diagonalDp = sqrt(shortestSide * shortestSide + longestSide * longestSide);

    // 7인치 = 672dp (96dpi 기준: 7 * 96 = 672)
    const double minDiagonalDp = 672.0; // 7인치
    const double minShortestSideDp = 600.0; // 최소 너비

    // 타블렛 조건: 대각선 7인치 이상 AND 최소 너비 600dp 이상 (AND 조건)
    if (diagonalDp >= minDiagonalDp && shortestSide >= minShortestSideDp) {
      return false; // 타블렛
    }

    return true; // 스마트폰
  }

  /// 종합적인 타블렛 감지
  static bool isTablet(BuildContext context) {
    return !isSmartphone(context);
  }

  /// 저장된 디바이스 타입을 우선적으로 사용하는 스마트폰 감지 (화면 회전에 영향받지 않음)
  /// WidgetRef가 있는 경우 저장된 값을 사용하고, 없는 경우 실시간 감지
  static bool isSmartphoneWithRef(BuildContext context, WidgetRef? ref) {
    if (ref != null) {
      try {
        // 저장된 디바이스 타입 사용 (화면 회전에 영향받지 않음)
        return ref.read(isSmartphoneDeviceProvider);
      } catch (e) {
        // Provider 읽기 실패 시 실시간 감지로 fallback
        return isSmartphone(context);
      }
    }
    // ref가 없는 경우 실시간 감지
    return isSmartphone(context);
  }

  /// 저장된 디바이스 타입을 우선적으로 사용하는 태블릿 감지 (화면 회전에 영향받지 않음)
  /// WidgetRef가 있는 경우 저장된 값을 사용하고, 없는 경우 실시간 감지
  static bool isTabletWithRef(BuildContext context, WidgetRef? ref) {
    if (ref != null) {
      try {
        // 저장된 디바이스 타입 사용 (화면 회전에 영향받지 않음)
        return ref.read(isTabletDeviceProvider);
      } catch (e) {
        // Provider 읽기 실패 시 실시간 감지로 fallback
        return isTablet(context);
      }
    }
    // ref가 없는 경우 실시간 감지
    return isTablet(context);
  }

  /// BuildContext 없이 화면 크기 기반으로 타블렛 여부 감지
  /// SettingsNotifier 등 초기화 시점에서 사용
  static bool isTabletWithoutContext() {
    try {
      // WidgetsBinding을 통해 화면 크기 가져오기
      final window = WidgetsBinding.instance.platformDispatcher.views.first;
      final physicalSize = window.physicalSize;
      final devicePixelRatio = window.devicePixelRatio;
      
      // 논리적 픽셀로 변환
      final logicalWidth = physicalSize.width / devicePixelRatio;
      final logicalHeight = physicalSize.height / devicePixelRatio;
      final shortestSide = logicalWidth < logicalHeight ? logicalWidth : logicalHeight;
      final longestSide = logicalWidth > logicalHeight ? logicalWidth : logicalHeight;
      
      // 개선된 타블렛 감지 로직 (isSmartphone과 동일)
      final aspectRatio = longestSide / shortestSide;
      final diagonalDp = sqrt(shortestSide * shortestSide + longestSide * longestSide);

      print('🔍 DeviceUtils.isTabletWithoutContext() 상세 정보:');
      print('  📱 물리적 크기: ${physicalSize.width.toStringAsFixed(1)} x ${physicalSize.height.toStringAsFixed(1)}px');
      print('  📏 픽셀 비율: ${devicePixelRatio.toStringAsFixed(2)}');
      print('  📐 논리적 크기: ${logicalWidth.toStringAsFixed(1)} x ${logicalHeight.toStringAsFixed(1)}dp');
      print('  📏 최소 변: ${shortestSide.toStringAsFixed(1)}dp');
      print('  📏 최대 변: ${longestSide.toStringAsFixed(1)}dp');
      print('  📐 가로세로 비율: ${aspectRatio.toStringAsFixed(2)}');
      print('  📏 대각선: ${diagonalDp.toStringAsFixed(1)}dp');

      // 정확한 타블렛 감지 조건 (7인치 이상 AND 최소 너비 600dp 이상)
      // 7인치 = 672dp (96dpi 기준: 7 * 96 = 672)
      const double minDiagonalDp = 672.0; // 7인치
      const double minShortestSideDp = 600.0; // 최소 너비

      // 타블렛 조건: 대각선 7인치 이상 AND 최소 너비 600dp 이상 (AND 조건)
      if (diagonalDp >= minDiagonalDp && shortestSide >= minShortestSideDp) {
        print('  ✅ 타블렛 감지: 대각선 ${diagonalDp.toStringAsFixed(1)}dp >= ${minDiagonalDp}dp AND 최소 변 ${shortestSide.toStringAsFixed(1)}dp >= ${minShortestSideDp}dp');
        return true;
      }

      // 조건 불충족 시 상세 로그
      if (diagonalDp >= minDiagonalDp) {
        print('  📱 스마트폰 감지: 대각선은 충족(${diagonalDp.toStringAsFixed(1)}dp >= ${minDiagonalDp}dp)하지만 최소 변 부족(${shortestSide.toStringAsFixed(1)}dp < ${minShortestSideDp}dp)');
      } else if (shortestSide >= minShortestSideDp) {
        print('  📱 스마트폰 감지: 최소 변은 충족(${shortestSide.toStringAsFixed(1)}dp >= ${minShortestSideDp}dp)하지만 대각선 부족(${diagonalDp.toStringAsFixed(1)}dp < ${minDiagonalDp}dp)');
      } else {
        print('  📱 스마트폰 감지: 대각선(${diagonalDp.toStringAsFixed(1)}dp < ${minDiagonalDp}dp)과 최소 변(${shortestSide.toStringAsFixed(1)}dp < ${minShortestSideDp}dp) 모두 부족');
      }

      print('  📱 스마트폰 감지: 모든 타블렛 조건 불충족');
      return false; // 스마트폰
    } catch (e) {
      // 감지 실패 시 스마트폰으로 기본 설정
      return false;
    }
  }

  /// 작은 스마트폰 감지 (5인치 미만)
  static bool isSmallSmartphone(BuildContext context) {
    if (!isSmartphone(context)) return false;

    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final devicePixelRatio = mediaQuery.devicePixelRatio;

    final physicalWidth = size.width * devicePixelRatio;
    final physicalHeight = size.height * devicePixelRatio;
    final physicalDiagonal = sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    final diagonalInches = physicalDiagonal / 160;

    return diagonalInches < 5.0;
  }

  /// 매우 작은 스마트폰 감지 (갤럭시 플립 등 - 세로 모드에서 화면 너비가 380dp 이하)
  /// 기존 사용자들에게 영향을 주지 않으면서 작은 기기에서만 추가 최적화 적용
  static bool isCompactSmartphone(BuildContext context) {
    if (!isSmartphone(context)) return false;

    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final orientation = mediaQuery.orientation;

    // 세로 모드에서의 화면 너비를 기준으로 판단
    final portraitWidth = orientation == Orientation.portrait
        ? size.width
        : size.height;

    // 갤럭시 플립(374dp)과 같은 매우 작은 화면 너비를 가진 기기 감지
    return portraitWidth <= 380.0;
  }

  // === 적응형 그리드 레이아웃 ===

  /// 적응형 그리드 열 수 계산 (기본값만 제공, 실제로는 설정값 사용 권장)
  /// 이 메서드는 설정값이 없을 때의 기본값만 제공합니다.
  /// 실제 앱에서는 설정 Provider의 값을 사용해야 합니다.
  static int getOptimalGridColumns(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final orientation = mediaQuery.orientation;
    final isSmartphoneDevice = isSmartphone(context);
    final isSmallDevice = isSmallSmartphone(context);
    final isCompactDevice = isCompactSmartphone(context);

    if (isSmartphoneDevice) {
      if (isCompactDevice) {
        // 매우 작은 스마트폰 (갤럭시 플립 등): 세로 4열, 가로 4열로 증가
        return 4;
      } else if (isSmallDevice) {
        // 작은 스마트폰: 세로 3열, 가로 3열
        return 3;
      } else {
        // 일반 스마트폰: 세로 3열, 가로 4열
        return orientation == Orientation.portrait ? 3 : 4;
      }
    } else {
      // 타블렛: 기본값 (실제로는 설정값 사용)
      return orientation == Orientation.portrait ? 4 : 5;
    }
  }

  /// 적응형 카드 가로세로 비율 (실제 콘텐츠 크기 기반 동적 계산)
  static double getOptimalCardAspectRatio(BuildContext context, [int? columns]) {
    // 실제 콘텐츠 크기를 측정하여 오버플로우 없는 정확한 비율 계산
    // childAspectRatio = width / height 이므로 값이 클수록 카드가 더 납작해짐
    
    try {
      final mediaQuery = MediaQuery.of(context);
      final screenWidth = mediaQuery.size.width;
      final actualColumns = columns ?? getOptimalGridColumns(context);
      
      // 1. 실제 카드 너비 계산
      final spacing = getOptimalCardSpacing(context);
      final horizontalPadding = 16.0; // 좌우 패딩
      final totalSpacing = spacing * (actualColumns - 1) + horizontalPadding;
      final cardWidth = (screenWidth - totalSpacing) / actualColumns;
      
      // 2. 콘텐츠 높이 계산
      final imageHeight = cardWidth; // 정사각형 이미지
      final textHeight = _calculateOptimalTextHeight(context, actualColumns);
      final paddingHeight = _calculateTotalPadding(context, actualColumns);
      final totalContentHeight = imageHeight + textHeight + paddingHeight;

      // 3. 안전 비율 계산 (적절한 여유 공간)
      final ratio = cardWidth / totalContentHeight; // 여유공간 완전 제거
      
      // 4. 합리적 범위로 제한 (더 낮은 최소값으로 수정)
      return ratio.clamp(0.5, 1.5); // 최소값을 0.5로 낮춤
      
    } catch (e) {
      // 계산 실패 시 안전한 기본값 반환
      return isSmartphone(context) ? 0.85 : 0.95;
    }
  }

  /// 텍스트 콘텐츠의 최적 높이 계산 (통합 텍스트 크기 사용) - 적절한 줄간격 유지하며 컴팩트하게
  static double _calculateOptimalTextHeight(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    
    // 상품명 높이 계산 - 2줄, 적절한 줄간격 유지 (통합 텍스트 크기 사용)
    final textSize = getUnifiedTextSize(context, columns);
    final nameHeight = 2 * textSize * 1.2; // 줄 간격을 1.2로 되돌림 (가독성 확보)
    
    // 가격 텍스트 높이 (수량은 배지로 이동하여 제거)
    final priceHeight = textSize * 1.2; // 줄 간격 되돌림
    
    // 텍스트 영역 간 간격 (열 수에 따라 지수적 조정) - 아래 공간만 조금 축소
    final baseTextSpacing = isSmartphoneDevice ? 1.5 : 2.0; // 1.5, 2.0 유지 (아래 공간 축소용)
    const baseColumns = 4;
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 1.5); // 지수적 감소
    final textSpacing = (baseTextSpacing * exponentialRatio).clamp(0.5, 3.0); // 범위 유지
    
    return nameHeight + priceHeight + textSpacing;
  }

  /// 카드 전체 패딩 높이 계산 - 텍스트 아래 공간을 더욱 축소
  static double _calculateTotalPadding(BuildContext context, int columns) {
    final cardPadding = getCardPadding(context, columns);
    final isSmartphoneDevice = isSmartphone(context);
    
    // 카드 내부 패딩 (상하) - 실제 UI에서는 0.2로 더 축소
    final verticalPadding = cardPadding.top + 0.2; // bottom을 0.5에서 0.2로 더 축소
    
    // 이미지와 텍스트 사이 간격 - 더욱 축소
    final imageTextSpacing = getImageTextSpacing(context, columns);
    
    // 상품명과 가격 사이 간격 - 더욱 축소 (추가 4.0 제거했으므로)
    final componentSpacing = getComponentSpacing(context, columns); // + 4.0 제거됨
    
    // 가격 Container의 패딩 (상하) - 거의 제거
    final priceQuantityPadding = getPriceQuantityPadding(context, columns);
    final priceQuantityVerticalPadding = priceQuantityPadding.top + priceQuantityPadding.bottom;
    
    // 모든 패딩과 간격 합계 - 간소화
    final dynamicPadding = verticalPadding + 
                          imageTextSpacing + 
                          componentSpacing + 
                          priceQuantityVerticalPadding; // 중복 제거
    
    // 고정 하단 여백을 거의 완전 제거 - 텍스트 아래 공간 최소화
    final fixedBottomPadding = isSmartphoneDevice ? 0.5 : 0.8; // 1.0→0.5, 1.5→0.8 더욱 축소
    
    final totalPadding = dynamicPadding + fixedBottomPadding;
    
    return totalPadding;
  }

  /// 열 수에 따른 이미지-텍스트 간격 계산 (고정 여백 고려 최적화) - 아주 살짝 띄우기
  static double getImageTextSpacing(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    
    // 기본 간격 (4열 기준) - 이미지와 가격 사이를 더 많이 띄우기
    final baseSpacing = isSmartphoneDevice ? 3.5 : 4.0; // 2.5→3.5, 3.0→4.0 (1pt 더 증가)
    const baseColumns = 4;
    
    // 열 수에 따라 지수적으로 간격 조절 (카드가 작아질수록 급격히 감소)
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 1.5); // 지수적 감소
    final spacing = (baseSpacing * exponentialRatio).clamp(0.5, 4.0); // 최소값 0.3→0.5, 최대값 3.0→4.0
    
    return spacing;
  }

  /// 적응형 카드 간격
  static double getOptimalCardSpacing(BuildContext context) {
    final isSmartphoneDevice = isSmartphone(context);
    final isSmallDevice = isSmallSmartphone(context);
    final isCompactDevice = isCompactSmartphone(context);

    if (isCompactDevice) {
      return 1.5; // 매우 작은 스마트폰: 더 좁은 간격
    } else if (isSmallDevice) {
      return 2.0;
    } else if (isSmartphoneDevice) {
      return 4.0;
    } else {
      return 6.0;
    }
  }

  // === 적응형 텍스트 크기 ===

  /// 통합 텍스트 크기 (상품명, 가격, 수량 모두 동일) - 전체적으로 크기 증가
  static double getUnifiedTextSize(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    final isCompactDevice = isCompactSmartphone(context);

    // 기준값 (4열 기준) - 매우 작은 스마트폰에서 추가 축소하여 만의자리 숫자 표시 개선
    double baseFontSize;
    if (isCompactDevice) {
      baseFontSize = 8.5; // 매우 작은 스마트폰: 더 축소하여 만의자리 숫자 표시 개선
    } else if (isSmartphoneDevice) {
      baseFontSize = 12.0; // 일반 스마트폰: 기존 크기 유지
    } else {
      baseFontSize = 18.0; // 태블릿: 기존 크기 유지
    }

    const baseColumns = 4;

    // 열 수에 따라 지수적으로 폰트 크기 조절
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.8);
    final fontSize = (baseFontSize * exponentialRatio).clamp(7.5, 24.0); // 최소값을 7.5로 낮춤

    return fontSize;
  }

  /// 상품명 텍스트 크기 (통합 함수 사용)
  static double getProductNameTextSize(BuildContext context, int columns) {
    return getUnifiedTextSize(context, columns);
  }

  /// 가격 텍스트 크기 (통합 함수 사용)
  static double getPriceTextSize(BuildContext context, int columns) {
    return getUnifiedTextSize(context, columns);
  }

  /// 수량 텍스트 크기 (통합 함수 사용)
  static double getQuantityTextSize(BuildContext context, int columns) {
    return getUnifiedTextSize(context, columns);
  }

  /// 열 수에 따른 가격/수량 컨테이너 패딩 (최소 패딩)
  static EdgeInsets getPriceQuantityPadding(BuildContext context, int columns) {
    // 기준값 (4열 기준) - 패딩 최소화
    final baseHorizontal = 4.0; // 6.0 → 4.0
    final baseVertical = 0.5;   // 2.0 → 0.5 (거의 제거)
    const baseColumns = 4;
    
    // 열 수에 따라 지수적으로 패딩 조절
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.8);
    
    final horizontal = (baseHorizontal * exponentialRatio).clamp(2.0, 8.0); // 범위 추가 축소
    final vertical = (baseVertical * exponentialRatio).clamp(0.0, 2.0);     // 최소값 0.0
    
    return EdgeInsets.symmetric(
      horizontal: horizontal,
      vertical: vertical,
    );
  }

  // === 적응형 패딩 및 마진 ===

  /// 카드 내부 패딩 (열 수에 따라 동적 조정)
  static EdgeInsets getCardPadding(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);

    // 기준값 (4열 기준)
    final basePadding = isSmartphoneDevice ? 8.0 : 12.0;
    const baseColumns = 4;

    // 열 수에 따라 지수적으로 패딩 조절
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.8);
    final padding = (basePadding * exponentialRatio).clamp(4.0, 16.0);

    return EdgeInsets.all(padding);
  }

  /// 카드 외부 마진 (열 수에 따라 동적 조정)
  static EdgeInsets getCardMargin(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    
    // 기준값 (4열 기준)  
    final baseMargin = isSmartphoneDevice ? 3.0 : 4.0;
    const baseColumns = 4;
    
    // 열 수에 따라 지수적으로 마진 조절
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.8);
    final margin = (baseMargin * exponentialRatio).clamp(1.0, 8.0);
    
    return EdgeInsets.all(margin);
  }

  /// 구성요소 간 간격 (열 수에 따라 동적 조정) - 텍스트 위치 조정을 위해 살짝 증가
  static double getComponentSpacing(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    final mediaQuery = MediaQuery.of(context);
    final orientation = mediaQuery.orientation;
    
    // 기준값 (4열 기준) - 상품명을 더욱 아래로 이동하기 위해 간격 추가 증가
    final baseSpacing = isSmartphoneDevice ? 6.5 : 7.5; // 5.0→6.5, 6.0→7.5 (1.5pt 더 증가)
    const baseColumns = 4;
    
    // 열 수에 따라 지수적으로 간격 조절
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.8);
    var spacing = (baseSpacing * exponentialRatio).clamp(1.5, 5.0); // 범위 조정: 1.0→1.5, 4.0→5.0
    
    // 세로모드에서 간격 조정 (가로모드 수준으로 맞춤) + 고정 여백 고려
    if (orientation == Orientation.portrait) {
      if (isSmartphoneDevice) {
        spacing *= 0.6; // 50%→60% (살짝 늘림)
      } else {
        spacing *= 0.7; // 60%→70% (살짝 늘림)
      }
    }
    
    return spacing;
  }

  // === 레이아웃 모드 판별 ===

  /// 스마트폰에서 세로 레이아웃을 사용해야 하는지 판별
  /// (가격과 수량을 세로로 배치)
  static bool shouldUseVerticalLayout(BuildContext context) {
    return isSmartphone(context);
  }

  /// 상품명 줄바꿈을 허용해야 하는지 판별
  static bool shouldAllowNameWrap(BuildContext context) {
    return isSmartphone(context);
  }

  // === 수량 배지 스타일링 ===

  /// 수량 배지 크기 계산 - 텍스트 크기에 맞춰 조정
  static double getQuantityBadgeSize(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    final isCompactDevice = isCompactSmartphone(context);

    // 기준 크기 (4열 기준) - 매우 작은 스마트폰에서 크기 유지하여 두자리수 표시 개선
    double baseSize;
    if (isCompactDevice) {
      baseSize = 22.0; // 매우 작은 스마트폰: 크기 증가하여 두자리수 수량 표시 개선
    } else if (isSmartphoneDevice) {
      baseSize = 20.0; // 일반 스마트폰: 기존 크기 유지
    } else {
      baseSize = 30.0; // 태블릿: 기존 크기 유지
    }

    const baseColumns = 4;

    // 열 수에 따라 크기 조정
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.6);
    final size = (baseSize * exponentialRatio).clamp(18.0, 40.0); // 최소값을 18.0으로 증가하여 두자리수 표시 개선

    return size;
  }

  /// 수량 배지 텍스트 크기 계산 - 매우 작은 디바이스에서 텍스트만 더 축소
  static double getQuantityBadgeTextSize(BuildContext context, int columns) {
    final isCompactDevice = isCompactSmartphone(context);
    final badgeSize = getQuantityBadgeSize(context, columns);

    // 매우 작은 디바이스에서는 텍스트 비율을 더 줄여서 두자리수가 잘 들어가도록 함
    final textRatio = isCompactDevice ? 0.45 : 0.55; // 매우 작은 디바이스에서 텍스트 비율 감소
    return (badgeSize * textRatio).clamp(9.0, 20.0); // 최소값을 9.0으로 낮춤
  }

  /// 수량 배지 위치 오프셋 계산 (우측 하단)
  static EdgeInsets getQuantityBadgePosition(BuildContext context, int columns) {
    final isSmartphoneDevice = isSmartphone(context);
    final isCompactDevice = isCompactSmartphone(context);

    // 기준 오프셋 - 매우 작은 스마트폰에서 추가 축소
    double baseOffset;
    if (isCompactDevice) {
      baseOffset = 4.0; // 매우 작은 스마트폰: 추가로 1pt 축소
    } else if (isSmartphoneDevice) {
      baseOffset = 5.0; // 일반 스마트폰: 기존 크기 유지
    } else {
      baseOffset = 8.0; // 태블릿: 기존 크기 유지
    }

    const baseColumns = 4;

    // 열 수에 따라 오프셋 조정
    final columnRatio = baseColumns / columns;
    final exponentialRatio = pow(columnRatio, 0.4);
    final offset = (baseOffset * exponentialRatio).clamp(3.0, 12.0); // 최소값을 3.0으로 낮춤

    return EdgeInsets.only(right: offset, bottom: offset);
  }

  /// 수량 배지 라운드 사각형 borderRadius 계산
  static double getQuantityBadgeBorderRadius(BuildContext context, int columns) {
    final badgeSize = getQuantityBadgeSize(context, columns);
    return badgeSize * 0.25; // 라운드 사각형 (25% 곡률)
  }

  /// 최대 상품명 줄 수
  static int getMaxNameLines(BuildContext context) {
    final isSmartphoneDevice = isSmartphone(context);
    return isSmartphoneDevice ? 2 : 1;
  }

  /// 고정 카드 크기 계산 (Wrap용)
  /// 설정된 열 수를 기반으로 카드의 고정 크기를 계산합니다.
  static Size getFixedCardSize(BuildContext context, int columns, {double? availableWidth}) {
    final screenWidth = availableWidth ?? MediaQuery.of(context).size.width;
    final spacing = getOptimalCardSpacing(context);
    final padding = 4.0; // POS 화면의 실제 패딩 값 (EdgeInsets.all(4))

    // 사용 가능한 너비에서 패딩과 간격을 제외한 실제 카드 영역 계산
    final totalSpacing = spacing * (columns - 1); // 카드 간 간격
    final totalPadding = padding * 2; // 좌우 패딩
    final availableCardWidth = screenWidth - totalSpacing - totalPadding;

    // 범용적 동적 안전 여백 계산
    final cardWidthBeforeSafety = availableCardWidth / columns;

    // 1. 백분율 기반 안전 여백 (모든 화면 크기에 적응)
    final basePercentage = 0.025; // 기본 2.5%
    final columnAdjustment = columns * 0.001; // 열 수당 0.1% 추가
    final safetyPercentage = basePercentage + columnAdjustment;

    // 2. 백분율 기반 안전 여백 계산
    final percentageBasedSafety = cardWidthBeforeSafety * safetyPercentage;

    // 3. 최소 안전 여백 보장 (픽셀 밀도 고려)
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final minimumSafety = 2.0 * pixelRatio;

    // 4. 최종 안전 여백 (최소값 보장, 최대 카드 너비의 5% 제한)
    final maxSafety = cardWidthBeforeSafety * 0.05; // 최대 5%
    final safetyMargin = max(minimumSafety, min(maxSafety, percentageBasedSafety));

    // 5. 최종 카드 너비
    final cardWidth = cardWidthBeforeSafety - safetyMargin;





    // 높이는 aspectRatio를 기반으로 계산
    final aspectRatio = getOptimalCardAspectRatio(context, columns);
    final cardHeight = cardWidth / aspectRatio;

    return Size(cardWidth, cardHeight);
  }

  /// 고정 카드 크기 계산 (GridView.extent용) - 하위 호환성
  static double getFixedCardExtent(BuildContext context, int columns, {double? availableWidth}) {
    final cardSize = getFixedCardSize(context, columns, availableWidth: availableWidth);
    return cardSize.width;
  }
}
