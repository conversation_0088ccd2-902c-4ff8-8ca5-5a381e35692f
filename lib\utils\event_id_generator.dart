/// 행사 고유 ID 생성 유틸
/// microsecondsSinceEpoch 기반 + 같은 마이크로초 내 충돌 방지 카운터
/// 전역적으로 유니크한 ID를 보장하여 사용자 간 행사 ID 중복을 방지
class EventIdGenerator {
  static int _lastTimestamp = 0;
  static int _collisionCounter = 0;

  /// 글로벌 유니크 행사 ID 생성
  /// 
  /// 반환값: 64비트 정수 ID (상위 52비트: 타임스탬프, 하위 12비트: 충돌 방지 카운터)
  static int generate() {
    final now = DateTime.now().microsecondsSinceEpoch;
    if (now == _lastTimestamp) {
      _collisionCounter++;
    } else {
      _collisionCounter = 0;
      _lastTimestamp = now;
    }
    // 상위 비트: timestamp, 하위 12비트: collision counter (4096개)
    return (now << 12) | (_collisionCounter & 0xFFF);
  }

  /// 기존 AUTOINCREMENT ID를 새로운 유니크 ID로 마이그레이션할 때 사용
  /// 
  /// [oldId] 기존 AUTOINCREMENT ID
  /// [userId] 사용자 ID (추가 유니크성 보장)
  /// 반환값: 새로운 글로벌 유니크 ID
  static int generateForMigration(int oldId, String userId) {
    // 사용자 ID의 해시코드와 기존 ID를 조합하여 유니크성 보장
    final userHash = userId.hashCode.abs();
    final timestamp = DateTime.now().microsecondsSinceEpoch;
    
    // 타임스탬프 + 사용자 해시 + 기존 ID 조합
    return (timestamp << 16) | ((userHash & 0xFF) << 8) | (oldId & 0xFF);
  }
}
