import '../models/event.dart';
import '../models/event_workspace.dart';

/// Event와 EventWorkspace 간의 변환 유틸리티
///
/// 기존 Event 시스템에서 새로운 EventWorkspace 시스템으로의
/// 마이그레이션을 위한 변환 메서드들을 제공합니다.
class EventWorkspaceUtils {
  /// Event를 EventWorkspace로 변환
  static EventWorkspace? eventToWorkspace(Event? event) {
    if (event == null || event.id == null) return null;
    
    return EventWorkspace.fromEvent(event);
  }

  /// EventWorkspace를 Event로 변환
  static Event? workspaceToEvent(EventWorkspace? workspace) {
    if (workspace == null) return null;
    
    return workspace.toEvent();
  }

  /// Event ID를 받아서 EventWorkspace ID로 변환 (동일한 값)
  static int? eventIdToWorkspaceId(int? eventId) {
    return eventId;
  }

  /// EventWorkspace ID를 받아서 Event ID로 변환 (동일한 값)
  static int? workspaceIdToEventId(int? workspaceId) {
    return workspaceId;
  }

  /// Event 이름을 받아서 EventWorkspace 이름으로 변환 (동일한 값)
  static String eventNameToWorkspaceName(String? eventName) {
    return eventName ?? '행사 워크스페이스 없음';
  }

  /// EventWorkspace 이름을 받아서 Event 이름으로 변환 (동일한 값)
  static String workspaceNameToEventName(String? workspaceName) {
    return workspaceName ?? '행사 없음';
  }
}
