import 'dart:developer' as developer;
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

// ============================================================================
// 통합 로깅 시스템
// 
// 이 파일은 다음 5개 파일을 통합한 것입니다:
// - log_config.dart -> LogLevel, LogCategory, LogConfig 클래스
// - log_entry.dart -> LogEntry, LogStats 클래스
// - log_file_manager.dart -> LogFileManager 클래스
// - log_performance.dart -> LogPerformance 클래스
// - logger_utils.dart -> LoggerUtils 클래스
//
// 모든 기존 API는 100% 호환성을 유지합니다.
// ============================================================================

/// 로그 레벨 정의
enum LogLevel { debug, info, warning, error, critical }

/// 로그 카테고리 정의
enum LogCategory {
  general, // 일반 로그
  provider, // Provider 관련 로그
  database, // 데이터베이스 관련 로그
  network, // 네트워크 관련 로그
  performance, // 성능 관련 로그
  userAction, // 사용자 행동 로그
  business, // 비즈니스 로직 로그
  security, // 보안 관련 로그
  error, // 오류 관련 로그
}

/// 로그 설정을 관리하는 클래스
class LogConfig {
  final bool enableConsoleLogging;
  final bool enableFileLogging;
  final bool enableRemoteLogging;
  final LogLevel minimumLogLevel;
  final Set<LogCategory> enabledCategories;
  final String logFilePath;
  final int maxLogFileSize;
  final int maxLogFiles;

  const LogConfig({
    this.enableConsoleLogging = true,
    this.enableFileLogging = false,
    this.enableRemoteLogging = false,
    this.minimumLogLevel = LogLevel.debug,
    this.enabledCategories = const {LogCategory.general},
    this.logFilePath = 'logs/app.log',
    this.maxLogFileSize = 10 * 1024 * 1024, // 10MB
    this.maxLogFiles = 5,
  });

  /// 로그 출력 여부 확인
  bool shouldLog(LogLevel level, LogCategory category) {
    return level.index >= minimumLogLevel.index &&
        enabledCategories.contains(category);
  }

  /// 설정 복사본 생성
  LogConfig copyWith({
    bool? enableConsoleLogging,
    bool? enableFileLogging,
    bool? enableRemoteLogging,
    LogLevel? minimumLogLevel,
    Set<LogCategory>? enabledCategories,
    String? logFilePath,
    int? maxLogFileSize,
    int? maxLogFiles,
  }) {
    return LogConfig(
      enableConsoleLogging: enableConsoleLogging ?? this.enableConsoleLogging,
      enableFileLogging: enableFileLogging ?? this.enableFileLogging,
      enableRemoteLogging: enableRemoteLogging ?? this.enableRemoteLogging,
      minimumLogLevel: minimumLogLevel ?? this.minimumLogLevel,
      enabledCategories: enabledCategories ?? this.enabledCategories,
      logFilePath: logFilePath ?? this.logFilePath,
      maxLogFileSize: maxLogFileSize ?? this.maxLogFileSize,
      maxLogFiles: maxLogFiles ?? this.maxLogFiles,
    );
  }

  /// 설정 정보를 Map으로 변환
  Map<String, dynamic> toJson() {
    return {
      'enableConsoleLogging': enableConsoleLogging,
      'enableFileLogging': enableFileLogging,
      'enableRemoteLogging': enableRemoteLogging,
      'minimumLogLevel': minimumLogLevel.toString().split('.').last,
      'enabledCategories': enabledCategories
          .map((c) => c.toString().split('.').last)
          .toList(),
      'logFilePath': logFilePath,
      'maxLogFileSize': maxLogFileSize,
      'maxLogFiles': maxLogFiles,
    };
  }
}

/// 로그 엔트리를 나타내는 클래스
class LogEntry {
  final DateTime timestamp;
  final LogLevel level;
  final LogCategory category;
  final String tag;
  final String message;
  final Object? error;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? data;

  LogEntry({
    required this.level,
    required this.category,
    required this.tag,
    required this.message,
    this.error,
    this.stackTrace,
    this.data,
  }) : timestamp = DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'level': level.toString().split('.').last.toUpperCase(),
      'category': category.toString().split('.').last,
      'tag': tag,
      'message': message,
      if (error != null) 'error': error.toString(),
      if (stackTrace != null) 'stackTrace': stackTrace.toString(),
      if (data != null) 'data': data,
    };
  }

  String toFormattedString() {
    final buffer = StringBuffer();
    buffer.write('[${timestamp.toIso8601String()}] ');
    buffer.write('[${level.toString().split('.').last.toUpperCase()}] ');
    buffer.write('[${category.toString().split('.').last}] ');
    buffer.write('[$tag] ');
    buffer.write(message);

    if (data != null && data!.isNotEmpty) {
      buffer.write(' | Data: ${jsonEncode(data)}');
    }

    if (error != null) {
      buffer.write('\nError: $error');
    }

    if (stackTrace != null) {
      buffer.write('\nStack Trace:\n$stackTrace');
    }

    return buffer.toString();
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

/// 로그 통계를 관리하는 클래스
class LogStats {
  final Map<LogLevel, int> levelCounts;
  final Map<LogCategory, int> categoryCounts;
  final Map<String, int> errorCounts;
  final List<String> recentErrors;
  final DateTime startTime;

  LogStats({
    Map<LogLevel, int>? levelCounts,
    Map<LogCategory, int>? categoryCounts,
    Map<String, int>? errorCounts,
    List<String>? recentErrors,
    DateTime? startTime,
  }) : levelCounts = levelCounts ?? {},
       categoryCounts = categoryCounts ?? {},
       errorCounts = errorCounts ?? {},
       recentErrors = recentErrors ?? [],
       startTime = startTime ?? DateTime.now();

  void incrementLevel(LogLevel level) {
    levelCounts[level] = (levelCounts[level] ?? 0) + 1;
  }

  void incrementCategory(LogCategory category) {
    categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
  }

  void incrementError(String errorType) {
    errorCounts[errorType] = (errorCounts[errorType] ?? 0) + 1;
    recentErrors.insert(0, '$errorType (${DateTime.now().toIso8601String()})');

    // 최근 에러는 최대 50개까지만 유지
    if (recentErrors.length > 50) {
      recentErrors.removeRange(50, recentErrors.length);
    }
  }

  void clear() {
    levelCounts.clear();
    categoryCounts.clear();
    errorCounts.clear();
    recentErrors.clear();
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime.toIso8601String(),
      'duration': DateTime.now().difference(startTime).inMilliseconds,
      'levelCounts': levelCounts.map(
        (k, v) => MapEntry(k.toString().split('.').last, v),
      ),
      'categoryCounts': categoryCounts.map(
        (k, v) => MapEntry(k.toString().split('.').last, v),
      ),
      'errorCounts': errorCounts,
      'recentErrors': recentErrors,
      'totalLogs': levelCounts.values.fold(0, (sum, count) => sum + count),
    };
  }
}

/// 파일 로깅을 관리하는 클래스
class LogFileManager {
  static String? _logDirectory;
  static bool _isInitialized = false;

  /// 파일 로깅 초기화
  static Future<void> initialize(LogConfig config) async {
    if (_isInitialized) return;

    if (config.enableFileLogging) {
      try {
        final directory = await getApplicationDocumentsDirectory();
        _logDirectory = '${directory.path}/logs';
        final logDir = Directory(_logDirectory!);
        if (!await logDir.exists()) {
          await logDir.create(recursive: true);
        }
        _isInitialized = true;
      } catch (e) {
        // 순환 의존성 해결: 직접 콘솔 출력
        print('LogFileManager: 로그 디렉토리 생성 실패: $e');
      }
    }
  }

  /// 파일에 로그 쓰기
  static Future<void> writeToFile(LogEntry entry, LogConfig config) async {
    if (!config.enableFileLogging || _logDirectory == null) return;

    try {
      final logFile = File('$_logDirectory/${config.logFilePath}');
      final logContent = '${entry.toFormattedString()}\n';

      // 파일 크기 확인 및 로테이션
      if (await logFile.exists()) {
        final fileSize = await logFile.length();
        if (fileSize > config.maxLogFileSize) {
          await _rotateLogFiles(config);
        }
      }

      await logFile.writeAsString(logContent, mode: FileMode.append);
    } catch (e) {
      // 순환 의존성 해결: 직접 콘솔 출력
      print('LogFileManager: 파일 로깅 실패: $e');
    }
  }

  /// 로그 파일 로테이션
  static Future<void> _rotateLogFiles(LogConfig config) async {
    if (_logDirectory == null) return;

    try {
      final baseFileName = config.logFilePath.split('.').first;
      final extension = config.logFilePath.split('.').last;

      // 기존 파일들을 번호 순으로 이동
      for (int i = config.maxLogFiles - 1; i > 0; i--) {
        final oldFile = File('$_logDirectory/$baseFileName.$i.$extension');
        final newFile = File(
          '$_logDirectory/$baseFileName.${i + 1}.$extension',
        );

        if (await oldFile.exists()) {
          if (i == config.maxLogFiles - 1) {
            await oldFile.delete(); // 가장 오래된 파일 삭제
          } else {
            await oldFile.rename(newFile.path);
          }
        }
      }

      // 현재 파일을 .1로 이동
      final currentFile = File('$_logDirectory/${config.logFilePath}');
      if (await currentFile.exists()) {
        final rotatedFile = File('$_logDirectory/$baseFileName.1.$extension');
        await currentFile.rename(rotatedFile.path);
      }
    } catch (e) {
      print('LogFileManager: 로그 파일 로테이션 실패: $e');
    }
  }

  /// 모든 로그 파일 삭제
  static Future<bool> clearLogFiles() async {
    if (_logDirectory == null) return false;

    try {
      final logDir = Directory(_logDirectory!);
      if (await logDir.exists()) {
        await for (final entity in logDir.list()) {
          if (entity is File && entity.path.contains('.log')) {
            await entity.delete();
          }
        }
        return true;
      }
    } catch (e) {
      print('LogFileManager: 로그 파일 삭제 실패: $e');
    }

    return false;
  }

  /// 로그 파일 목록 조회
  static Future<List<String>> getLogFiles() async {
    if (_logDirectory == null) return [];

    try {
      final logDir = Directory(_logDirectory!);
      if (await logDir.exists()) {
        final files = <String>[];
        await for (final entity in logDir.list()) {
          if (entity is File && entity.path.contains('.log')) {
            files.add(entity.path);
          }
        }
        return files;
      }
    } catch (e) {
      print('LogFileManager: 로그 파일 목록 조회 실패: $e');
    }

    return [];
  }

  /// 로그 디렉토리 경로 반환
  static String? get logDirectory => _logDirectory;

  /// 초기화 상태 반환
  static bool get isInitialized => _isInitialized;
}

/// 성능 모니터링을 관리하는 클래스
class LogPerformance {
  static final Map<String, Stopwatch> _methodTimers = {};
  static final Map<String, int> _methodCallCounts = {};
  static final Map<String, List<int>> _performanceHistory = {};

  /// 메서드 시작 로그 및 성능 모니터링 시작
  static Future<void> methodStart(
    String methodName, {
    String tag = 'BaraBoothManager',
    Map<String, dynamic>? data,
  }) async {
    final key = '$tag/$methodName';
    _methodTimers[key] = Stopwatch()..start();
    _methodCallCounts[key] = (_methodCallCounts[key] ?? 0) + 1;

    await _logEntry(
      LogEntry(
        level: LogLevel.debug,
        category: LogCategory.performance,
        tag: tag,
        message: '▶ $methodName',
        data: {
          'method': methodName,
          'action': 'start',
          'callCount': _methodCallCounts[key],
          ...?data,
        },
      ),
    );
  }

  /// 메서드 종료 로그 및 성능 측정 결과 기록
  static Future<void> methodEnd(
    String methodName, {
    String tag = 'BaraBoothManager',
    Map<String, dynamic>? data,
  }) async {
    final key = '$tag/$methodName';
    final stopwatch = _methodTimers[key];
    if (stopwatch != null) {
      stopwatch.stop();
      final elapsedMs = stopwatch.elapsedMilliseconds;
      _methodTimers.remove(key);

      // 성능 히스토리에 추가
      addPerformanceData(methodName, elapsedMs);

      await _logEntry(
        LogEntry(
          level: LogLevel.debug,
          category: LogCategory.performance,
          tag: tag,
          message: '◀ $methodName',
          data: {
            'method': methodName,
            'action': 'end',
            'elapsedMs': elapsedMs,
            'callCount': _methodCallCounts[key],
            ...?data,
          },
        ),
      );
    } else {
      await _logEntry(
        LogEntry(
          level: LogLevel.debug,
          category: LogCategory.performance,
          tag: tag,
          message: '◀ $methodName (타이머 없음)',
          data: data,
        ),
      );
    }
  }

  /// 성능 메트릭 기록
  static Future<void> logPerformance(
    String operation,
    int elapsedMs, {
    String tag = 'BaraBoothManager',
    Map<String, dynamic>? metrics,
  }) async {
    // 성능 히스토리에 추가
    addPerformanceData(operation, elapsedMs);

    await _logEntry(
      LogEntry(
        level: LogLevel.info,
        category: LogCategory.performance,
        tag: tag,
        message: '성능 측정: $operation',
        data: {
          'operation': operation,
          'elapsedMs': elapsedMs,
          'type': 'performance',
          ...?metrics,
        },
      ),
    );
  }

  /// 성능 히스토리 추가
  static void addPerformanceData(String operation, int elapsedMs) {
    _performanceHistory.putIfAbsent(operation, () => []);
    _performanceHistory[operation]!.add(elapsedMs);

    // 히스토리는 최대 100개까지만 유지
    if (_performanceHistory[operation]!.length > 100) {
      _performanceHistory[operation]!.removeAt(0);
    }
  }

  /// 성능 통계 조회
  static Map<String, Map<String, double>> getPerformanceStats() {
    final stats = <String, Map<String, double>>{};

    for (final entry in _performanceHistory.entries) {
      final values = entry.value;
      if (values.isNotEmpty) {
        values.sort();
        final avg = values.fold(0, (sum, value) => sum + value) / values.length;
        final median = values.length % 2 == 0
            ? (values[values.length ~/ 2 - 1] + values[values.length ~/ 2]) / 2
            : values[values.length ~/ 2].toDouble();
        final min = values.first.toDouble();
        final max = values.last.toDouble();

        stats[entry.key] = {
          'average': avg,
          'median': median,
          'min': min,
          'max': max,
          'count': values.length.toDouble(),
        };
      }
    }

    return stats;
  }

  /// 성능 데이터 초기화
  static void clearPerformanceData() {
    _methodTimers.clear();
    _methodCallCounts.clear();
    _performanceHistory.clear();
  }

  /// 메서드 호출 횟수 조회
  static Map<String, int> getMethodCallCounts() {
    return Map.from(_methodCallCounts);
  }

  /// 활성 타이머 조회
  static Map<String, Stopwatch> getActiveTimers() {
    return Map.from(_methodTimers);
  }

  // 로그 엔트리 메서드 (순환 의존성 해결)
  static Future<void> _logEntry(LogEntry entry) async {
    // LoggerUtils 메서드를 직접 호출 (같은 파일 내부이므로 순환 의존성 없음)
    LoggerUtils._handleLogEntry(entry);
  }
}

/// 경량화된 로깅 시스템
/// 
/// 성능 최적화를 위해 환경별 로깅 레벨 조정:
/// - 릴리즈 모드: ERROR 레벨만 기록
/// - 디버그 모드: 모든 레벨 기록
/// - 파일 로깅: 중요한 로그만 기록
class LoggerUtils {
  static const String _tag = 'LoggerUtils';
  
  // 경량화: 환경별 로깅 레벨 제한
  static bool get _isProductionMode => kReleaseMode;
  static bool get _shouldLogDebug => kDebugMode;
  static bool get _shouldLogInfo => kDebugMode || kProfileMode;
  static bool get _shouldLogWarn => true; // 항상 기록
  static bool get _shouldLogError => true; // 항상 기록

  /// 디버그 로그 (개발 환경에서만)
  static void logDebug(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    if (!_shouldLogDebug) return;
    
    _logMessage(
      LogLevel.debug,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 정보 로그 (개발/프로파일 환경에서만)
  static void logInfo(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    if (!_shouldLogInfo) return;
    
    _logMessage(
      LogLevel.info,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 경고 로그 (항상 기록)
  static void logWarning(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    if (!_shouldLogWarn) return;
    
    _logMessage(
      LogLevel.warning,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 오류 로그 (항상 기록, 파일에도 저장)
  static void logError(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    if (!_shouldLogError) return;
    
    _logMessage(
      LogLevel.error,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
      saveToFile: true, // 오류는 파일에도 저장
    );
  }

  /// Critical 로그 (항상 기록, 파일에도 저장)
  static Future<void> logCritical(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) async {
    if (!_shouldLogError) return;
    
    _logMessage(
      LogLevel.critical,
      message,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
      saveToFile: true,
    );
  }

  /// 기존 호환성을 위한 error 메서드 (logError와 동일)
  static void error(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    logError(message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// 성능 로그 (디버그 모드에서만, 최적화됨)
  static void logPerformance(
    String operation,
    Duration duration, {
    String? tag,
    Map<String, dynamic>? metrics,
  }) {
    if (!_shouldLogDebug) return;

    final message = StringBuffer('🚀 성능: $operation (${duration.inMilliseconds}ms)');
    
    if (metrics != null && metrics.isNotEmpty) {
      message.write(' - ');
      message.write(metrics.entries
          .map((e) => '${e.key}: ${e.value}')
          .join(', '));
    }

    _logMessage(
      LogLevel.info, // performance 대신 info 사용
      message.toString(),
      tag: tag ?? 'Performance',
    );
  }

  /// 기존 호환성을 위한 methodStart 메서드 (경량화)
  static void methodStart(
    String methodName, {
    String? tag,
    Map<String, dynamic>? data,
  }) {
    if (!_shouldLogDebug) return;
    
    logDebug('메서드 시작: $methodName', tag: tag);
  }

  /// 기존 호환성을 위한 methodEnd 메서드 (경량화)
  static void methodEnd(
    String methodName, {
    String? tag,
    Map<String, dynamic>? data,
  }) {
    if (!_shouldLogDebug) return;
    
    logDebug('메서드 종료: $methodName', tag: tag);
  }

  /// 사용자 행동 로깅 (기존 호환성)
  static Future<void> logUserAction(
    String action,
    String screen, {
    String? tag,
    Map<String, dynamic>? actionData,
  }) async {
    if (!_shouldLogInfo) return;
    
    logInfo('사용자 행동: $action (화면: $screen)', tag: tag ?? 'UserAction');
  }

  /// 비즈니스 이벤트 로깅 (기존 호환성)
  static Future<void> logBusinessEvent(
    String event,
    String category, {
    String? tag,
    Map<String, dynamic>? eventData,
  }) async {
    if (!_shouldLogInfo) return;
    
    logInfo('비즈니스 이벤트: $event (카테고리: $category)', tag: tag ?? 'BusinessEvent');
  }

  /// 시스템 정보 로깅 (기존 호환성)
  static Future<void> logSystemInfo() async {
    if (!_shouldLogInfo) return;
    
    final systemInfo = StringBuffer('시스템 정보: ');
    systemInfo.write('플랫폼=${Platform.operatingSystem}, ');
    systemInfo.write('버전=${Platform.operatingSystemVersion}, ');
    systemInfo.write('CPU=${Platform.numberOfProcessors}개');
    
    logInfo(systemInfo.toString(), tag: 'SystemInfo');
  }

  /// 내부 로깅 메서드 (경량화)
  static void _logMessage(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
    bool saveToFile = false,
  }) {
    final effectiveTag = tag ?? _tag;
    final timestamp = DateTime.now();
    
    // 콘솔 출력 (환경별 최적화)
    if (_shouldOutputToConsole(level)) {
      _outputToConsole(level, message, effectiveTag, timestamp, error, stackTrace);
    }

    // 파일 저장 (중요한 로그만)
    if (saveToFile || _shouldSaveToFile(level)) {
      _saveToFileAsync(level, message, effectiveTag, timestamp, error, stackTrace);
    }
  }

  /// LogPerformance에서 사용하는 내부 메서드 (순환 의존성 해결)
  static void _handleLogEntry(LogEntry entry) {
    _logMessage(
      entry.level,
      entry.message,
      tag: entry.tag,
      error: entry.error,
      stackTrace: entry.stackTrace,
    );
  }

  /// 콘솔 출력 여부 결정
  static bool _shouldOutputToConsole(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return _shouldLogDebug;
      case LogLevel.info:
        return _shouldLogInfo;
      case LogLevel.warning:
        return _shouldLogWarn;
      case LogLevel.error:
      case LogLevel.critical: // critical 케이스 추가
        return _shouldLogError;
    }
  }

  /// 파일 저장 여부 결정 (경량화)
  static bool _shouldSaveToFile(LogLevel level) {
    if (_isProductionMode) {
      return level == LogLevel.error || level == LogLevel.critical; // 프로덕션에서는 오류만
    }
    return level == LogLevel.warning || level == LogLevel.error || level == LogLevel.critical; // 개발에서는 경고 이상
  }

  /// 콘솔 출력 (기존 유지)
  static void _outputToConsole(
    LogLevel level,
    String message,
    String tag,
    DateTime timestamp,
    Object? error,
    StackTrace? stackTrace,
  ) {
    final timeStr = '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}:'
        '${timestamp.second.toString().padLeft(2, '0')}';

    final prefix = _getLogPrefix(level);
    final formattedMessage = '$prefix [$timeStr] [$tag] $message';

    // Flutter 환경에서 developer.log 사용, 그 외에는 print 사용
    try {
      developer.log(
        formattedMessage,
        time: timestamp,
        level: _getLogLevelValue(level),
        name: tag,
        error: error,
        stackTrace: stackTrace,
      );
    } catch (_) {
      // developer.log 실패 시 fallback
      print(formattedMessage);
      if (error != null) print('Error: $error');
      if (stackTrace != null) print('StackTrace: $stackTrace');
    }
  }

  /// 비동기 파일 저장 (성능 최적화)
  static void _saveToFileAsync(
    LogLevel level,
    String message,
    String tag,
    DateTime timestamp,
    Object? error,
    StackTrace? stackTrace,
  ) {
    // 비동기로 파일 저장하여 메인 스레드 블로킹 방지
    Future.microtask(() async {
      try {
        final logEntry = LogEntry(
          level: level,
          category: LogCategory.general, // 기본 카테고리 추가
          message: message,
          tag: tag,
          error: error,
          stackTrace: stackTrace,
        );

        // 순환 의존성 해결: 직접 호출
        await LogFileManager.writeToFile(logEntry, LogConfig());
      } catch (e) {
        // 파일 저장 실패는 무시 (성능 우선)
        if (_shouldLogDebug) {
          print('로그 파일 저장 실패: $e');
        }
      }
    });
  }

  /// 로그 레벨 접두사
  static String _getLogPrefix(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛 DEBUG';
      case LogLevel.info:
        return 'ℹ️ INFO';
      case LogLevel.warning:
        return '⚠️ WARN';
      case LogLevel.error:
        return '❌ ERROR';
      case LogLevel.critical: // critical 케이스 추가
        return '🚨 CRITICAL';
    }
  }

  /// developer.log 레벨 값
  static int _getLogLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.critical: // critical 케이스 추가
        return 1200;
    }
  }

  /// 로그 설정 초기화 (경량화) - 기존 호환성 유지
  static Future<void> initialize([LogConfig? config]) async {
    try {
      // LogFileManager 초기화
      if (config != null) {
        await LogFileManager.initialize(config);
      }
      
      if (_shouldLogInfo) {
        logInfo('로깅 시스템 초기화 완료 (통합 모드)', tag: _tag);
      }
    } catch (e) {
      print('로깅 시스템 초기화 실패: $e');
    }
  }

  /// 로그 파일 정리 (경량화)
  static Future<void> cleanup() async {
    try {
      await LogFileManager.clearLogFiles();
      
      if (_shouldLogInfo) {
        logInfo('로그 파일 정리 완료', tag: _tag);
      }
    } catch (e) {
      if (_shouldLogDebug) {
        print('로그 파일 정리 실패: $e');
      }
    }
  }

  /// 성능 측정 헬퍼 (최적화)
  static Future<T> measurePerformance<T>(
    String operation,
    Future<T> Function() action, {
    String? tag,
    Map<String, dynamic>? additionalMetrics,
  }) async {
    if (!_shouldLogDebug) {
      // 성능 로깅이 비활성화된 경우 측정 없이 바로 실행
      return await action();
    }

    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await action();
      stopwatch.stop();
      
      final metrics = <String, dynamic>{
        'success': true,
        ...?additionalMetrics,
      };
      
      logPerformance(operation, stopwatch.elapsed, tag: tag, metrics: metrics);
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      final metrics = <String, dynamic>{
        'success': false,
        'error': e.toString(),
        ...?additionalMetrics,
      };
      
      logPerformance(operation, stopwatch.elapsed, tag: tag, metrics: metrics);
      logError('성능 측정 중 오류 발생: $operation', tag: tag, error: e);
      
      rethrow;
    }
  }

  /// 메모리 누수 방지를 위한 정리 (앱 종료 시 호출)
  static void shutdown() {
    // LogPerformance 정리
    LogPerformance.clearPerformanceData();
    
    logInfo('LoggerUtils 정리 완료', tag: _tag);
  }
}
