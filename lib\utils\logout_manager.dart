/// 개선된 로그아웃 매니저 - Phoenix를 사용한 완전한 앱 재시작
///
/// 기존 로그아웃의 문제점들을 해결하기 위해 새로 설계된 로그아웃 시스템
/// - Widget dispose 후 ref 사용 문제 해결
/// - 앱 종료 대신 로그인 페이지로 자연스러운 이동
/// - 완전한 앱 초기화 보장

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/logger_utils.dart';
import '../services/database_service.dart';


class LogoutManager {
  static const String _tag = 'LogoutManager';

  /// 완전한 로그아웃 수행 - Phoenix를 사용한 앱 재시작
  ///
  /// 개선된 로그아웃 시스템:
  /// 1. Widget dispose 후 ref 사용 문제 해결
  /// 2. 앱 종료 대신 로그인 페이지로 자연스러운 이동
  /// 3. 모든 상태와 메모리 완전 초기화
  /// 4. Phoenix.rebirth()를 사용한 안전한 앱 재시작
  static Future<void> performCompleteLogout({
    required BuildContext context,
  }) async {
    try {
      LoggerUtils.logInfo('🔄 개선된 로그아웃 시작', tag: _tag);

      // 1. 백그라운드에서 데이터 정리
      await _performDataCleanup(context);

      // 2. Firebase 로그아웃 (서버 데이터는 유지)
      await FirebaseAuth.instance.signOut();
      LoggerUtils.logInfo('✅ Firebase 로그아웃 완료 (서버 닉네임 데이터 유지)', tag: _tag);

      // 3. SharedPreferences 완전 초기화
      await _resetSharedPreferencesForLogout();

      // 4. 잠시 대기 (정리 작업 완료 보장)
      await Future.delayed(const Duration(milliseconds: 500));

      // 5. Phoenix를 사용한 앱 재시작 (로그인 페이지로 이동)
      LoggerUtils.logInfo('🔄 Phoenix를 사용한 앱 재시작', tag: _tag);
      Phoenix.rebirth(context);

    } catch (e) {
      LoggerUtils.logError('❌ 로그아웃 중 오류 발생', tag: _tag, error: e);

      // 오류 발생 시에도 앱 재시작
      try {
        LoggerUtils.logInfo('🔄 오류 발생 - Phoenix 재시작 시도', tag: _tag);
        Phoenix.rebirth(context);
      } catch (e2) {
        LoggerUtils.logError('❌ Phoenix 재시작도 실패', tag: _tag, error: e2);
      }
    }
  }



  /// 데이터 정리 작업 (Provider 의존성 제거)
  static Future<void> _performDataCleanup(BuildContext context) async {
    try {
      LoggerUtils.logInfo('🗑️ 데이터 정리 시작', tag: _tag);

      // 1. 로컬 데이터 정리
      await _cleanupLocalDataSilently(context);
      LoggerUtils.logInfo('✅ 로컬 데이터 정리 완료', tag: _tag);

      // 2. 메모리 정리
      await _performMemoryCleanup();
      LoggerUtils.logInfo('✅ 메모리 정리 완료', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('❌ 데이터 정리 중 오류', tag: _tag, error: e);
      // 오류가 있어도 계속 진행
    }
  }



  /// 메모리 정리 (iOS 최적화)
  static Future<void> _performMemoryCleanup() async {
    try {
      LoggerUtils.logInfo('메모리 정리 시작', tag: _tag);

      // 가비지 컬렉션 유도 (iOS에서 중요)
      for (int i = 0; i < 3; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        // 메모리 정리를 위한 대기
      }

      LoggerUtils.logInfo('메모리 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('메모리 정리 실패', tag: _tag, error: e);
    }
  }

  /// 조용한 로컬 데이터 정리 (provider 상태 변경 없이)
  static Future<void> _cleanupLocalDataSilently(BuildContext context) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 중요한 상태들 백업 (iOS에서 더 안전한 처리)
      final isOnboarded = prefs.getBool('isOnboarded') ?? false;
      final allKeys = prefs.getKeys().toList();

      LoggerUtils.logInfo('SharedPreferences 정리 시작 - 총 ${allKeys.length}개 키', tag: _tag);

      // 모든 데이터 삭제 (iOS에서 더 확실한 정리)
      for (final key in allKeys) {
        try {
          await prefs.remove(key);
        } catch (e) {
          LoggerUtils.logWarning('키 삭제 실패: $key', tag: _tag, error: e);
        }
      }

      // 추가 확인을 위한 clear() 호출
      await prefs.clear();

      // 필요한 상태만 복원 (로그아웃 시에는 온보딩을 다시 보여주지 않음)
      await prefs.setBool('isOnboarded', isOnboarded);
      await prefs.setString('auth_mode', 'login');

      // iOS에서 상태 불일치 방지를 위한 추가 플래그
      await prefs.setBool('logout_completed', true);

      LoggerUtils.logInfo('SharedPreferences 완전 정리 완료 (온보딩 상태 유지: $isOnboarded)', tag: _tag);

      // SQLite 데이터베이스 정리
      await _cleanupDatabaseSilently(context);

      // 캐시 정리
      await _cleanupCachesSilently();

    } catch (e) {
      LoggerUtils.logError('조용한 데이터 정리 실패', tag: _tag, error: e);
    }
  }

  /// 데이터베이스 조용히 정리
  static Future<void> _cleanupDatabaseSilently(BuildContext context) async {
    try {
      LoggerUtils.logInfo('SQLite 데이터베이스 정리 중...', tag: _tag);

      // 0) 먼저 DB 연결을 정식으로 닫는다 (열린 핸들로 인한 경고 예방)
      try {
        final container = ProviderScope.containerOf(context, listen: false);
        final dbService = container.read(databaseServiceProvider);
        await dbService.close();
        LoggerUtils.logInfo('DatabaseService.close() 호출 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logWarning('DatabaseService close 실패(무시): $e', tag: _tag);
      }

      // 1) sqflite API로 안전 삭제 시도 (플랫폼별 적합한 방식)
      final databasesPath = await getDatabasesPath();
      final dbPath = '$databasesPath/parabara_database.db';

      try {
        await databaseFactory.deleteDatabase(dbPath);
        LoggerUtils.logInfo('SQLite 데이터베이스 안전 삭제 완료: $dbPath', tag: _tag);
      } catch (e) {
        // 2) 드물게 내부 핸들이 잡혀 있으면 경고만 남기고 마지막 수단으로 파일 삭제
        LoggerUtils.logWarning('SQLite 데이터베이스 삭제 중 경고: $e', tag: _tag);
        try {
          final dbFile = File(dbPath);
          if (await dbFile.exists()) {
            await dbFile.delete();
            LoggerUtils.logInfo('강제 파일 삭제 완료(마지막 수단): $dbPath', tag: _tag);
          }
        } catch (e2) {
          LoggerUtils.logWarning('강제 파일 삭제 실패(무시): $e2', tag: _tag);
        }
      }
    } catch (e) {
      LoggerUtils.logError('데이터베이스 정리 실패', tag: _tag, error: e);
    }
  }

  /// 캐시 조용히 정리
  static Future<void> _cleanupCachesSilently() async {
    try {
      LoggerUtils.logInfo('캐시 정리 중...', tag: _tag);

      // 이미지 캐시 디렉토리 정리
      try {
        final appDir = await getApplicationDocumentsDirectory();
        final imageDir = Directory('${appDir.path}/product_images');
        if (await imageDir.exists()) {
          await imageDir.delete(recursive: true);
          LoggerUtils.logInfo('이미지 캐시 디렉토리 삭제 완료', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logWarning('이미지 캐시 정리 실패 (무시)', tag: _tag, error: e);
      }

      // 임시 파일 정리
      try {
        final tempDir = await getTemporaryDirectory();
        final tempFiles = await tempDir.list().toList();
        for (final file in tempFiles) {
          if (file is File) {
            await file.delete();
          }
        }
        LoggerUtils.logInfo('임시 파일 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logWarning('임시 파일 정리 실패 (무시)', tag: _tag, error: e);
      }

    } catch (e) {
      LoggerUtils.logError('캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// SharedPreferences 완전 초기화 (로그아웃용 - 로그인 페이지로 이동)
  static Future<void> _resetSharedPreferencesForLogout() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 온보딩 상태 백업
      final isOnboarded = prefs.getBool('isOnboarded') ?? false;

      // 모든 데이터 삭제
      await prefs.clear();

      // 로그아웃 후 로그인 페이지로 이동하도록 설정
      await prefs.setBool('isOnboarded', isOnboarded); // 온보딩은 유지
      await prefs.setString('auth_mode', 'login');
      await prefs.setBool('logout_completed', true); // 로그아웃 완료 플래그

      LoggerUtils.logInfo('✅ SharedPreferences 초기화 완료 (로그아웃 플래그 설정)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SharedPreferences 초기화 실패', tag: _tag, error: e);
    }
  }

  // 🔥 로그아웃 시 Firebase 사용자 설정 삭제 메서드 제거
  // 로그아웃은 로컬 인증 상태만 해제하고, 서버의 사용자 데이터(닉네임 등)는 유지해야 함
  // 회원탈퇴 시에만 서버 데이터를 삭제해야 함 (MyPageScreen._clearFirebaseUserSettingsForDeletion 참조)
}
