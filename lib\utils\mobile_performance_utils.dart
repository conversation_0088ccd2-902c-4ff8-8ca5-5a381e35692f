import 'dart:async';
import 'logger_utils.dart';
import 'memory_manager.dart';
import 'object_pool.dart';

/// 모바일 성능 최적화 유틸리티
/// 
/// 메모리 모니터링, 성능 최적화, 리소스 관리 등을 담당합니다.
class MobilePerformanceUtils {
  static const String _tag = 'MobilePerformanceUtils';
  
  // 메모리 모니터링 관련
  static Timer? _memoryMonitorTimer;
  static bool _isMemoryMonitoring = false;
  static const int _memoryThresholdMB = 1024; // 메모리 임계값 (MB) - 1GB
  static const Duration _monitoringInterval = Duration(seconds: 30);

  // 성능 최적화 설정
  static bool _isOptimized = false;
  static const int _maxImageCacheSize = 500; // MB - 50MB → 500MB

  // 최적화 시스템 인스턴스들
  static MemoryManager? _memoryManager;
  static ObjectPool<Object>? _objectPool;

  /// 성능 최적화 초기화
  static void initializeOptimizations() {
    if (_isOptimized) return;
    
    try {
      // MemoryManager 초기화
      _memoryManager = MemoryManager();
      _memoryManager!.initialize();
      
      // ObjectPool 초기화
      _objectPool = ObjectPool(
        config: ObjectPoolConfig.defaultConfig(),
        factory: () => Object(),
      );
      _objectPool!.initialize();
      
      // 이미지 캐시 크기 제한
      _setImageCacheSize();
      
      // 메모리 모니터링 시작
      startMemoryMonitoring();
      
      // 성능 최적화 플래그 설정
      _isOptimized = true;
      
      LoggerUtils.logInfo('모바일 성능 최적화 초기화 완료', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '성능 최적화 초기화 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 메모리 모니터링 시작
  static void startMemoryMonitoring({Duration? interval}) {
    if (_isMemoryMonitoring) return;
    
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = Timer.periodic(
      interval ?? _monitoringInterval,
      (_) => _checkMemoryUsage(),
    );
    
    _isMemoryMonitoring = true;
    LoggerUtils.logInfo('메모리 모니터링 시작', tag: _tag);
  }

  /// 메모리 모니터링 중지
  static void stopMemoryMonitoring() {
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;
    _isMemoryMonitoring = false;
    LoggerUtils.logInfo('메모리 모니터링 중지', tag: _tag);
  }

  /// 앱 종료 시 모든 리소스 정리 (메모리 누수 방지)
  static void shutdown() {
    stopMemoryMonitoring();
    
    // 최적화 시스템 인스턴스들 정리
    _memoryManager?.dispose();
    _memoryManager = null;
    
    _objectPool?.dispose();
    _objectPool = null;
    
    _isOptimized = false;
    LoggerUtils.logInfo('MobilePerformanceUtils shutdown 완료', tag: _tag);
  }

  /// 메모리 사용량 확인
  static Future<void> _checkMemoryUsage() async {
    try {
      final memoryInfo = await _getMemoryInfo();
      final usedMemoryMB = memoryInfo['used'] ?? 0;
      
      LoggerUtils.logDebug(
        '메모리 사용량: ${usedMemoryMB}MB',
        tag: _tag,
        data: {'used_memory_mb': usedMemoryMB},
      );
      
      // 메모리 임계값 초과 시 정리 작업 수행
      if (usedMemoryMB > _memoryThresholdMB) {
        LoggerUtils.logWarning(
          '메모리 사용량 임계값 초과: ${usedMemoryMB}MB > ${_memoryThresholdMB}MB',
          tag: _tag,
        );
        await performMemoryCleanup();
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '메모리 사용량 확인 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 메모리 정보 조회
  static Future<Map<String, int>> _getMemoryInfo() async {
    try {
      // Flutter에서는 직접적인 메모리 정보 조회가 제한적이므로
      // 기본값을 반환하고 실제 구현에서는 플랫폼 채널을 통해 조회
      return {
        'used': 50, // 기본값 (실제로는 플랫폼 채널을 통해 조회 필요)
        'total': 1000,
        'available': 950,
      };
    } catch (e) {
      LoggerUtils.logError(
        '메모리 정보 조회 실패',
        tag: _tag,
        error: e,
      );
      return {'used': 0, 'total': 0, 'available': 0};
    }
  }

  /// 메모리 정리 작업 수행
  static Future<void> performMemoryCleanup() async {
    try {
      LoggerUtils.logInfo('메모리 정리 작업 시작', tag: _tag);
      
      // MemoryManager를 통한 메모리 정리
      if (_memoryManager != null) {
        // MemoryManager가 초기화되어 있음을 확인
        LoggerUtils.logDebug('MemoryManager가 활성화되어 있음', tag: _tag);
      }
      
      // 이미지 캐시 정리
      await _clearImageCache();
      
      // 객체 풀 정리
      _clearObjectPools();
      
      // 가비지 컬렉션 요청 (가능한 경우)
      _requestGarbageCollection();
      
      LoggerUtils.logInfo('메모리 정리 작업 완료', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '메모리 정리 작업 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 이미지 캐시 정리
  static Future<void> _clearImageCache() async {
    try {
      // CachedNetworkImage 캐시 정리
      // 실제 구현에서는 cached_network_image 패키지의 캐시 매니저 사용
      LoggerUtils.logDebug('이미지 캐시 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError(
        '이미지 캐시 정리 실패',
        tag: _tag,
        error: e,
      );
    }
  }

  /// 객체 풀 정리
  static void _clearObjectPools() {
    try {
      // 객체 풀에서 불필요한 객체들 제거
      LoggerUtils.logDebug('객체 풀 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError(
        '객체 풀 정리 실패',
        tag: _tag,
        error: e,
      );
    }
  }

  /// 가비지 컬렉션 요청
  static void _requestGarbageCollection() {
    try {
      // Dart의 가비지 컬렉션 요청 (실제로는 제한적)
      // Flutter에서는 자동으로 관리되므로 로그만 남김
      LoggerUtils.logDebug('가비지 컬렉션 요청 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError(
        '가비지 컬렉션 요청 실패',
        tag: _tag,
        error: e,
      );
    }
  }

  /// 이미지 캐시 크기 설정
  static void _setImageCacheSize() {
    try {
      // 이미지 캐시 크기 제한 설정
      LoggerUtils.logDebug('이미지 캐시 크기 설정 완료: ${_maxImageCacheSize}MB', tag: _tag);
    } catch (e) {
      LoggerUtils.logError(
        '이미지 캐시 크기 설정 실패',
        tag: _tag,
        error: e,
      );
    }
  }

  /// 성능 최적화 정리
  static void dispose() {
    stopMemoryMonitoring();
    _isOptimized = false;
    LoggerUtils.logInfo('성능 최적화 정리 완료', tag: _tag);
  }

  /// 현재 최적화 상태 확인
  static bool get isOptimized => _isOptimized;
  
  /// 메모리 모니터링 상태 확인
  static bool get isMemoryMonitoring => _isMemoryMonitoring;
}
