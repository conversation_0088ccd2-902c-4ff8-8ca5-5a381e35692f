import 'dart:async';
import 'logger_utils.dart';

/// 객체 풀 상태
enum PoolState {
  idle,
  active,
  error,
  disposing,
}

/// 객체 풀 통계
class PoolStats {
  final int totalObjects;
  final int availableObjects;
  final int borrowedObjects;
  final int totalCreated;
  final int totalDestroyed;
  final int totalBorrowed;
  final int totalReturned;
  final double utilizationRate;
  final Duration averageBorrowTime;
  final PoolState state;

  const PoolStats({
    required this.totalObjects,
    required this.availableObjects,
    required this.borrowedObjects,
    required this.totalCreated,
    required this.totalDestroyed,
    required this.totalBorrowed,
    required this.totalReturned,
    required this.utilizationRate,
    required this.averageBorrowTime,
    required this.state,
  });
}

/// 객체 풀 설정
class ObjectPoolConfig<T> {
  final int minSize;
  final int maxSize;
  final Duration objectLifetime;
  final Duration cleanupInterval;
  final bool enableHealthCheck;
  final Duration healthCheckInterval;
  final bool Function(T)? validator;
  final void Function(T)? reset;
  final void Function(T)? disposer;
  final bool enableMemoryLeakDetection;
  final Duration leakDetectionInterval;

  const ObjectPoolConfig({
    this.minSize = 0,
    this.maxSize = 100,
    this.objectLifetime = const Duration(minutes: 30),
    this.cleanupInterval = const Duration(minutes: 5),
    this.enableHealthCheck = true,
    this.healthCheckInterval = const Duration(minutes: 10),
    this.validator,
    this.reset,
    this.disposer,
    this.enableMemoryLeakDetection = true,
    this.leakDetectionInterval = const Duration(minutes: 15),
  });

  /// 기본 설정
  factory ObjectPoolConfig.defaultConfig() {
    return const ObjectPoolConfig();
  }

  /// 고성능 설정
  factory ObjectPoolConfig.highPerformance() {
    return const ObjectPoolConfig(
      minSize: 10,
      maxSize: 200,
      objectLifetime: const Duration(minutes: 15),
      cleanupInterval: const Duration(minutes: 2),
      enableHealthCheck: true,
      healthCheckInterval: const Duration(minutes: 5),
      enableMemoryLeakDetection: true,
      leakDetectionInterval: const Duration(minutes: 10),
    );
  }

  /// 메모리 효율 설정
  factory ObjectPoolConfig.memoryEfficient() {
    return const ObjectPoolConfig(
      minSize: 0,
      maxSize: 50,
      objectLifetime: const Duration(minutes: 60),
      cleanupInterval: const Duration(minutes: 10),
      enableHealthCheck: false,
      enableMemoryLeakDetection: true,
      leakDetectionInterval: const Duration(minutes: 30),
    );
  }
}

/// 풀링된 객체 정보
class PooledObject<T> {
  final T object;
  final String id;
  final DateTime createdAt;
  DateTime? _lastBorrowed;
  DateTime? _lastReturned;
  int _borrowCount = 0;

  PooledObject({
    required this.object,
    required this.id,
    required this.createdAt,
  });

  /// 객체 대여
  void borrow() {
    _lastBorrowed = DateTime.now();
    _borrowCount++;
  }

  /// 객체 반환
  void returnToPool() {
    _lastReturned = DateTime.now();
  }

  /// 객체 수명 (분)
  int get ageInMinutes => DateTime.now().difference(createdAt).inMinutes;

  /// 유휴 시간 (분)
  int get idleTimeInMinutes {
    if (_lastReturned == null) return 0;
    return DateTime.now().difference(_lastReturned!).inMinutes;
  }

  /// 대여 횟수
  int get borrowCount => _borrowCount;

  /// 사용 가능 여부
  bool get isAvailable => _lastReturned != null && _lastBorrowed != null;

  /// 마지막 사용 시간
  DateTime? get lastUsed => _lastBorrowed ?? createdAt;
}

/// 객체 풀
class ObjectPool<T> {
  final ObjectPoolConfig<T> config;
  final T Function() factory;
  
  final Map<String, PooledObject<T>> _objects = {};
  final Set<String> _availableObjects = {};
  final Set<String> _borrowedObjects = {};
  
  Timer? _cleanupTimer;
  Timer? _healthCheckTimer;
  Timer? _leakDetectionTimer;
  PoolState _state = PoolState.idle;
  
  int _totalCreated = 0;
  int _totalDestroyed = 0;
  int _totalBorrowed = 0;
  int _totalReturned = 0;
  final List<Duration> _borrowTimes = [];
  
  // 메모리 누수 감지를 위한 참조 추적 (WeakReference 대신 간단한 추적)
  final Map<String, DateTime> _objectTimestamps = {};
  
  // 콜백 함수들
  void Function(String, T)? onObjectCreated;
  void Function(String, T)? onObjectBorrowed;
  void Function(String, T)? onObjectReturned;
  void Function(String, T)? onObjectDestroyed;
  void Function(String)? onMemoryLeakDetected;

  ObjectPool({
    required this.config,
    required this.factory,
  });

  /// 초기화
  Future<void> initialize() async {
    LoggerUtils.logDebug(
      'Initializing object pool: ${T.toString()}',
      tag: 'ObjectPool',
    );

    _state = PoolState.active;
    _startCleanupTimer();
    
    if (config.enableHealthCheck) {
      _startHealthCheckTimer();
    }
    
    if (config.enableMemoryLeakDetection) {
      _startLeakDetectionTimer();
    }

    // 최소 크기만큼 객체 생성
    for (int i = 0; i < config.minSize; i++) {
      _createObject();
    }

    LoggerUtils.logDebug(
      'Object pool initialized: ${T.toString()}',
      tag: 'ObjectPool',
    );
  }

  /// 메모리 누수 감지 시작
  void _startLeakDetectionTimer() {
    _leakDetectionTimer?.cancel();
    _leakDetectionTimer = Timer.periodic(config.leakDetectionInterval, (_) {
      _detectMemoryLeaks();
    });
  }

  /// 메모리 누수 감지
  void _detectMemoryLeaks() {
    final now = DateTime.now();
    final expiredObjects = <String>[];

    for (final entry in _objectTimestamps.entries) {
      final key = entry.key;
      final timestamp = entry.value;

      // 1시간 이상 된 객체 확인
      if (now.difference(timestamp) > const Duration(hours: 1)) {
        // 간단한 메모리 누수 감지 (실제로는 더 정교한 방법 필요)
        LoggerUtils.logDebug(
          'Checking for potential memory leak: $key',
          tag: 'ObjectPool',
        );
        onMemoryLeakDetected?.call(key);
      }
    }

    // 만료된 추적 정보 정리
    for (final key in expiredObjects) {
      _objectTimestamps.remove(key);
    }
  }

  /// 객체 참조 추적 시작
  void _trackObject(String objectId, T object) {
    _objectTimestamps[objectId] = DateTime.now();
  }

  /// 객체 참조 추적 중지
  void _untrackObject(String objectId) {
    _objectTimestamps.remove(objectId);
  }

  /// 객체 대여
  Future<T> borrow() async {
    if (_state == PoolState.error) {
      throw Exception('Object pool is in error state');
    }

    final borrowStartTime = DateTime.now();

    try {
      // 사용 가능한 객체가 있는지 확인
      if (_availableObjects.isNotEmpty) {
        final objectId = _availableObjects.first;
        final pooledObject = _objects[objectId]!;
        
        if (pooledObject.isAvailable) {
          pooledObject.borrow();
          _borrowedObjects.add(objectId);
          _trackObject(objectId, pooledObject.object);
          
          // 검증
          if (config.validator != null) {
            try {
              config.validator!(pooledObject.object);
            } catch (e) {
              // 검증 실패 시 객체 무효화
              _untrackObject(objectId);
              _destroyObject(objectId);
              
              LoggerUtils.logWarning(
                'Object validation failed: $objectId - $e',
                tag: 'ObjectPool',
              );
              
              // 재시도
              return await borrow();
            }
          }

          onObjectBorrowed?.call(objectId, pooledObject.object);

          LoggerUtils.logDebug(
            'Borrowed object: $objectId',
            tag: 'ObjectPool',
          );
          
          return pooledObject.object;
        } else {
          // 유효하지 않은 객체 제거
          await _destroyObject(objectId);
        }
      }

      // 새 객체 생성 가능한지 확인
      if (_objects.length < config.maxSize) {
        final pooledObject = await _createObject();
        pooledObject.borrow();
        _borrowedObjects.add(pooledObject.id);
        _trackObject(pooledObject.id, pooledObject.object);
        
        onObjectBorrowed?.call(pooledObject.id, pooledObject.object);

        LoggerUtils.logDebug(
          'Created and borrowed new object: ${pooledObject.id}',
          tag: 'ObjectPool',
        );
        
        return pooledObject.object;
      }

      // 대기
      return await _waitForObject();
    } finally {
      final borrowTime = DateTime.now().difference(borrowStartTime);
      _borrowTimes.add(borrowTime);
      
      // 최근 100개 대여 시간만 유지
      if (_borrowTimes.length > 100) {
        _borrowTimes.removeAt(0);
      }
    }
  }

  /// 객체 반환
  void returnObject(T object) {
    final objectId = _findObjectId(object);
    if (objectId == null) {
      LoggerUtils.logWarning(
        'Attempted to return unknown object',
        tag: 'ObjectPool',
      );
      return;
    }

    final pooledObject = _objects[objectId];
    if (pooledObject == null) return;

    pooledObject.returnToPool();
    _borrowedObjects.remove(objectId);
    _availableObjects.add(objectId);

    // 객체 리셋
    if (config.reset != null) {
      try {
        config.reset!(pooledObject.object);
      } catch (e) {
        LoggerUtils.logWarning(
          'Failed to reset object $objectId: $e',
          tag: 'ObjectPool',
        );
      }
    }

    onObjectReturned?.call(objectId, pooledObject.object);

    LoggerUtils.logDebug(
      'Returned object: $objectId',
      tag: 'ObjectPool',
    );
  }

  /// 객체 생성
  Future<PooledObject<T>> _createObject() async {
    final startTime = DateTime.now();
    
    try {
      final object = factory();
      final objectId = _generateObjectId();
      
      final pooledObject = PooledObject<T>(
        object: object,
        id: objectId,
        createdAt: DateTime.now(),
      );

      _objects[objectId] = pooledObject;
      _availableObjects.add(objectId);
      _totalCreated++;

      onObjectCreated?.call(objectId, object);

      final creationTime = DateTime.now().difference(startTime);
      
      LoggerUtils.logDebug(
        'Created object: $objectId (${creationTime.inMilliseconds}ms)',
        tag: 'ObjectPool',
      );

      return pooledObject;
    } catch (e) {
      LoggerUtils.logError(
        'Failed to create object: $e',
        error: e,
        tag: 'ObjectPool',
      );
      rethrow;
    }
  }

  /// 객체 대기
  Future<T> _waitForObject() async {
    final maxWaitTime = const Duration(seconds: 30);
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      // 사용 가능한 객체 확인
      if (_availableObjects.isNotEmpty) {
        final objectId = _availableObjects.first;
        final pooledObject = _objects[objectId]!;
        
        if (pooledObject.isAvailable) {
          pooledObject.borrow();
          _borrowedObjects.add(objectId);
          _trackObject(objectId, pooledObject.object);
          return pooledObject.object;
        } else {
          await _destroyObject(objectId);
        }
      }

      // 새 객체 생성 시도
      if (_objects.length < config.maxSize) {
        final pooledObject = await _createObject();
        pooledObject.borrow();
        _borrowedObjects.add(pooledObject.id);
        _trackObject(pooledObject.id, pooledObject.object);
        return pooledObject.object;
      }

      // 잠시 대기
      await Future.delayed(const Duration(milliseconds: 100));
    }

    throw Exception('Timeout waiting for available object');
  }

  /// 객체 제거
  Future<void> _destroyObject(String objectId) async {
    final pooledObject = _objects[objectId];
    if (pooledObject == null) return;

    try {
      if (config.disposer != null) {
        config.disposer!(pooledObject.object);
      }
    } catch (e) {
      LoggerUtils.logWarning(
        'Error disposing object $objectId: $e',
        tag: 'ObjectPool',
      );
    }

    _objects.remove(objectId);
    _availableObjects.remove(objectId);
    _borrowedObjects.remove(objectId);
    _totalDestroyed++;
    _untrackObject(objectId);

    onObjectDestroyed?.call(objectId, pooledObject.object);

    LoggerUtils.logDebug(
      'Destroyed object: $objectId',
      tag: 'ObjectPool',
    );
  }

  /// 객체 ID로 객체 찾기
  String? _findObjectId(T object) {
    for (final entry in _objects.entries) {
      if (identical(entry.value.object, object)) {
        return entry.key;
      }
    }
    return null;
  }

  /// 객체 ID 생성
  String _generateObjectId() {
    return 'obj_${T.toString()}_${DateTime.now().millisecondsSinceEpoch}_$_totalCreated';
  }

  /// 정리 타이머 시작
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(config.cleanupInterval, (_) {
      _performCleanup();
    });
  }

  /// 정리 작업 수행
  void _performCleanup() {
    final objectsToRemove = <String>[];

    for (final entry in _objects.entries) {
      final pooledObject = entry.value;
      
      // 수명 초과 객체
      if (pooledObject.ageInMinutes > config.objectLifetime.inMinutes) {
        objectsToRemove.add(entry.key);
        continue;
      }

      // 유휴 시간 초과 객체 (최소 크기 유지)
      if (pooledObject.isAvailable && 
          pooledObject.idleTimeInMinutes > config.objectLifetime.inMinutes &&
          _objects.length > config.minSize) {
        objectsToRemove.add(entry.key);
      }
    }

    for (final objectId in objectsToRemove) {
      _destroyObject(objectId);
    }

    if (objectsToRemove.isNotEmpty) {
      LoggerUtils.logDebug(
        'Cleaned up ${objectsToRemove.length} objects',
        tag: 'ObjectPool',
      );
    }
  }

  /// 헬스 체크 타이머 시작
  void _startHealthCheckTimer() {
    _healthCheckTimer = Timer.periodic(config.healthCheckInterval, (_) {
      _performHealthCheck();
    });
  }

  /// 헬스 체크 수행
  Future<void> _performHealthCheck() async {
    LoggerUtils.logDebug(
      'Performing health check on ${_objects.length} objects',
      tag: 'ObjectPool',
    );

    final objectsToRemove = <String>[];

    for (final entry in _objects.entries) {
      final pooledObject = entry.value;
      
      if (config.validator != null) {
        try {
          config.validator!(pooledObject.object);
          
          // 오류 상태였던 객체 복구
          if (!pooledObject.isAvailable) { // isAvailable 사용
            pooledObject.returnToPool(); // returnToPool 사용
            
            LoggerUtils.logDebug(
              'Object recovered: ${pooledObject.id}',
              tag: 'ObjectPool',
            );
          }
        } catch (e) {
          // 검증 실패 시 객체 무효화
          _untrackObject(entry.key);
          _destroyObject(entry.key);
          
          LoggerUtils.logWarning(
            'Unhealthy object detected: ${pooledObject.id} - $e',
            tag: 'ObjectPool',
          );
        }
      }
    }

    // 불량 객체 제거
    for (final objectId in objectsToRemove) {
      await _destroyObject(objectId);
    }

    // 최소 크기 유지
    while (_objects.length < config.minSize) {
      await _createObject();
    }
  }



  /// 통계 정보
  PoolStats getStats() {
    final avgBorrowTime = _borrowTimes.isNotEmpty
        ? Duration(milliseconds: _borrowTimes
            .map((d) => d.inMilliseconds)
            .reduce((a, b) => a + b) ~/ _borrowTimes.length)
        : Duration.zero;

    final utilizationRate = _objects.isNotEmpty
        ? _borrowedObjects.length / _objects.length
        : 0.0;

    return PoolStats(
      totalObjects: _objects.length,
      availableObjects: _availableObjects.length,
      borrowedObjects: _borrowedObjects.length,
      totalCreated: _totalCreated,
      totalDestroyed: _totalDestroyed,
      totalBorrowed: _totalBorrowed,
      totalReturned: _totalReturned,
      utilizationRate: utilizationRate,
      averageBorrowTime: avgBorrowTime,
      state: _state,
    );
  }

  /// 풀 크기 조정
  Future<void> resize(int newMaxSize) async {
    if (newMaxSize < config.minSize) {
      throw ArgumentError('New max size cannot be less than min size');
    }

    final oldMax = config.maxSize;
    
    LoggerUtils.logDebug(
      'Resizing object pool from $oldMax to $newMaxSize',
      tag: 'ObjectPool',
    );

    if (newMaxSize < oldMax) {
      // 풀 크기 감소
      final excessObjects = _objects.length - newMaxSize;
      final availableObjects = _availableObjects.toList();
      
      for (int i = 0; i < excessObjects && i < availableObjects.length; i++) {
        await _destroyObject(availableObjects[i]);
      }
    }
  }

  /// 모든 객체 반환
  void returnAllObjects() {
    final borrowedObjects = _borrowedObjects.toList();
    for (final objectId in borrowedObjects) {
      final pooledObject = _objects[objectId];
      if (pooledObject != null) {
        returnObject(pooledObject.object);
      }
    }
  }

  /// 풀 비우기
  Future<void> clear() async {
    final objectIds = _objects.keys.toList();
    for (final objectId in objectIds) {
      await _destroyObject(objectId);
    }
  }

  /// 리소스 정리
  Future<void> dispose() async {
    _cleanupTimer?.cancel();
    _healthCheckTimer?.cancel();
    _leakDetectionTimer?.cancel();
    
    await clear();
    
    LoggerUtils.logDebug(
      'Object pool disposed',
      tag: 'ObjectPool',
    );
  }
}

/// 전역 객체 풀 관리자
class ObjectPoolManager {
  static final Map<String, ObjectPool> _pools = {};
  
  /// 풀 생성 또는 가져오기
  static ObjectPool<T> getPool<T>({
    required String name,
    required T Function() factory,
    void Function(T)? reset,
    int maxSize = 100,
    Duration maxIdleTime = const Duration(minutes: 5),
  }) {
    if (!_pools.containsKey(name)) {
      final config = ObjectPoolConfig<T>(
        minSize: 0,
        maxSize: maxSize,
        validator: null,
        enableHealthCheck: false,
      );
      
      _pools[name] = ObjectPool<T>(
        config: config,
        factory: factory,
      );
    }
    
    return _pools[name] as ObjectPool<T>;
  }
  
  /// 풀 제거
  static void removePool(String name) {
    final pool = _pools.remove(name);
    pool?.dispose();
  }
  
  /// 모든 풀 정리
  static void clearAll() {
    for (final pool in _pools.values) {
      pool.dispose();
    }
    _pools.clear();
  }
  
  /// 풀 상태 정보
  static Map<String, Map<String, dynamic>> getPoolStats() {
    final stats = <String, Map<String, dynamic>>{};
    
    for (final entry in _pools.entries) {
      final poolStats = entry.value.getStats();
      stats[entry.key] = {
        'totalObjects': poolStats.totalObjects,
        'availableObjects': poolStats.availableObjects,
        'borrowedObjects': poolStats.borrowedObjects,
        'utilizationRate': poolStats.utilizationRate,
      };
    }
    
    return stats;
  }

  /// 메모리 누수 방지를 위한 정리 (앱 종료 시 호출)
  static void shutdown() {
    clearAll();
    LoggerUtils.logInfo('ObjectPoolManager 정리 완료');
  }
}

/// 자주 사용되는 객체들의 풀링을 위한 전역 풀들
class GlobalPools {
  /// 문자열 빌더 풀
  static ObjectPool<StringBuffer> get stringBufferPool => 
    ObjectPoolManager.getPool<StringBuffer>(
      name: 'StringBuffer',
      factory: () => StringBuffer(),
      reset: (sb) => sb.clear(),
      maxSize: 50,
    );
  
  /// 리스트 풀
  static ObjectPool<List> get listPool =>
    ObjectPoolManager.getPool<List>(
      name: 'List',
      factory: () => <dynamic>[],
      reset: (list) => list.clear(),
      maxSize: 100,
    );
  
  /// 맵 풀
  static ObjectPool<Map> get mapPool =>
    ObjectPoolManager.getPool<Map>(
      name: 'Map',
      factory: () => <dynamic, dynamic>{},
      reset: (map) => map.clear(),
      maxSize: 50,
    );
  
  /// Set 풀
  static ObjectPool<Set> get setPool =>
    ObjectPoolManager.getPool<Set>(
      name: 'Set',
      factory: () => <dynamic>{},
      reset: (set) => set.clear(),
      maxSize: 30,
    );
}

/// 객체 풀링을 위한 확장 메서드들
extension ObjectPoolExtensions<T> on T {
  /// 객체를 풀에 반환하는 확장 메서드
  void releaseToPool<T>(ObjectPool<T> pool) {
    pool.returnObject(this as T);
  }
}

/// 풀링 가능한 객체를 위한 믹스인
mixin Poolable<T> {
  /// 객체 초기화 (풀에서 재사용 시)
  void reset();
  
  /// 풀에서 가져온 후 호출
  void onAcquired() {}
  
  /// 풀로 반환되기 전에 호출
  void onReleased() {}
} 
