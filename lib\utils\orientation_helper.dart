import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 화면 방향 제어를 위한 헬퍼 클래스
class OrientationHelper {
  /// 세로모드로 고정 (온보딩, 로그인 등에서 사용)
  static Future<void> setPortraitMode() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// 모든 방향 허용 (인벤토리 등에서 사용)
  static Future<void> setAllOrientations() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// 현재 방향이 가로모드인지 확인
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// 현재 방향이 세로모드인지 확인
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// 방향 전환 시 부드러운 전환을 위한 지연
  static Future<void> waitForOrientationChange() async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// 페이지 진입 시 세로모드로 설정하고 부드러운 전환 제공
  static Future<void> enterPortraitMode() async {
    await setPortraitMode();
    await waitForOrientationChange();
  }

  /// 페이지 진입 시 모든 방향 허용하고 부드러운 전환 제공
  static Future<void> enterAllOrientationsMode() async {
    await setAllOrientations();
    await waitForOrientationChange();
  }
}

/// 방향 제어가 필요한 페이지에서 사용할 Mixin
mixin OrientationControlMixin<T extends StatefulWidget> on State<T> {
  bool _isPortraitMode = true;

  /// 세로모드로 설정
  @protected
  Future<void> setPortraitMode() async {
    if (!_isPortraitMode) {
      _isPortraitMode = true;
      await OrientationHelper.enterPortraitMode();
    }
  }

  /// 모든 방향 허용
  @protected
  Future<void> setAllOrientations() async {
    if (_isPortraitMode) {
      _isPortraitMode = false;
      await OrientationHelper.enterAllOrientationsMode();
    }
  }

  /// 현재 방향 상태 확인
  @protected
  bool get isPortraitMode => _isPortraitMode;
}
