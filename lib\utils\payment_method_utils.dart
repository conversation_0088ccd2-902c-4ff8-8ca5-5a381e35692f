/// 결제 방법 관련 유틸리티 함수들
class PaymentMethodUtils {
  /// 결제 방법 ID를 한글 이름으로 변환
  static String getDisplayName(String? paymentMethodId) {
    if (paymentMethodId == null || paymentMethodId.isEmpty) {
      return '';
    }
    
    switch (paymentMethodId.toLowerCase()) {
      case 'cash':
        return '현금';
      case 'transfer':
        return '계좌이체';
      case 'card':
        return '카드';
      case 'mobile':
        return '모바일결제';
      default:
        // 이미 한글인 경우 그대로 반환
        return paymentMethodId;
    }
  }
  
  /// 한글 이름을 결제 방법 ID로 변환 (필요시 사용)
  static String getIdFromDisplayName(String displayName) {
    switch (displayName) {
      case '현금':
        return 'cash';
      case '계좌이체':
        return 'transfer';
      case '카드':
        return 'card';
      case '모바일결제':
        return 'mobile';
      default:
        return displayName;
    }
  }
}
