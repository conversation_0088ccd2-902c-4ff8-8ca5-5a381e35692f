/// 바라 부스 매니저 - 권한 유틸리티
///
/// 권한 체크 및 제한 기능 관련 유틸리티 함수들을 제공합니다.
/// - 권한 체크 및 에러 메시지 표시
/// - 초대받은 사용자 제한 처리
/// - 공통 권한 검증 로직
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/permission_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/toast_utils.dart';
import '../utils/logger_utils.dart';

/// 권한 유틸리티 클래스
class PermissionUtils {
  static const String _tag = 'PermissionUtils';

  /// 현재 행사에 대한 수정 권한 체크
  /// 
  /// [context] BuildContext
  /// [ref] WidgetRef
  /// [showToast] 권한 없을 때 토스트 메시지 표시 여부 (기본값: true)
  /// 반환값: 수정 권한 여부
  static Future<bool> checkModifyPermission(
    BuildContext context,
    WidgetRef ref, {
    bool showToast = true,
  }) async {
    LoggerUtils.methodStart('checkModifyPermission', tag: _tag);

    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        if (showToast) {
          ToastUtils.showError(context, '현재 선택된 행사가 없습니다');
        }
        return false;
      }

      final canModify = await ref.read(permissionNotifierProvider.notifier)
          .canModify(currentWorkspace.id);

      if (!canModify && showToast) {
        ToastUtils.showWarning(context, '초대받은 사용자는 데이터를 수정할 수 없습니다');
      }

      LoggerUtils.logInfo('수정 권한 체크 결과: $canModify', tag: _tag);
      return canModify;
    } catch (e) {
      LoggerUtils.logError('수정 권한 체크 실패', tag: _tag, error: e);
      if (showToast) {
        ToastUtils.showError(context, '권한 확인 중 오류가 발생했습니다');
      }
      return false;
    }
  }

  /// 현재 행사에 대한 읽기 권한 체크
  /// 
  /// [context] BuildContext
  /// [ref] WidgetRef
  /// [showToast] 권한 없을 때 토스트 메시지 표시 여부 (기본값: true)
  /// 반환값: 읽기 권한 여부
  static Future<bool> checkReadPermission(
    BuildContext context,
    WidgetRef ref, {
    bool showToast = true,
  }) async {
    LoggerUtils.methodStart('checkReadPermission', tag: _tag);

    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        if (showToast) {
          ToastUtils.showError(context, '현재 선택된 행사가 없습니다');
        }
        return false;
      }

      final canRead = await ref.read(permissionNotifierProvider.notifier)
          .canRead(currentWorkspace.id);

      if (!canRead && showToast) {
        ToastUtils.showWarning(context, '이 행사의 데이터를 볼 권한이 없습니다');
      }

      LoggerUtils.logInfo('읽기 권한 체크 결과: $canRead', tag: _tag);
      return canRead;
    } catch (e) {
      LoggerUtils.logError('읽기 권한 체크 실패', tag: _tag, error: e);
      if (showToast) {
        ToastUtils.showError(context, '권한 확인 중 오류가 발생했습니다');
      }
      return false;
    }
  }

  /// 현재 행사에 대한 다운로드 권한 체크
  /// 
  /// [context] BuildContext
  /// [ref] WidgetRef
  /// [showToast] 권한 없을 때 토스트 메시지 표시 여부 (기본값: true)
  /// 반환값: 다운로드 권한 여부
  static Future<bool> checkDownloadPermission(
    BuildContext context,
    WidgetRef ref, {
    bool showToast = true,
  }) async {
    LoggerUtils.methodStart('checkDownloadPermission', tag: _tag);

    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        if (showToast) {
          ToastUtils.showError(context, '현재 선택된 행사가 없습니다');
        }
        return false;
      }

      final canDownload = await ref.read(permissionNotifierProvider.notifier)
          .canDownload(currentWorkspace.id);

      if (!canDownload && showToast) {
        ToastUtils.showWarning(context, '다운로드 권한이 없습니다');
      }

      LoggerUtils.logInfo('다운로드 권한 체크 결과: $canDownload', tag: _tag);
      return canDownload;
    } catch (e) {
      LoggerUtils.logError('다운로드 권한 체크 실패', tag: _tag, error: e);
      if (showToast) {
        ToastUtils.showError(context, '권한 확인 중 오류가 발생했습니다');
      }
      return false;
    }
  }

  /// 현재 행사에 대한 선입금 관리 권한 체크
  /// 
  /// [context] BuildContext
  /// [ref] WidgetRef
  /// [showToast] 권한 없을 때 토스트 메시지 표시 여부 (기본값: true)
  /// 반환값: 선입금 관리 권한 여부
  static Future<bool> checkPrepaymentPermission(
    BuildContext context,
    WidgetRef ref, {
    bool showToast = true,
  }) async {
    LoggerUtils.methodStart('checkPrepaymentPermission', tag: _tag);

    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        if (showToast) {
          ToastUtils.showError(context, '현재 선택된 행사가 없습니다');
        }
        return false;
      }

      final canManagePrepayment = await ref.read(permissionNotifierProvider.notifier)
          .canManagePrepayment(currentWorkspace.id);

      if (!canManagePrepayment && showToast) {
        ToastUtils.showWarning(context, '선입금 관리 권한이 없습니다');
      }

      LoggerUtils.logInfo('선입금 관리 권한 체크 결과: $canManagePrepayment', tag: _tag);
      return canManagePrepayment;
    } catch (e) {
      LoggerUtils.logError('선입금 관리 권한 체크 실패', tag: _tag, error: e);
      if (showToast) {
        ToastUtils.showError(context, '권한 확인 중 오류가 발생했습니다');
      }
      return false;
    }
  }

  /// 현재 사용자가 행사 소유자인지 확인
  /// 
  /// [context] BuildContext
  /// [ref] WidgetRef
  /// 반환값: 소유자 여부
  static Future<bool> isEventOwner(
    BuildContext context,
    WidgetRef ref,
  ) async {
    LoggerUtils.methodStart('isEventOwner', tag: _tag);

    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        return false;
      }

      final isOwner = await ref.read(permissionNotifierProvider.notifier)
          .isEventOwner(currentWorkspace.id);

      LoggerUtils.logInfo('소유자 여부 확인 결과: $isOwner', tag: _tag);
      return isOwner;
    } catch (e) {
      LoggerUtils.logError('소유자 여부 확인 실패', tag: _tag, error: e);
      return false;
    }
  }



  /// 권한 체크와 함께 액션 실행
  /// 
  /// [context] BuildContext
  /// [ref] WidgetRef
  /// [action] 실행할 액션
  /// [requireModify] 수정 권한 필요 여부 (기본값: true)
  /// 반환값: 액션 실행 여부
  static Future<bool> executeWithPermissionCheck(
    BuildContext context,
    WidgetRef ref,
    VoidCallback action, {
    bool requireModify = true,
  }) async {
    LoggerUtils.methodStart('executeWithPermissionCheck', tag: _tag);

    try {
      bool hasPermission;
      
      if (requireModify) {
        hasPermission = await checkModifyPermission(context, ref);
      } else {
        hasPermission = await checkReadPermission(context, ref);
      }

      if (hasPermission) {
        action();
        return true;
      }
      
      return false;
    } catch (e) {
      LoggerUtils.logError('권한 체크와 액션 실행 실패', tag: _tag, error: e);
      ToastUtils.showError(context, '작업 실행 중 오류가 발생했습니다');
      return false;
    }
  }
}
