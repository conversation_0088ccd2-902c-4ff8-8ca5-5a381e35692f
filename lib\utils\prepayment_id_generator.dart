/// 선입금 고유 ID 생성 유틸
/// microsecondsSinceEpoch 기반 + 같은 마이크로초 내 충돌 방지 카운터
class PrepaymentIdGenerator {
  static int _lastTimestamp = 0;
  static int _collisionCounter = 0;

  static int generate() {
    final now = DateTime.now().microsecondsSinceEpoch;
    if (now == _lastTimestamp) {
      _collisionCounter++;
    } else {
      _collisionCounter = 0;
      _lastTimestamp = now;
    }
    // 상위 비트: timestamp, 하위 12비트: collision counter (4096개)
    return (now << 12) | (_collisionCounter & 0xFFF);
  }
}
