/// 상품 표시 관련 유틸리티 함수들
///
/// 주요 기능:
/// - 카테고리명과 상품명을 조합한 표시 형식 생성
/// - 판매 기록에서 "[카테고리명]상품명" 형식 지원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import '../models/product.dart';
import '../models/category.dart' as model_category;
import '../models/sales_log.dart';
import '../repositories/product_repository.dart';
import '../utils/logger_utils.dart';

class ProductDisplayUtils {
  static const String _tag = 'ProductDisplayUtils';

  /// 상품의 카테고리명과 상품명을 조합하여 "카테고리명-상품명" 형식으로 반환
  ///
  /// [product]: 표시할 상품
  /// [categories]: 카테고리 목록 (성능 최적화를 위해 미리 로드된 목록 사용)
  /// 반환값: "카테고리명-상품명" 형식의 문자열
  static String getDisplayNameWithCategory(
    Product product, 
    List<model_category.Category> categories,
  ) {
    try {
      final category = categories.firstWhere(
        (cat) => cat.id == product.categoryId,
        orElse: () => model_category.Category(
          id: product.categoryId,
          name: '알 수 없는 카테고리',
          sortOrder: 999,
          eventId: product.eventId,
        ),
      );
      
      return '${category.name}-${product.name}';
    } catch (e) {
      LoggerUtils.logError('상품 표시명 생성 실패', error: e, tag: _tag);
      return product.name; // 실패 시 원본 상품명 반환
    }
  }

  /// 판매 기록에서 상품의 카테고리명과 상품명을 조합하여 "카테고리명-상품명" 형식으로 반환
  ///
  /// [salesLog]: 판매 기록
  /// [productRepository]: 상품 정보 조회용 Repository
  /// [categories]: 카테고리 목록 (성능 최적화를 위해 미리 로드된 목록 사용)
  /// 반환값: "카테고리명-상품명" 형식의 문자열, 실패 시 원본 상품명
  static Future<String> getSalesLogDisplayNameWithCategory(
    SalesLog salesLog,
    ProductRepository productRepository,
    List<model_category.Category> categories,
  ) async {
    try {
      // productId가 없으면 원본 상품명 반환
      if (salesLog.productId == null) {
        return salesLog.productName;
      }

      // 상품 정보 조회
      final product = await productRepository.getProductById(salesLog.productId!);
      if (product == null) {
        return salesLog.productName;
      }

      // 카테고리명과 조합
      return getDisplayNameWithCategory(product, categories);
    } catch (e) {
      LoggerUtils.logError('판매 기록 표시명 생성 실패', error: e, tag: _tag);
      return salesLog.productName; // 실패 시 원본 상품명 반환
    }
  }

  /// 카테고리별로 그룹화된 상품 목록에서 특정 카테고리의 상품들을 찾기
  ///
  /// [products]: 상품 목록
  /// [categoryId]: 찾을 카테고리 ID
  /// 반환값: 해당 카테고리의 상품 목록
  static List<Product> getProductsByCategory(
    List<Product> products, 
    int categoryId,
  ) {
    return products.where((product) => product.categoryId == categoryId).toList();
  }

  /// 상품 목록을 카테고리별로 그룹화
  ///
  /// [products]: 그룹화할 상품 목록
  /// 반환값: 카테고리ID를 키로 하는 상품 목록 맵
  static Map<int, List<Product>> groupProductsByCategory(List<Product> products) {
    final Map<int, List<Product>> groupedProducts = {};
    
    for (final product in products) {
      final categoryId = product.categoryId;
      if (!groupedProducts.containsKey(categoryId)) {
        groupedProducts[categoryId] = [];
      }
      groupedProducts[categoryId]!.add(product);
    }
    
    return groupedProducts;
  }

  /// 카테고리명으로 카테고리 찾기
  ///
  /// [categories]: 카테고리 목록
  /// [categoryName]: 찾을 카테고리명
  /// 반환값: 찾은 카테고리, 없으면 null
  static model_category.Category? findCategoryByName(
    List<model_category.Category> categories,
    String categoryName,
  ) {
    try {
      return categories.firstWhere((cat) => cat.name == categoryName);
    } catch (e) {
      return null;
    }
  }
}
