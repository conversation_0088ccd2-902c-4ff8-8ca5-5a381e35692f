import 'package:equatable/equatable.dart';

/// 앱 전체에서 사용하는 구조화된 에러 시스템
/// 
/// 타입 안전성과 일관된 에러 처리를 보장합니다.
sealed class AppError extends Equatable {
  const AppError();
  
  /// 에러 메시지 (사용자에게 표시)
  String get userMessage;
  
  /// 에러 코드 (디버깅용)
  String get errorCode;
  
  /// 에러 심각도
  ErrorSeverity get severity;
  
  /// 복구 가능 여부
  bool get isRecoverable;
  
  /// 복구 액션
  String? get recoveryAction;
}

/// 에러 심각도 레벨
enum ErrorSeverity {
  low,      // 정보성 에러
  medium,   // 경고성 에러
  high,     // 심각한 에러
  critical, // 치명적 에러
}

/// 데이터베이스 관련 에러
class DatabaseError extends AppError {
  final String message;
  final String? operation;
  final String? table;
  final dynamic originalError;
  
  const DatabaseError({
    required this.message,
    this.operation,
    this.table,
    this.originalError,
  });
  
  @override
  String get userMessage => '데이터 처리 중 오류가 발생했습니다.';
  
  @override
  String get errorCode => 'DB_${operation?.toUpperCase() ?? 'UNKNOWN'}';
  
  @override
  ErrorSeverity get severity => ErrorSeverity.high;
  
  @override
  bool get isRecoverable => true;
  
  @override
  String? get recoveryAction => '다시 시도해주세요.';
  
  @override
  List<Object?> get props => [message, operation, table, originalError];
}

/// 네트워크 관련 에러
class NetworkError extends AppError {
  final String message;
  final int? statusCode;
  final String? endpoint;
  final Duration? timeout;
  
  const NetworkError({
    required this.message,
    this.statusCode,
    this.endpoint,
    this.timeout,
  });
  
  @override
  String get userMessage {
    if (statusCode == 404) return '요청한 정보를 찾을 수 없습니다.';
    if (statusCode == 500) return '서버 오류가 발생했습니다.';
    if (timeout != null) return '네트워크 연결 시간이 초과되었습니다.';
    return '네트워크 연결에 문제가 있습니다.';
  }
  
  @override
  String get errorCode => 'NET_${statusCode ?? 'UNKNOWN'}';
  
  @override
  ErrorSeverity get severity {
    if (statusCode == 404) return ErrorSeverity.medium;
    if (statusCode == 500) return ErrorSeverity.high;
    return ErrorSeverity.medium;
  }
  
  @override
  bool get isRecoverable => true;
  
  @override
  String? get recoveryAction => '네트워크 연결을 확인하고 다시 시도해주세요.';
  
  @override
  List<Object?> get props => [message, statusCode, endpoint, timeout];
}

/// 유효성 검증 에러
class ValidationError extends AppError {
  final String field;
  final String message;
  final String? expectedValue;
  final String? actualValue;
  
  const ValidationError({
    required this.field,
    required this.message,
    this.expectedValue,
    this.actualValue,
  });
  
  @override
  String get userMessage => '$field: $message';
  
  @override
  String get errorCode => 'VAL_${field.toUpperCase()}';
  
  @override
  ErrorSeverity get severity => ErrorSeverity.low;
  
  @override
  bool get isRecoverable => true;
  
  @override
  String? get recoveryAction => '입력값을 확인하고 다시 입력해주세요.';
  
  @override
  List<Object?> get props => [field, message, expectedValue, actualValue];
}

/// 권한 관련 에러
class PermissionError extends AppError {
  final String permission;
  final String message;
  final bool isRequired;
  
  const PermissionError({
    required this.permission,
    required this.message,
    this.isRequired = false,
  });
  
  @override
  String get userMessage => '권한이 필요합니다: $permission';
  
  @override
  String get errorCode => 'PERM_${permission.toUpperCase()}';
  
  @override
  ErrorSeverity get severity => isRequired ? ErrorSeverity.high : ErrorSeverity.medium;
  
  @override
  bool get isRecoverable => true;
  
  @override
  String? get recoveryAction => '설정에서 권한을 허용해주세요.';
  
  @override
  List<Object?> get props => [permission, message, isRequired];
}

/// 파일 처리 관련 에러
class FileError extends AppError {
  final String operation;
  final String filePath;
  final String message;
  final FileErrorType type;
  
  const FileError({
    required this.operation,
    required this.filePath,
    required this.message,
    required this.type,
  });
  
  @override
  String get userMessage {
    switch (type) {
      case FileErrorType.notFound:
        return '파일을 찾을 수 없습니다.';
      case FileErrorType.accessDenied:
        return '파일 접근 권한이 없습니다.';
      case FileErrorType.corrupted:
        return '파일이 손상되었습니다.';
      case FileErrorType.tooLarge:
        return '파일 크기가 너무 큽니다.';
      case FileErrorType.unsupported:
        return '지원하지 않는 파일 형식입니다.';
    }
  }
  
  @override
  String get errorCode => 'FILE_${type.name.toUpperCase()}';
  
  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
  
  @override
  bool get isRecoverable => type != FileErrorType.corrupted;
  
  @override
  String? get recoveryAction {
    switch (type) {
      case FileErrorType.notFound:
        return '파일 경로를 확인해주세요.';
      case FileErrorType.accessDenied:
        return '파일 권한을 확인해주세요.';
      case FileErrorType.corrupted:
        return '파일을 다시 다운로드해주세요.';
      case FileErrorType.tooLarge:
        return '더 작은 파일을 선택해주세요.';
      case FileErrorType.unsupported:
        return '지원되는 파일 형식을 사용해주세요.';
    }
  }
  
  @override
  List<Object?> get props => [operation, filePath, message, type];
}

/// 파일 에러 타입
enum FileErrorType {
  notFound,     // 파일을 찾을 수 없음
  accessDenied, // 접근 권한 없음
  corrupted,    // 파일 손상
  tooLarge,     // 파일 크기 초과
  unsupported,  // 지원하지 않는 형식
}

/// 알 수 없는 에러
class UnknownError extends AppError {
  final String message;
  final dynamic originalError;
  final StackTrace? stackTrace;
  
  const UnknownError({
    required this.message,
    this.originalError,
    this.stackTrace,
  });
  
  @override
  String get userMessage => '알 수 없는 오류가 발생했습니다.';
  
  @override
  String get errorCode => 'UNKNOWN';
  
  @override
  ErrorSeverity get severity => ErrorSeverity.critical;
  
  @override
  bool get isRecoverable => false;
  
  @override
  String? get recoveryAction => '앱을 다시 시작해주세요.';
  
  @override
  List<Object?> get props => [message, originalError, stackTrace];
}

/// Provider 전용 예외 클래스
class ProviderException implements Exception {
  final String message;
  final String code;
  final Exception? error;
  final StackTrace? stackTrace;
  final ProviderExceptionSeverity severity;
  final Map<String, dynamic>? context;

  const ProviderException({
    required this.message,
    required this.code,
    this.error,
    this.stackTrace,
    this.severity = ProviderExceptionSeverity.error,
    this.context,
  });

  @override
  String toString() {
    return 'ProviderException: $message (Code: $code, Severity: $severity)';
  }

  /// 일반적인 Provider 예외 생성
  static ProviderException general(
    String message, {
    String? code,
    Exception? error,
    StackTrace? stackTrace,
    ProviderExceptionSeverity severity = ProviderExceptionSeverity.error,
    Map<String, dynamic>? context,
  }) {
    return ProviderException(
      message: message,
      code: code ?? 'PROVIDER_GENERAL_ERROR',
      error: error,
      stackTrace: stackTrace,
      severity: severity,
      context: context,
    );
  }

  /// 작업 실패 예외 생성
  static ProviderException forOperationFailed(
    String operation, {
    Exception? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    return ProviderException(
      message: '$operation 작업이 실패했습니다',
      code: 'OPERATION_FAILED',
      error: error,
      stackTrace: stackTrace,
      severity: ProviderExceptionSeverity.error,
      context: context,
    );
  }

  /// 데이터베이스 오류 예외 생성
  static ProviderException forDatabaseError(
    String message, {
    Exception? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    return ProviderException(
      message: message,
      code: 'DATABASE_ERROR',
      error: error,
      stackTrace: stackTrace,
      severity: ProviderExceptionSeverity.error,
      context: context,
    );
  }

  /// 유효성 검증 오류 예외 생성
  static ProviderException forValidationError(
    String message, {
    Exception? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    return ProviderException(
      message: message,
      code: 'VALIDATION_ERROR',
      error: error,
      stackTrace: stackTrace,
      severity: ProviderExceptionSeverity.warning,
      context: context,
    );
  }

  /// 최대 재시도 초과 예외 생성
  static ProviderException forMaxRetriesExceeded({
    Exception? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    return ProviderException(
      message: '최대 재시도 횟수를 초과했습니다',
      code: 'MAX_RETRIES_EXCEEDED',
      error: error,
      stackTrace: stackTrace,
      severity: ProviderExceptionSeverity.error,
      context: context,
    );
  }

  /// 기존 예외를 ProviderException으로 래핑
  static ProviderException wrap(
    dynamic error, {
    String? message,
    String? code,
    StackTrace? stackTrace,
    ProviderExceptionSeverity severity = ProviderExceptionSeverity.error,
    Map<String, dynamic>? context,
  }) {
    if (error is ProviderException) return error;
    
    return ProviderException(
      message: message ?? error.toString(),
      code: code ?? 'WRAPPED_ERROR',
      error: error is Exception ? error : Exception(error.toString()),
      stackTrace: stackTrace,
      severity: severity,
      context: context,
    );
  }
}

/// Provider 예외 심각도
enum ProviderExceptionSeverity {
  info,     // 정보성
  warning,  // 경고
  error,    // 오류
  critical, // 치명적 오류
}
