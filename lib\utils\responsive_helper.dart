import 'package:flutter/material.dart';

/// 반응형 디자인을 위한 헬퍼 클래스
/// 
/// 모바일/태블릿의 세로/가로 모드에 대응하는 유틸리티 제공
/// 온보딩 플로우에서 일관된 반응형 경험을 위해 사용
class ResponsiveHelper {
  ResponsiveHelper._();

  // === 화면 크기 기준점 ===
  
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // === 화면 타입 판별 ===
  
  /// 현재 화면이 모바일인지 확인
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  /// 현재 화면이 태블릿인지 확인
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  /// 현재 화면이 데스크톱인지 확인
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }
  
  /// 현재 화면이 가로 모드인지 확인
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
  
  /// 현재 화면이 세로 모드인지 확인
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  // === 화면 크기별 값 반환 ===
  
  /// 화면 크기에 따른 값 반환
  static T getValueForScreenSize<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
  
  /// 방향에 따른 값 반환
  static T getValueForOrientation<T>({
    required BuildContext context,
    required T portrait,
    required T landscape,
  }) {
    return isLandscape(context) ? landscape : portrait;
  }

  // === 패딩/마진 값 ===
  
  /// 화면 크기별 기본 패딩
  static EdgeInsets getScreenPadding(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: const EdgeInsets.all(16.0),
      tablet: const EdgeInsets.all(24.0),
      desktop: const EdgeInsets.all(32.0),
    );
  }
  
  /// 화면 크기별 카드 패딩
  static EdgeInsets getCardPadding(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: const EdgeInsets.all(16.0),
      tablet: const EdgeInsets.all(20.0),
      desktop: const EdgeInsets.all(24.0),
    );
  }
  
  /// 화면 크기별 섹션 간격
  static double getSectionSpacing(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 24.0,
      tablet: 32.0,
      desktop: 40.0,
    );
  }

  // === 폰트 크기 ===
  
  /// 화면 크기별 제목 폰트 크기
  static double getTitleFontSize(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 28.0,
      tablet: 32.0,
      desktop: 36.0,
    );
  }
  
  /// 화면 크기별 부제목 폰트 크기
  static double getSubtitleFontSize(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 16.0,
      tablet: 18.0,
      desktop: 20.0,
    );
  }
  
  /// 화면 크기별 본문 폰트 크기
  static double getBodyFontSize(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 14.0,
      tablet: 16.0,
      desktop: 16.0,
    );
  }

  // === 버튼 크기 ===
  
  /// 화면 크기별 버튼 높이
  static double getButtonHeight(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 48.0,
      tablet: 52.0,
      desktop: 56.0,
    );
  }
  
  /// 화면 크기별 버튼 패딩
  static EdgeInsets getButtonPadding(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      tablet: const EdgeInsets.symmetric(horizontal: 28.0, vertical: 14.0),
      desktop: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 16.0),
    );
  }

  // === 카드 및 컨테이너 ===
  
  /// 화면 크기별 카드 최대 너비
  static double getCardMaxWidth(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: double.infinity,
      tablet: 500.0,
      desktop: 600.0,
    );
  }
  
  /// 화면 크기별 모서리 반경
  static double getBorderRadius(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 12.0,
      tablet: 16.0,
      desktop: 20.0,
    );
  }

  // === 아이콘 크기 ===
  
  /// 화면 크기별 메인 아이콘 크기
  static double getMainIconSize(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 80.0,
      tablet: 100.0,
      desktop: 120.0,
    );
  }
  
  /// 화면 크기별 일반 아이콘 크기
  static double getIconSize(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 24.0,
      tablet: 28.0,
      desktop: 32.0,
    );
  }

  // === 레이아웃 헬퍼 ===
  
  /// 화면 크기에 따른 컬럼 수 반환
  static int getColumnCount(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
    );
  }
  
  /// 화면 크기별 최대 컨텐츠 너비
  static double getMaxContentWidth(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: double.infinity,
      tablet: 600.0,
      desktop: 800.0,
    );
  }

  // === 애니메이션 지속시간 ===
  
  /// 화면 크기별 애니메이션 지속시간 (성능 고려)
  static Duration getAnimationDuration(BuildContext context) {
    return getValueForScreenSize(
      context: context,
      mobile: const Duration(milliseconds: 300),
      tablet: const Duration(milliseconds: 350),
      desktop: const Duration(milliseconds: 400),
    );
  }
}
