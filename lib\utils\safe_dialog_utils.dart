import 'package:flutter/material.dart';
import 'logger_utils.dart';
import 'toast_utils.dart';

/// 안전한 다이얼로그 관리 유틸리티
/// 
/// Flutter의 위젯 생명주기를 고려하여 다이얼로그를 안전하게 표시하고 닫는 기능을 제공합니다.
/// `_dependents.isEmpty` 에러를 방지하고 컨텍스트 유효성을 검사합니다.
class SafeDialogUtils {
  static const String _tag = 'SafeDialogUtils';

  /// 안전한 다이얼로그 표시
  /// 
  /// [context]: BuildContext (mounted 상태 확인)
  /// [builder]: 다이얼로그 빌더 함수
  /// [barrierDismissible]: 바깥 영역 터치로 닫기 가능 여부
  /// [barrierColor]: 배경 색상
  /// [useSafeArea]: SafeArea 사용 여부
  /// 
  /// Returns: 다이얼로그 결과 또는 null
  static Future<T?> safeShowDialog<T>({
    required BuildContext context,
    required Widget Function(BuildContext) builder,
    bool barrierDismissible = true,
    Color? barrierColor,
    bool useSafeArea = true,
    String? logContext,
  }) async {
    // 컨텍스트 유효성 검사
    if (!_isContextValid(context)) {
      LoggerUtils.logWarning(
        'Context is not valid for showing dialog${logContext != null ? ': $logContext' : ''}',
        tag: _tag,
      );
      return null;
    }

    try {
      LoggerUtils.logDebug(
        'Showing dialog${logContext != null ? ': $logContext' : ''}',
        tag: _tag,
      );

      return await showDialog<T>(
        context: context,
        builder: builder,
        barrierDismissible: barrierDismissible,
        barrierColor: barrierColor,
        useSafeArea: useSafeArea,
      );
    } catch (e) {
      LoggerUtils.logError(
        'Failed to show dialog${logContext != null ? ': $logContext' : ''}: $e',
        tag: _tag,
        error: e,
      );
      return null;
    }
  }

  /// 안전한 다이얼로그 닫기
  ///
  /// [context]: BuildContext (mounted 상태 확인)
  /// [result]: 반환할 결과값
  ///
  /// Returns: 성공적으로 닫혔는지 여부
  static bool safePopDialog<T>(BuildContext context, [T? result]) {
    // 컨텍스트 유효성 검사
    if (!_isContextValid(context)) {
      LoggerUtils.logWarning('Context is not valid for popping dialog', tag: _tag);
      return false;
    }

    try {
      // Navigator 상태 확인 (try-catch 내부에서)
      if (!Navigator.canPop(context)) {
        LoggerUtils.logWarning('Cannot pop dialog - no route to pop', tag: _tag);
        return false;
      }

      // 다시 한번 컨텍스트 확인 (Navigator.pop 직전)
      if (!context.mounted) {
        LoggerUtils.logWarning('Context became invalid before popping', tag: _tag);
        return false;
      }

      Navigator.of(context).pop(result);
      LoggerUtils.logDebug('Dialog popped successfully', tag: _tag);
      return true;
    } catch (e) {
      LoggerUtils.logError('Failed to pop dialog: $e', tag: _tag, error: e);
      return false;
    }
  }

  /// 안전한 확인 다이얼로그 표시
  /// 
  /// [context]: BuildContext
  /// [title]: 다이얼로그 제목
  /// [content]: 다이얼로그 내용
  /// [confirmLabel]: 확인 버튼 텍스트
  /// [cancelLabel]: 취소 버튼 텍스트
  /// [isDestructive]: 위험한 작업인지 여부 (빨간색 버튼)
  /// 
  /// Returns: 확인(true), 취소(false), 또는 null
  static Future<bool?> safeShowConfirmDialog({
    required BuildContext context,
    required String title,
    required String content,
    String confirmLabel = '확인',
    String cancelLabel = '취소',
    bool isDestructive = false,
    IconData? icon,
  }) {
    bool isDialogClosed = false; // 중복 닫기 방지

    return safeShowDialog<bool>(
      context: context,
      logContext: 'Confirm Dialog: $title',
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: isDestructive ? Colors.red : null,
              ),
              const SizedBox(width: 8),
            ],
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () {
              if (isDialogClosed) return;
              isDialogClosed = true;
              safePopDialog(context, false);
            },
            child: Text(cancelLabel),
          ),
          ElevatedButton(
            onPressed: () {
              if (isDialogClosed) return;
              isDialogClosed = true;
              safePopDialog(context, true);
            },
            style: isDestructive
                ? ElevatedButton.styleFrom(backgroundColor: Colors.red)
                : null,
            child: Text(
              confirmLabel,
              style: isDestructive
                  ? const TextStyle(color: Colors.white)
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  /// 안전한 입력 다이얼로그 표시
  ///
  /// [context]: BuildContext
  /// [title]: 다이얼로그 제목
  /// [hintText]: 입력 필드 힌트
  /// [initialValue]: 초기값
  /// [validator]: 입력값 검증 함수
  ///
  /// Returns: 입력된 텍스트 또는 null
  static Future<String?> safeShowInputDialog({
    required BuildContext context,
    required String title,
    String? hintText,
    String? initialValue,
    String? Function(String?)? validator,
  }) {
    final controller = TextEditingController(text: initialValue);
    bool isDialogClosed = false; // 중복 닫기 방지

    try {
      return safeShowDialog<String>(
        context: context,
        logContext: 'Input Dialog: $title',
        builder: (context) => AlertDialog(
          title: Text(title),
          content: TextField(
            controller: controller,
            decoration: InputDecoration(
              hintText: hintText,
              border: const OutlineInputBorder(),
            ),
            autofocus: true,
            onSubmitted: (value) {
              if (isDialogClosed) return;
              final error = validator?.call(value);
              if (error == null) {
                isDialogClosed = true;
                safePopDialog(context, value);
              } else {
                // 안전한 에러 표시
                safeShowToast(context: context, message: error, isError: true);
              }
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (isDialogClosed) return;
                isDialogClosed = true;
                safePopDialog(context, null);
              },
              child: const Text('취소'),
            ),
            ElevatedButton(
              onPressed: () {
                if (isDialogClosed) return;
                final value = controller.text;
                final error = validator?.call(value);
                if (error != null) {
                  // 안전한 에러 표시
                  safeShowToast(context: context, message: error, isError: true);
                  return;
                }
                isDialogClosed = true;
                safePopDialog(context, value);
              },
              child: const Text('확인'),
            ),
          ],
        ),
      );
    } finally {
      // 다이얼로그가 완전히 닫힌 후에 컨트롤러를 안전하게 dispose
      controller.dispose();
    }
  }

  /// 컨텍스트 유효성 검사
  ///
  /// [context]: 검사할 BuildContext
  ///
  /// Returns: 유효한 컨텍스트인지 여부
  static bool _isContextValid(BuildContext context) {
    try {
      // context.mounted 확인 (Flutter 3.7+)
      if (!context.mounted) {
        return false;
      }

      return true;
    } catch (e) {
      LoggerUtils.logError('Context validation failed: $e', tag: _tag, error: e);
      return false;
    }
  }

  /// 안전한 토스트 메시지 표시
  ///
  /// [context]: BuildContext
  /// [message]: 표시할 메시지
  /// [isError]: 에러 메시지인지 여부
  ///
  /// Returns: 성공적으로 표시되었는지 여부
  static bool safeShowToast({
    required BuildContext context,
    required String message,
    bool isError = false,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!_isContextValid(context)) {
      LoggerUtils.logWarning('Context is not valid for showing toast', tag: _tag);
      return false;
    }

    try {
      if (isError) {
        ToastUtils.showError(context, message, duration: duration);
      } else {
        ToastUtils.showMessage(context, message, duration: duration);
      }
      return true;
    } catch (e) {
      LoggerUtils.logError('Failed to show toast: $e', tag: _tag, error: e);
      return false;
    }
  }

  /// 안전한 페이지 네비게이션
  /// 
  /// [context]: BuildContext
  /// [page]: 이동할 페이지
  /// 
  /// Returns: 네비게이션 결과
  static Future<T?> safePushPage<T>(
    BuildContext context,
    Widget page, {
    String? logContext,
  }) async {
    if (!_isContextValid(context)) {
      LoggerUtils.logWarning(
        'Context is not valid for navigation${logContext != null ? ': $logContext' : ''}',
        tag: _tag,
      );
      return null;
    }

    try {
      return await Navigator.push<T>(
        context,
        MaterialPageRoute(builder: (context) => page),
      );
    } catch (e) {
      LoggerUtils.logError(
        'Failed to navigate${logContext != null ? ': $logContext' : ''}: $e',
        tag: _tag,
        error: e,
      );
      return null;
    }
  }
}
