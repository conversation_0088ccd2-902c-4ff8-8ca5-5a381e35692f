import 'dart:core';

/// SQL 쿼리 보안을 위한 유틸리티 클래스입니다.
/// - LIKE, ORDER BY, WHERE IN, VALUES 등 안전한 쿼리 생성 지원
/// - SQL injection 방지, 입력값 검증, 이스케이프 등 보안 기능 포함
class SqlUtils {
  /// LIKE 검색을 위한 안전한 패턴 생성
  ///
  /// 입력값에서 SQL injection 위험이 있는 문자를 이스케이프 처리합니다.
  /// [input]: 검색어
  /// 반환값: 이스케이프된 LIKE 패턴 문자열
  static String escapeLikePattern(String input) {
    // LIKE 검색에서 특별한 의미를 가지는 문자들을 이스케이프
    return input
        .replaceAll('\\', '\\\\')
        .replaceAll('%', '\\%')
        .replaceAll('_', '\\_')
        .replaceAll('[', '\\[')
        .replaceAll(']', '\\]');
  }

  /// 안전한 LIKE 패턴 생성
  ///
  /// 입력값을 이스케이프 처리하고 와일드카드를 추가합니다.
  /// [input]: 검색어
  /// 반환값: %로 감싼 LIKE 패턴 문자열
  static String createSafeLikePattern(String input) {
    return '%${escapeLikePattern(input)}%';
  }

  /// ORDER BY 절에 사용할 수 있는 허용된 컬럼 이름인지 검증
  ///
  /// SQL injection 방지를 위해 미리 정의된 컬럼 이름만 허용합니다.
  /// [columnName]: 컬럼명
  /// [allowedColumns]: 허용 컬럼 리스트
  /// 반환값: true(허용), false(거부)
  static bool isValidOrderByColumn(
    String columnName,
    List<String> allowedColumns,
  ) {
    return allowedColumns.contains(columnName.toLowerCase());
  }

  /// 안전한 ORDER BY 절 생성
  ///
  /// 허용된 컬럼과 정렬 방향으로만 ORDER BY 절을 생성합니다.
  /// [column]: 컬럼명
  /// [allowedColumns]: 허용 컬럼 리스트
  /// [descending]: 내림차순 여부
  /// 반환값: ORDER BY 절 문자열
  /// 예외: 허용되지 않은 컬럼명 사용 시 ArgumentError
  static String createSafeOrderBy(
    String column,
    List<String> allowedColumns, {
    bool descending = false,
  }) {
    if (!isValidOrderByColumn(column, allowedColumns)) {
      throw ArgumentError('Invalid column name for ORDER BY: $column');
    }
    return '$column ${descending ? 'DESC' : 'ASC'}';
  }

  /// 테이블 이름 검증
  ///
  /// SQL injection 방지를 위해 미리 정의된 테이블 이름만 허용합니다.
  /// [tableName]: 테이블명
  /// [allowedTables]: 허용 테이블 리스트
  /// 반환값: true(허용), false(거부)
  static bool isValidTableName(String tableName, List<String> allowedTables) {
    return allowedTables.contains(tableName.toLowerCase());
  }

  /// 안전한 WHERE IN 절 생성
  ///
  /// 동적인 IN 절을 안전하게 생성합니다.
  /// [column]: 컬럼명
  /// [count]: 값 개수
  /// 반환값: IN 절 문자열
  /// 예외: count <= 0 시 ArgumentError
  static String createSafeWhereIn(String column, int count) {
    if (count <= 0) {
      throw ArgumentError('Count must be greater than 0');
    }
    return '$column IN (${List.filled(count, '?').join(', ')})';
  }

  /// SQL 쿼리에서 사용할 수 있는 값인지 검증
  ///
  /// null을 제외한 기본 데이터 타입만 허용합니다.
  /// [value]: 값
  /// 반환값: true(허용), false(거부)
  static bool isValidSqlValue(dynamic value) {
    if (value == null) return true;
    return value is num || value is String || value is bool;
  }

  /// 배치 작업을 위한 안전한 VALUES 절 생성
  ///
  /// 다중 레코드 삽입 시 안전하게 VALUES 절을 생성합니다.
  /// [rowCount]: 행 개수
  /// [columnCount]: 컬럼 개수
  /// 반환값: VALUES 절 문자열
  /// 예외: rowCount/columnCount <= 0 시 ArgumentError
  static String createBatchValuesClause(int rowCount, int columnCount) {
    if (rowCount <= 0 || columnCount <= 0) {
      throw ArgumentError('Row count and column count must be greater than 0');
    }

    final singleRow = '(${List.filled(columnCount, '?').join(', ')})';
    return List.filled(rowCount, singleRow).join(', ');
  }

  /// ORDER BY 절 검증
  ///
  /// 정렬 방향, 컬럼명 등 허용 범위 내에서만 ORDER BY 절을 생성합니다.
  /// [orderBy]: ORDER BY 문자열
  /// [allowedColumns]: 허용 컬럼 리스트
  /// 반환값: 검증된 ORDER BY 절
  /// 예외: 잘못된 형식/컬럼/방향 사용 시 ArgumentError
  static String validateOrderBy(String orderBy, List<String> allowedColumns) {
    // 정렬 방향 검증
    final validDirections = ['ASC', 'DESC'];

    // 컬럼과 정렬 방향 분리
    final parts = orderBy.trim().split(' ');
    if (parts.isEmpty || parts.length > 2) {
      throw ArgumentError('잘못된 ORDER BY 형식입니다: $orderBy');
    }

    final column = parts[0].toLowerCase();
    final direction = parts.length > 1 ? parts[1].toUpperCase() : 'ASC';

    // 컬럼 이름 검증
    if (!allowedColumns.map((c) => c.toLowerCase()).contains(column)) {
      throw ArgumentError('허용되지 않는 정렬 컬럼입니다: $column');
    }

    // 정렬 방향 검증
    if (!validDirections.contains(direction)) {
      throw ArgumentError('잘못된 정렬 방향입니다: $direction');
    }

    // SQL injection 방지를 위해 검증된 값으로 ORDER BY 절 구성
    return '${escapeSqlIdentifier(column)} $direction';
  }

  /// SQL 식별자(테이블명, 컬럼명 등) 이스케이프
  ///
  /// SQLite에서 큰따옴표로 식별자를 감싸 이스케이프 처리합니다.
  /// [identifier]: 식별자
  /// 반환값: 이스케이프된 식별자
  static String escapeSqlIdentifier(String identifier) {
    // SQLite에서는 큰따옴표로 식별자를 감싸서 이스케이프
    return '"${identifier.replaceAll('"', '""')}"';
  }
}
