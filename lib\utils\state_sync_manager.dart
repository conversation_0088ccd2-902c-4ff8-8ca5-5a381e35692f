import 'dart:async';
import 'logger_utils.dart';

/// 상태 동기화 관리자
/// - 화면 전환 시 상태 동기화
/// - 앱 생명주기별 상태 관리
/// - 백그라운드/포그라운드 전환 시 상태 보존
class StateSyncManager {
  static final StateSyncManager _instance = StateSyncManager._internal();
  factory StateSyncManager() => _instance;
  StateSyncManager._internal();

  final Map<String, dynamic> _stateCache = {};
  final Map<String, DateTime> _stateTimestamps = {};
  final Duration _stateExpiry = const Duration(minutes: 30);
  
  final List<StateSyncListener> _listeners = [];
  final Map<String, StreamController<StateSyncEvent>> _eventControllers = {};
  
  bool _isInBackground = false;
  DateTime? _backgroundStartTime;

  /// 상태 동기화 리스너 등록
  void addListener(StateSyncListener listener) {
    _listeners.add(listener);
  }

  /// 상태 동기화 리스너 제거
  void removeListener(StateSyncListener listener) {
    _listeners.remove(listener);
  }

  /// 상태 캐시 저장 (중복 방지)
  void cacheState(String key, dynamic state) {
    // 동일한 상태가 이미 캐시되어 있으면 스킵
    if (_stateCache.containsKey(key) && _stateCache[key] == state) {
      return;
    }

    _stateCache[key] = state;
    _stateTimestamps[key] = DateTime.now();

    LoggerUtils.logDebug(
      'State cached: $key',
      tag: 'StateSyncManager',
    );
  }

  /// 상태 캐시에서 복원
  T? restoreState<T>(String key) {
    final timestamp = _stateTimestamps[key];
    if (timestamp == null) return null;
    
    if (DateTime.now().difference(timestamp) > _stateExpiry) {
      _stateCache.remove(key);
      _stateTimestamps.remove(key);
      return null;
    }
    
    final state = _stateCache[key];
    if (state is T) {
      LoggerUtils.logDebug(
        'State restored: $key',
        tag: 'StateSyncManager',
      );
      return state;
    }
    
    return null;
  }

  /// 상태 캐시 무효화
  void invalidateState(String key) {
    _stateCache.remove(key);
    _stateTimestamps.remove(key);
    
    LoggerUtils.logDebug(
      'State invalidated: $key',
      tag: 'StateSyncManager',
    );
  }

  /// 모든 상태 캐시 정리
  void clearAllStates() {
    _stateCache.clear();
    _stateTimestamps.clear();
    
    LoggerUtils.logDebug(
      'All states cleared',
      tag: 'StateSyncManager',
    );
  }

  /// 화면 전환 시 상태 동기화
  Future<void> syncStateOnScreenTransition(
    String fromScreen,
    String toScreen,
    Map<String, dynamic>? stateData,
  ) async {
    final event = StateSyncEvent.screenTransition(
      fromScreen: fromScreen,
      toScreen: toScreen,
      stateData: stateData,
    );
    
    await _notifyListeners(event);
    
    LoggerUtils.logDebug(
      'Screen transition sync: $fromScreen -> $toScreen',
      tag: 'StateSyncManager',
    );
  }

  /// 앱 포그라운드 진입 시 상태 복원
  Future<void> onAppForeground() async {
    if (_isInBackground) {
      _isInBackground = false;
      final backgroundDuration = _backgroundStartTime != null 
          ? DateTime.now().difference(_backgroundStartTime!)
          : Duration.zero;
      
      final event = StateSyncEvent.appForeground(
        backgroundDuration: backgroundDuration,
      );
      
      await _notifyListeners(event);
      
      LoggerUtils.logDebug(
        'App foregrounded after ${backgroundDuration.inSeconds}s',
        tag: 'StateSyncManager',
      );
    }
  }

  /// 앱 백그라운드 진입 시 상태 보존
  Future<void> onAppBackground() async {
    if (!_isInBackground) {
      _isInBackground = true;
      _backgroundStartTime = DateTime.now();
      
      final event = StateSyncEvent.appBackground();
      await _notifyListeners(event);
      
      LoggerUtils.logDebug(
        'App backgrounded',
        tag: 'StateSyncManager',
      );
    }
  }

  /// 메모리 부족 시 상태 정리
  Future<void> onMemoryPressure() async {
    final event = StateSyncEvent.memoryPressure();
    await _notifyListeners(event);
    
    // 오래된 상태 정리
    _cleanupExpiredStates();
    
    LoggerUtils.logDebug(
      'Memory pressure handled',
      tag: 'StateSyncManager',
    );
  }

  /// 만료된 상태 정리
  void _cleanupExpiredStates() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _stateTimestamps.entries) {
      if (now.difference(entry.value) > _stateExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _stateCache.remove(key);
      _stateTimestamps.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      LoggerUtils.logDebug(
        'Cleaned up ${expiredKeys.length} expired states',
        tag: 'StateSyncManager',
      );
    }
  }

  /// 리스너들에게 이벤트 알림
  Future<void> _notifyListeners(StateSyncEvent event) async {
    for (final listener in _listeners) {
      try {
        await listener.onStateSyncEvent(event);
      } catch (e) {
        LoggerUtils.logError(
          'Listener error: $e',
          error: e,
          tag: 'StateSyncManager',
        );
      }
    }
  }

  /// 상태 동기화 이벤트 스트림 구독
  Stream<StateSyncEvent> subscribeToEvents(String eventType) {
    if (!_eventControllers.containsKey(eventType)) {
      _eventControllers[eventType] = StreamController<StateSyncEvent>.broadcast();
    }
    return _eventControllers[eventType]!.stream;
  }

  /// 상태 동기화 통계
  Map<String, dynamic> getStats() {
    return {
      'cachedStates': _stateCache.length,
      'listeners': _listeners.length,
      'isInBackground': _isInBackground,
      'backgroundStartTime': _backgroundStartTime,
      'cacheKeys': _stateCache.keys.toList(),
    };
  }

  /// 리소스 정리
  void dispose() {
    // StreamController들 안전하게 정리
    for (final entry in _eventControllers.entries) {
      try {
        if (!entry.value.isClosed) {
          entry.value.close();
        }
      } catch (e) {
        // 이미 닫힌 컨트롤러는 무시
        LoggerUtils.logWarning(
          'Failed to close StreamController for ${entry.key}: $e',
          tag: 'StateSyncManager',
        );
      }
    }
    _eventControllers.clear();
    _listeners.clear();
    clearAllStates();

    LoggerUtils.logDebug(
      'StateSyncManager disposed - all resources cleaned up',
      tag: 'StateSyncManager',
    );
  }
}

/// 상태 동기화 이벤트
abstract class StateSyncEvent {
  final DateTime timestamp;
  final String eventType;

  StateSyncEvent(this.eventType) : timestamp = DateTime.now();

  /// 화면 전환 이벤트
  factory StateSyncEvent.screenTransition({
    required String fromScreen,
    required String toScreen,
    Map<String, dynamic>? stateData,
  }) = ScreenTransitionEvent;

  /// 앱 포그라운드 이벤트
  factory StateSyncEvent.appForeground({Duration? backgroundDuration}) = AppForegroundEvent;

  /// 앱 백그라운드 이벤트
  factory StateSyncEvent.appBackground() = AppBackgroundEvent;

  /// 메모리 부족 이벤트
  factory StateSyncEvent.memoryPressure() = MemoryPressureEvent;
}

/// 화면 전환 이벤트
class ScreenTransitionEvent extends StateSyncEvent {
  final String fromScreen;
  final String toScreen;
  final Map<String, dynamic>? stateData;

  ScreenTransitionEvent({
    required this.fromScreen,
    required this.toScreen,
    this.stateData,
  }) : super('screen_transition');
}

/// 앱 포그라운드 이벤트
class AppForegroundEvent extends StateSyncEvent {
  final Duration? backgroundDuration;

  AppForegroundEvent({this.backgroundDuration}) : super('app_foreground');
}

/// 앱 백그라운드 이벤트
class AppBackgroundEvent extends StateSyncEvent {
  AppBackgroundEvent() : super('app_background');
}

/// 메모리 부족 이벤트
class MemoryPressureEvent extends StateSyncEvent {
  MemoryPressureEvent() : super('memory_pressure');
}

/// 상태 동기화 리스너 인터페이스
abstract class StateSyncListener {
  Future<void> onStateSyncEvent(StateSyncEvent event);
}

/// Provider 상태 동기화 Mixin
mixin ProviderStateSync<T> {
  final StateSyncManager _syncManager = StateSyncManager();
  String? _currentScreen;

  /// 화면 설정
  void setCurrentScreen(String screenName) {
    _currentScreen = screenName;
  }

  /// 상태 동기화
  void syncState(String key, T state) {
    _syncManager.cacheState(key, state);
  }

  /// 상태 복원
  T? restoreState(String key) {
    return _syncManager.restoreState<T>(key);
  }

  /// 화면 전환 시 상태 동기화
  Future<void> onScreenTransition(String toScreen, Map<String, dynamic>? stateData) async {
    if (_currentScreen != null) {
      await _syncManager.syncStateOnScreenTransition(
        _currentScreen!,
        toScreen,
        stateData,
      );
    }
    _currentScreen = toScreen;
  }
}

/// 상태 무결성 검증 시스템
class StateIntegrityValidator {
  /// 상태 무결성 검증
  static bool validateState<T>(T state, List<ValidationRule<T>> rules) {
    for (final rule in rules) {
      if (!rule.validate(state)) {
        LoggerUtils.logWarning(
          'State validation failed: ${rule.description}',
          tag: 'StateIntegrityValidator',
        );
        return false;
      }
    }
    return true;
  }

  /// 상태 복구 시도
  static T? attemptStateRecovery<T>(T? corruptedState, T Function() fallbackFactory) {
    if (corruptedState == null) {
      return fallbackFactory();
    }
    
    // 기본적인 상태 복구 로직
    try {
      // 상태가 유효한지 확인
      if (corruptedState is Map) {
        if (corruptedState.isEmpty) {
          return fallbackFactory();
        }
      }
      
      return corruptedState;
    } catch (e) {
      LoggerUtils.logError(
        'State recovery failed: $e',
        error: e,
        tag: 'StateIntegrityValidator',
      );
      return fallbackFactory();
    }
  }
}

/// 상태 검증 규칙
abstract class ValidationRule<T> {
  final String description;
  
  ValidationRule(this.description);
  
  bool validate(T state);
}

/// 상태가 null이 아닌지 검증
class NotNullValidationRule<T> extends ValidationRule<T> {
  NotNullValidationRule() : super('State must not be null');
  
  @override
  bool validate(T state) => state != null;
}

/// 상태가 빈 컬렉션이 아닌지 검증
class NotEmptyValidationRule<T> extends ValidationRule<T> {
  NotEmptyValidationRule() : super('State must not be empty');
  
  @override
  bool validate(T state) {
    if (state is List) return state.isNotEmpty;
    if (state is Map) return state.isNotEmpty;
    if (state is Set) return state.isNotEmpty;
    return true;
  }
} 
