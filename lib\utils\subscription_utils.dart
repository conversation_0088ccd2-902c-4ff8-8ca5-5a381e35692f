/// 바라 부스 매니저 - 구독 관련 유틸리티
///
/// 구독 상태에 따른 기능 제한을 확인하고 처리하는 유틸리티입니다.
/// - 기능 사용 가능 여부 확인
/// - 제한 다이얼로그 표시
/// - 업그레이드 안내
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/subscription_plan.dart';
import '../providers/subscription_provider.dart';
import '../utils/toast_utils.dart';
import '../utils/logger_utils.dart';
import '../screens/subscription/subscription_plans_screen.dart';

/// 구독 관련 유틸리티 클래스
class SubscriptionUtils {
  static const String _tag = 'SubscriptionUtils';

  /// 특정 기능 사용 가능 여부 확인 (다이얼로그 없이)
  static Future<bool> hasFeature(WidgetRef ref, String featureName) async {
    try {
      return await ref.read(featureAccessProvider(featureName).future);
    } catch (e) {
      LoggerUtils.logError('기능 사용 가능 여부 확인 실패: $featureName', tag: _tag, error: e);
      return false;
    }
  }

  /// 특정 기능 사용 가능 여부 확인
  static Future<bool> checkFeatureAccess({
    required WidgetRef ref,
    required BuildContext context,
    required String featureName,
    String? customMessage,
    bool showDialog = true,
  }) async {
    try {
      final hasAccess = await ref.read(featureAccessProvider(featureName).future);

      if (!hasAccess && showDialog) {
        await _showFeatureRestrictedDialog(
          context: context,
          featureName: featureName,
          customMessage: customMessage,
        );
      }

      return hasAccess;
    } catch (e) {
      LoggerUtils.logError('기능 접근 권한 확인 실패: $featureName', tag: _tag, error: e);
      return false;
    }
  }

  /// 행사 생성 가능 여부 확인
  static Future<bool> checkEventCreationLimit({
    required WidgetRef ref,
    required BuildContext context,
    required int currentEventCount,
    bool showDialog = true,
  }) async {
    try {
      final canCreate = await ref.read(canCreateEventProvider(currentEventCount).future);
      
      if (!canCreate && showDialog) {
        await _showEventLimitDialog(context: context);
      }
      
      return canCreate;
    } catch (e) {
      LoggerUtils.logError('행사 생성 제한 확인 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 상품 생성 가능 여부 확인
  static Future<bool> checkProductCreationLimit({
    required WidgetRef ref,
    required BuildContext context,
    required int currentProductCount,
    bool showDialog = true,
  }) async {
    try {
      final canCreate = await ref.read(canCreateProductProvider(currentProductCount).future);
      
      if (!canCreate && showDialog) {
        await _showProductLimitDialog(context: context);
      }
      
      return canCreate;
    } catch (e) {
      LoggerUtils.logError('상품 생성 제한 확인 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 서버 연동 기능 사용 가능 여부 확인
  static Future<bool> checkServerSyncAccess({
    required WidgetRef ref,
    required BuildContext context,
    bool showDialog = true,
  }) async {
    return await checkFeatureAccess(
      ref: ref,
      context: context,
      featureName: 'serverSync',
      customMessage: '서버 연동 기능은 플러스 플랜에서만 사용할 수 있습니다.\n무료 플랜에서는 로컬 저장만 가능합니다.',
      showDialog: showDialog,
    );
  }

  /// 기능 제한 다이얼로그 표시
  static Future<void> _showFeatureRestrictedDialog({
    required BuildContext context,
    required String featureName,
    String? customMessage,
  }) async {
    final featureNames = {
      'setDiscount': '세트 할인',
      'service': '서비스',
      'serverSync': '서버 연동',
      'sellerManagement': '판매자별 관리',
      'excelExport': '엑셀 내보내기',
      'pdfExport': 'PDF 내보내기',
      'export': 'PDF/엑셀 내보내기',
      'advancedStats': '고급 통계',
    };

    final displayName = featureNames[featureName] ?? featureName;

    // 기능별 맞춤 메시지
    String message;
    if (customMessage != null) {
      message = customMessage;
    } else {
      switch (featureName) {
        case 'setDiscount':
        case 'advancedStats':
          message = '$displayName 기능은 플러스 플랜 이상에서 사용할 수 있습니다.';
          break;
        case 'service':
          message = '$displayName 기능은 모든 플랜에서 사용할 수 있습니다.';
          break;
        case 'serverSync':
        case 'sellerManagement':
        case 'pdfExport':
          message = '$displayName 기능은 플러스 플랜 이상에서 사용할 수 있습니다.';
          break;
        case 'excelExport':
          message = '$displayName 기능은 플러스 플랜 이상에서 사용할 수 있습니다.';
          break;
        default:
          message = '$displayName 기능은 플러스 플랜 이상에서 사용할 수 있습니다.';
      }
    }

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.star, color: Colors.amber),
            SizedBox(width: 8),
            Text(_getUpgradeTitle(featureName)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            SizedBox(height: 16),
            Text(
              _getUpgradeBenefits(featureName),
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            ..._getFeatureList(featureName),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('확인'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionPlansScreen(),
                ),
              );
            },
            child: Text('업그레이드'),
          ),
        ],
      ),
    );
  }

  /// 행사 제한 다이얼로그 표시
  static Future<void> _showEventLimitDialog({
    required BuildContext context,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('행사 등록 제한'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('무료 플랜에서는 행사를 1개까지만 등록할 수 있습니다.'),
            SizedBox(height: 16),
            Text('더 많은 행사를 관리하려면:'),
            SizedBox(height: 8),
            Text('• 기존 행사를 리셋하여 새로 시작하거나'),
            Text('• 플러스 플랜으로 업그레이드하세요'),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💡 행사 리셋이란?',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '행사는 유지하되 모든 데이터(상품, 판매기록 등)를 삭제하여 새로 시작하는 기능입니다.',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('확인'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionPlansScreen(),
                ),
              );
            },
            child: Text('업그레이드'),
          ),
        ],
      ),
    );
  }

  /// 상품 제한 다이얼로그 표시
  static Future<void> _showProductLimitDialog({
    required BuildContext context,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('상품 등록 제한'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('무료 플랜에서는 상품을 30개까지만 등록할 수 있습니다.'),
            SizedBox(height: 16),
            Text('더 많은 상품을 등록하려면:'),
            SizedBox(height: 8),
            Text('• 기존 상품을 삭제하거나'),
            Text('• 플러스 플랜으로 업그레이드하세요'),
            SizedBox(height: 16),
            Text(
              '플러스 플랜에서는 상품을 무제한으로 등록할 수 있습니다.',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('확인'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionPlansScreen(),
                ),
              );
            },
            child: Text('업그레이드'),
          ),
        ],
      ),
    );
  }

  /// 현재 구독 플랜 정보 가져오기 (동기)
  static SubscriptionPlanType getCurrentPlanTypeSync(WidgetRef ref) {
    final asyncValue = ref.read(subscriptionNotifierProvider);
    return asyncValue.when(
      data: (planType) => planType,
      loading: () => SubscriptionPlanType.free,
      error: (_, __) => SubscriptionPlanType.free,
    );
  }

  /// 플러스 플랜 사용자인지 확인 (동기)
  static bool isPlusUserSync(WidgetRef ref) {
    return getCurrentPlanTypeSync(ref) == SubscriptionPlanType.plus;
  }

  /// 무료 플랜 사용자인지 확인 (동기)
  static bool isFreeUserSync(WidgetRef ref) {
    return getCurrentPlanTypeSync(ref) == SubscriptionPlanType.free;
  }

  /// 토스트로 간단한 제한 메시지 표시
  static void showFeatureRestrictedToast(BuildContext context, String featureName) {
    final featureNames = {
      'setDiscount': '세트 할인',
      'service': '서비스',
      'serverSync': '서버 연동',
      'sellerManagement': '판매자별 관리',
      'excelExport': '엑셀 내보내기',
      'pdfExport': 'PDF 내보내기',
      'export': 'PDF/엑셀 내보내기',
      'advancedStats': '고급 통계',
    };

    final displayName = featureNames[featureName] ?? featureName;
    String message;
    switch (featureName) {
      case 'setDiscount':
      case 'advancedStats':
      case 'excelExport':
      case 'pdfExport':
      case 'sellerManagement':
        message = '$displayName 기능은 플러스 플랜 이상에서 사용할 수 있습니다';
        break;
      case 'service':
        message = '$displayName 기능은 모든 플랜에서 사용할 수 있습니다';
        break;
      default:
        message = '$displayName 기능은 플러스 플랜 이상에서 사용할 수 있습니다';
    }
    ToastUtils.showWarning(context, message);
  }

  /// 업그레이드 제목 가져오기
  static String _getUpgradeTitle(String featureName) {
    switch (featureName) {
      case 'setDiscount':
      case 'service':
      case 'advancedStats':
      case 'excelExport':
        return '플러스 플랜 필요';
      default:
        return '플러스 플랜 필요';
    }
  }

  /// 업그레이드 혜택 메시지 가져오기
  static String _getUpgradeBenefits(String featureName) {
    switch (featureName) {
      case 'setDiscount':
      case 'service':
      case 'advancedStats':
      case 'excelExport':
        return '플러스 플랜으로 업그레이드하면:';
      default:
        return '플러스 플랜으로 업그레이드하면:';
    }
  }

  /// 기능 목록 가져오기
  static List<Widget> _getFeatureList(String featureName) {
    switch (featureName) {
      case 'setDiscount':
      case 'service':
      case 'advancedStats':
      case 'excelExport':
        return [
          Text('• 무제한 행사 및 상품 등록'),
          Text('• 세트 할인 및 서비스 기능'),
          Text('• 고급 통계 및 분석'),
          Text('• 엑셀 내보내기'),
          Text('• 로컬에서 모든 기능 사용'),
        ];
      default:
        return [
          Text('• 무제한 행사 및 상품 등록'),
          Text('• 세트 할인 및 서비스 기능'),
          Text('• 판매자별 관리'),
          Text('• PDF/엑셀 내보내기'),
          Text('• 고급 통계 및 분석'),
          Text('• 로컬에서 모든 기능 사용'),
        ];
    }
  }
}
