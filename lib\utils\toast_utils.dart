import 'dart:async';
import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'logger_utils.dart';

/// 토스트/스낵바/피드백 메시지 표시를 지원하는 유틸리티 클래스입니다.
/// - 성공/오류/정보/경고/커스텀 메시지, 액션 버튼, 아이콘 등 지원
/// - riverpod 3.x Provider/Repository와 연동, 사용자 피드백/UX 개선 목적
/// - 상단 오버레이 기반으로 FAB와 하단 네비게이션을 방해하지 않는 토스트 메시지 표시
class ToastUtils {
  static OverlayEntry? _currentOverlay;
  /// 성공 메시지 표시
  static void showSuccess(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.success, // 성공 색상으로 텍스트
      icon: Icons.check_circle_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 에러 메시지 표시
  static void showError(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.error, // 에러 색상으로 텍스트
      icon: Icons.error_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 정보 메시지 표시
  static void showInfo(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.info, // 정보 색상으로 텍스트
      icon: Icons.info_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 경고 메시지 표시
  static void showWarning(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.warning, // 경고 색상으로 텍스트
      icon: Icons.warning_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 일반 메시지 표시
  static void showMessage(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.onSurface, // 기본 텍스트 색상
      icon: Icons.message_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 현재 표시 중인 토스트 메시지 해제
  static void dismiss(BuildContext context) {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }

  /// 기존 호환성을 위한 일반 토스트 메서드
  static void showToast(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    _showTopToast(
      context,
      message,
      backgroundColor: AppColors.surface, // 연한 회색 배경
      foregroundColor: AppColors.onSurface, // 기본 텍스트 색상
      icon: Icons.message_rounded,
      duration: duration,
    );
  }

  /// 기존 호환성을 위한 Duration 상수들
  static const Duration shortDuration = Duration(seconds: 2);
  static const Duration longDuration = Duration(seconds: 3);

  /// 상단 오버레이 토스트 표시 (FAB와 하단 네비게이션 방해 안함)
  static void _showTopToast(
    BuildContext context,
    String message, {
    IconData? icon,
    Color? backgroundColor,
    Color? foregroundColor,
    Duration? duration,
    String? actionLabel,
    VoidCallback? onAction,
    bool? dismissible,
  }) {
    try {
      // context 유효성 체크
      if (!context.mounted) return;

      // 기존 오버레이 제거
      _currentOverlay?.remove();
      _currentOverlay = null;

      // 오버레이 엔트리 생성
      _currentOverlay = OverlayEntry(
        builder: (context) => _ToastWidget(
          message: message,
          icon: icon,
          backgroundColor: backgroundColor ?? AppColors.surface, // 연한 회색 배경
          foregroundColor: foregroundColor ?? AppColors.onSurface, // 적절한 텍스트 색상
          actionLabel: actionLabel,
          onAction: onAction,
          duration: duration ?? const Duration(seconds: 3),
          onDismiss: () {
            _currentOverlay?.remove();
            _currentOverlay = null;
          },
        ),
      );

      // 오버레이에 추가
      Overlay.of(context).insert(_currentOverlay!);

    } catch (e) {
      // 에러가 발생해도 앱이 크래시되지 않도록 처리
      LoggerUtils.logError('ToastUtils._showTopToast error: $e', tag: 'ToastUtils', error: e);
    }
  }
}

/// 상단 토스트 위젯
class _ToastWidget extends StatefulWidget {
  final String message;
  final IconData? icon;
  final Color backgroundColor;
  final Color foregroundColor;
  final String? actionLabel;
  final VoidCallback? onAction;
  final VoidCallback onDismiss;
  final Duration duration;

  const _ToastWidget({
    required this.message,
    this.icon,
    required this.backgroundColor,
    required this.foregroundColor,
    this.actionLabel,
    this.onAction,
    required this.onDismiss,
    required this.duration,
  });

  @override
  State<_ToastWidget> createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<_ToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  Timer? _autoRemoveTimer;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.forward();

    // 자동 제거 타이머 (페이드아웃 애니메이션 포함)
    _autoRemoveTimer = Timer(widget.duration, () {
      if (mounted) {
        _dismissWithAnimation();
      }
    });
  }

  /// 터치 시 페이드아웃 애니메이션과 함께 토스트 제거
  void _dismissWithAnimation() async {
    if (_controller.isAnimating) return; // 이미 애니메이션 중이면 무시
    
    // 자동 제거 타이머 취소 (중복 실행 방지)
    _autoRemoveTimer?.cancel();
    
    await _controller.reverse(); // 페이드아웃 애니메이션
    widget.onDismiss(); // 애니메이션 완료 후 오버레이 제거
  }

  @override
  void dispose() {
    _autoRemoveTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final bottomPadding = mediaQuery.padding.bottom;
  // Use TextScaler instead of deprecated textScaleFactor
  final textScaler = mediaQuery.textScaler; // TextScaler
  final textScaleFactor = textScaler.scale(1.0); // normalized scale value
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    
    // UI 요소들과의 고정 관계 계산 (세로/가로 모드에서 일관성 유지)
    final customBottomAppBarHeight = 60.0; // main.dart의 실제 값
    final fabSize = 56.0; // Material Design 표준 FAB 크기
    final fabTranslateY = 8.0; // Transform.translate offset
    final baseMargin = 24.0; // 기본 여백 (POS 버튼과 적당한 간격)
    
    // FAB 위쪽 부분 계산 (centerDocked 고려)
    final fabTopOffset = (fabSize / 2) - fabTranslateY; // 20.0
    
    // 기본 거리 계산 (UI 요소들과의 고정 관계)
    final baseDistance = customBottomAppBarHeight + fabTopOffset + baseMargin; // 96.0
    
    // 동적 조정 (시스템/접근성 고려만)
  final textScaleAdjustment = (textScaleFactor - 1.0) * 8.0; // 접근성 (TextScaler 기반)
    final densityAdjustment = devicePixelRatio > 3.0 ? 4.0 : 0.0; // 극단적 해상도
    
    // 최종 계산: 기본 거리 + 동적 조정 + 시스템 여백
    final bottomDistance = baseDistance + textScaleAdjustment + densityAdjustment + bottomPadding;

    return Positioned(
      bottom: bottomDistance,
      left: 16,
      right: 16,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: GestureDetector(
          onTap: _dismissWithAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.primarySeed, // 웜 테라코타 테두리
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.foregroundColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      widget.message,
                      style: TextStyle(
                        color: widget.foregroundColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (widget.actionLabel != null && widget.onAction != null) ...[
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        widget.onAction?.call();
                        widget.onDismiss();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: widget.foregroundColor,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                      ),
                      child: Text(widget.actionLabel!),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
