/// 검증 결과를 담는 클래스 - 강화됨
class ValidationResult {
  final bool isValid;
  final Map<String, String> errors;
  final List<String> warnings;
  final Map<String, dynamic> metadata;

  ValidationResult({
    required this.isValid,
    required this.errors,
    this.warnings = const [],
    this.metadata = const {},
  });

  /// 특정 필드의 에러 메시지 반환
  String? getError(String field) {
    return errors[field];
  }

  /// 첫 번째 에러 메시지 반환
  String? get firstError {
    return errors.values.isNotEmpty ? errors.values.first : null;
  }

  /// 모든 에러 메시지를 줄바꿈으로 연결하여 반환
  String get allErrors {
    return errors.values.join('\n');
  }

  /// 경고 메시지들을 포함한 전체 메시지
  String get allMessages {
    final allMessages = <String>[];
    allMessages.addAll(errors.values);
    allMessages.addAll(warnings);
    return allMessages.join('\n');
  }

  /// 특정 심각도 이상의 메시지만 반환
  List<String> getMessagesBySeverity(ValidationSeverity severity) {
    switch (severity) {
      case ValidationSeverity.error:
        return errors.values.toList();
      case ValidationSeverity.warning:
        return warnings;
      case ValidationSeverity.all:
        return [...errors.values, ...warnings];
    }
  }

  /// 검증 결과를 JSON으로 변환
  Map<String, dynamic> toJson() {
    return {
      'isValid': isValid,
      'errors': errors,
      'warnings': warnings,
      'metadata': metadata,
    };
  }

  /// JSON에서 검증 결과 생성
  factory ValidationResult.fromJson(Map<String, dynamic> json) {
    return ValidationResult(
      isValid: json['isValid'] ?? false,
      errors: Map<String, String>.from(json['errors'] ?? {}),
      warnings: List<String>.from(json['warnings'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// 두 검증 결과를 병합
  ValidationResult merge(ValidationResult other) {
    return ValidationResult(
      isValid: isValid && other.isValid,
      errors: {...errors, ...other.errors},
      warnings: [...warnings, ...other.warnings],
      metadata: {...metadata, ...other.metadata},
    );
  }
}

/// 동적 검증 규칙을 정의하는 클래스
class ValidationRule {
  final bool Function(Map<String, dynamic> data) condition;
  final String? Function(dynamic value) validator;
  final String description;

  const ValidationRule({
    required this.condition,
    required this.validator,
    required this.description,
  });
}

/// 검증 메시지의 심각도를 나타내는 열거형
enum ValidationSeverity { error, warning, all } 
