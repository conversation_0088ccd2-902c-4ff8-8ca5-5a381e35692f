/// 종합 검증 유틸리티 클래스들을 포함합니다.
/// - InputValidators: UI 입력 데이터 검증 (41개 메서드)
/// - SecurityValidators: 보안 위협 감지 (17개 메서드)
/// 
/// 기존 input_validators.dart + security_validators.dart 통합

/// 입력 데이터 검증을 지원하는 유틸리티 클래스입니다.
/// - 각종 필드별 검증 메서드 제공
/// - 비즈니스 규칙에 맞는 검증 로직 포함
/// - 사용자 친화적인 에러 메시지 제공
class InputValidators {
  InputValidators._();

  // 정규표현식 패턴들 - 기존 input_validators.dart와 동일
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  static final RegExp _phoneRegex = RegExp(
    r'^01[0-9]-?[0-9]{3,4}-?[0-9]{4}$',
  );

  static final RegExp _numberPattern = RegExp(r'^[0-9]+$');

  static final RegExp _alphanumericPattern = RegExp(r'^[a-zA-Z0-9]+$');

  static final RegExp _koreanNamePattern = RegExp(r'^[가-힣]{2,4}$');

  static final RegExp _businessNumberPattern = RegExp(r'^\d{3}-\d{2}-\d{5}$');

  static final RegExp _strongPasswordPattern = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );

  static final RegExp _postalCodeRegex = RegExp(r'^\d{5}$');

  static final RegExp _urlRegex = RegExp(
    r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
  );

  // 비밀번호 검증용 정규식들
  static final RegExp _passwordUppercase = RegExp(r'[A-Z]');
  static final RegExp _passwordLowercase = RegExp(r'[a-z]');
  static final RegExp _passwordNumber = RegExp(r'[0-9]');
  static final RegExp _passwordSpecial = RegExp(r'[!@#$%^&*(),.?":{}|<>]');

  // ============ 기본 검증 메서드들 ============

  /// 필수 입력 검증 - 기존 호환성 유지
  static String? validateRequired(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? '값'}을 입력해주세요.';
    }
    return null;
  }

  /// 길이 검증
  static String? validateLength(String? value, {
    int? minLength,
    int? maxLength,
    String? fieldName,
  }) {
    if (value == null) return null;

    final length = value.length;
    final field = fieldName ?? '입력값';

    if (minLength != null && length < minLength) {
      return '$field은(는) 최소 $minLength자 이상이어야 합니다.';
    }

    if (maxLength != null && length > maxLength) {
      return '$field은(는) 최대 $maxLength자 이하여야 합니다.';
    }

    return null;
  }

  /// 숫자만 포함 검증
  static String? validateNumeric(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;

    if (!_numberPattern.hasMatch(value)) {
      return fieldName != null ? '$fieldName은(는) 숫자만 입력 가능합니다.' : '숫자만 입력 가능합니다.';
    }
    return null;
  }

  /// 알파벳만 포함 검증
  static String? validateAlphabetic(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;

    if (!RegExp(r'^[a-zA-Z]+$').hasMatch(value)) {
      return fieldName != null ? '$fieldName은(는) 알파벳만 입력 가능합니다.' : '알파벳만 입력 가능합니다.';
    }
    return null;
  }

  /// 알파벳과 숫자만 포함 검증
  static String? validateAlphanumeric(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;

    if (!_alphanumericPattern.hasMatch(value)) {
      return fieldName != null ? '$fieldName은(는) 알파벳과 숫자만 입력 가능합니다.' : '알파벳과 숫자만 입력 가능합니다.';
    }
    return null;
  }

  // ============ 특수 포맷 검증 메서드들 ============

  /// 이메일 형식 검증
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) return null;

    if (!_emailRegex.hasMatch(email)) {
      return '올바른 이메일 형식이 아닙니다.';
    }
    return null;
  }

  /// 휴대폰 번호 형식 검증
  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) return null;

    // 하이픈 제거 후 검증
    final cleanPhone = phone.replaceAll('-', '');
    if (!_phoneRegex.hasMatch(cleanPhone)) {
      return '올바른 휴대폰 번호 형식이 아닙니다. (예: 010-1234-5678)';
    }
    return null;
  }

  /// 우편번호 형식 검증
  static String? validatePostalCode(String? postalCode) {
    if (postalCode == null || postalCode.isEmpty) return null;

    if (!_postalCodeRegex.hasMatch(postalCode)) {
      return '올바른 우편번호 형식이 아닙니다. (5자리 숫자)';
    }
    return null;
  }

  /// URL 형식 검증
  static String? validateUrl(String? url) {
    if (url == null || url.isEmpty) return null;

    if (!_urlRegex.hasMatch(url)) {
      return '올바른 URL 형식이 아닙니다.';
    }
    return null;
  }

  // ============ 비밀번호 검증 메서드들 ============

  /// 비밀번호 강도 검증
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) return null;

    if (password.length < 8) {
      return '비밀번호는 최소 8자 이상이어야 합니다.';
    }

    if (password.length > 50) {
      return '비밀번호는 최대 50자 이하여야 합니다.';
    }

    if (!_passwordUppercase.hasMatch(password)) {
      return '비밀번호에 대문자가 포함되어야 합니다.';
    }

    if (!_passwordLowercase.hasMatch(password)) {
      return '비밀번호에 소문자가 포함되어야 합니다.';
    }

    if (!_passwordNumber.hasMatch(password)) {
      return '비밀번호에 숫자가 포함되어야 합니다.';
    }

    if (!_passwordSpecial.hasMatch(password)) {
      return '비밀번호에 특수문자가 포함되어야 합니다.';
    }

    return null;
  }

  /// 비밀번호 확인 검증
  static String? validatePasswordConfirm(String? password, String? confirmPassword) {
    if (password != confirmPassword) {
      return '비밀번호가 일치하지 않습니다.';
    }
    return null;
  }

  // ============ 비즈니스 로직 검증 메서드들 ============

  /// 금액 검증
  static String? validateAmount(String? amount, {
    double? minAmount,
    double? maxAmount,
  }) {
    if (amount == null || amount.isEmpty) return null;

    final parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) {
      return '올바른 금액 형식이 아닙니다.';
    }

    if (parsedAmount < 0) {
      return '금액은 0 이상이어야 합니다.';
    }

    if (minAmount != null && parsedAmount < minAmount) {
      return '최소 금액은 ${minAmount.toStringAsFixed(0)}원입니다.';
    }

    if (maxAmount != null && parsedAmount > maxAmount) {
      return '최대 금액은 ${maxAmount.toStringAsFixed(0)}원입니다.';
    }

    return null;
  }

  /// 수량 검증
  static String? validateQuantity(String? quantity, {
    int? minQuantity,
    int? maxQuantity,
  }) {
    if (quantity == null || quantity.isEmpty) return null;

    final parsedQuantity = int.tryParse(quantity);
    if (parsedQuantity == null) {
      return '올바른 수량 형식이 아닙니다.';
    }

    if (parsedQuantity <= 0) {
      return '수량은 1 이상이어야 합니다.';
    }

    if (minQuantity != null && parsedQuantity < minQuantity) {
      return '최소 수량은 $minQuantity개입니다.';
    }

    if (maxQuantity != null && parsedQuantity > maxQuantity) {
      return '최대 수량은 $maxQuantity개입니다.';
    }

    return null;
  }

  /// 날짜 형식 검증 (YYYY-MM-DD)
  static String? validateDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;

    try {
      final date = DateTime.parse(dateString);
      // 미래 날짜 제한 (필요시)
      if (date.isAfter(DateTime.now().add(const Duration(days: 365)))) {
        return '너무 먼 미래의 날짜입니다.';
      }
      return null;
    } catch (e) {
      return '올바른 날짜 형식이 아닙니다. (YYYY-MM-DD)';
    }
  }

  // ============ 추가 검증 메서드들 ============

  /// 이름 검증 (한글, 영문만 허용)
  static String? validateName(String? name) {
    if (name == null || name.isEmpty) return null;

    if (name.length < 2 || name.length > 20) {
      return '이름은 2자 이상 20자 이하여야 합니다.';
    }

    if (!RegExp(r'^[가-힣a-zA-Z\s]+$').hasMatch(name)) {
      return '이름은 한글 또는 영문만 입력 가능합니다.';
    }

    return null;
  }

  /// 닉네임 검증
  static String? validateNickname(String? nickname) {
    if (nickname == null || nickname.isEmpty) return null;

    if (nickname.length < 2 || nickname.length > 15) {
      return '닉네임은 2자 이상 15자 이하여야 합니다.';
    }

    if (!RegExp(r'^[가-힣a-zA-Z0-9_]+$').hasMatch(nickname)) {
      return '닉네임은 한글, 영문, 숫자, 언더스코어만 사용 가능합니다.';
    }

    // 금지어 검사 (예시)
    final forbiddenWords = ['admin', 'administrator', 'root', '관리자'];
    for (final word in forbiddenWords) {
      if (nickname.toLowerCase().contains(word.toLowerCase())) {
        return '사용할 수 없는 닉네임입니다.';
      }
    }

    return null;
  }

  // ============ Boolean 검증 메서드들 ============

  /// 이메일 형식 확인 (Boolean 반환)
  static bool isValidEmail(String? email) {
    return email != null && email.isNotEmpty && _emailRegex.hasMatch(email);
  }

  /// 휴대폰 번호 형식 확인 (Boolean 반환)
  static bool isValidPhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) return false;
    final cleanPhone = phone.replaceAll('-', '');
    return _phoneRegex.hasMatch(cleanPhone);
  }

  /// 우편번호 형식 확인 (Boolean 반환)
  static bool isValidPostalCode(String? postalCode) {
    return postalCode != null && postalCode.isNotEmpty && _postalCodeRegex.hasMatch(postalCode);
  }

  /// URL 형식 확인 (Boolean 반환)
  static bool isValidUrl(String? url) {
    return url != null && url.isNotEmpty && _urlRegex.hasMatch(url);
  }

  /// 숫자 형식 확인 (Boolean 반환)
  static bool isNumeric(String? value) {
    if (value == null || value.isEmpty) return false;
    return _numberPattern.hasMatch(value);
  }

  /// 정수 형식 확인 (Boolean 반환)
  static bool isInteger(String? value) {
    if (value == null || value.isEmpty) return false;
    return int.tryParse(value) != null;
  }

  /// 알파벳만 포함 확인 (Boolean 반환)
  static bool isAlphabetic(String? value) {
    return value != null && value.isNotEmpty && RegExp(r'^[a-zA-Z]+$').hasMatch(value);
  }

  /// 알파벳과 숫자만 포함 확인 (Boolean 반환)
  static bool isAlphanumeric(String? value) {
    return value != null && value.isNotEmpty && _alphanumericPattern.hasMatch(value);
  }

  /// 문자열이 비어있지 않은지 확인
  static bool isNotEmpty(String? value) {
    return value != null && value.trim().isNotEmpty;
  }

  /// 문자열이 비어있는지 확인
  static bool isEmpty(String? value) {
    return !isNotEmpty(value);
  }

  /// 최소 길이 확인
  static bool hasMinLength(String? value, int minLength) {
    if (value == null) return false;
    return value.length >= minLength;
  }

  /// 정수 범위 확인
  static bool isIntInRange(int? value, int min, int max) {
    if (value == null) return false;
    return value >= min && value <= max;
  }

  /// 양수인지 확인
  static bool isPositiveInt(int? value) {
    if (value == null) return false;
    return value > 0;
  }

  /// 0 이상인지 확인
  static bool isNonNegativeInt(int? value) {
    if (value == null) return false;
    return value >= 0;
  }

  /// 요일 번호 유효성 확인 (1-7)
  static bool isValidDayOfWeek(int? dayOfWeek) {
    return isIntInRange(dayOfWeek, 1, 7);
  }

  /// 할인 금액 유효성 확인 (0 이상)
  static bool isValidDiscountAmount(int? discountAmount) {
    return isNonNegativeInt(discountAmount);
  }

  /// 할인율 유효성 확인 (0.0 ~ 100.0)
  static bool isValidDiscountPercentage(double? percentage) {
    if (percentage == null) return false;
    return percentage >= 0.0 && percentage <= 100.0;
  }

  /// 검색어 유효성 확인
  static bool isValidSearchQuery(String? query) {
    if (isEmpty(query)) return false;
    return hasMinLength(query, 1) && hasMaxLength(query, 100);
  }

  /// 한국어 이름 검증
  static bool isValidKoreanName(String? name) {
    if (isEmpty(name)) return false;
    return _koreanNamePattern.hasMatch(name!);
  }

  /// 비밀번호 강도 확인 (Boolean 반환) - 기존 방식 유지
  static bool isStrongPassword(String? password) {
    if (isEmpty(password) || !hasMinLength(password, 8)) return false;
    return _strongPasswordPattern.hasMatch(password!);
  }

  // ============ 추가 비즈니스 검증 메서드들 ============

  /// 상품명 검증
  static bool isValidProductName(String? productName) {
    if (productName == null || productName.isEmpty) return false;
    return productName.length >= 2 && 
           productName.length <= 100 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-_().,/]+$').hasMatch(productName);
  }

  /// 카테고리명 검증
  static bool isValidCategoryName(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) return false;
    return categoryName.length >= 2 && 
           categoryName.length <= 50 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-_]+$').hasMatch(categoryName);
  }

  /// 태그명 검증
  static bool isValidTagName(String? tagName) {
    if (tagName == null || tagName.isEmpty) return false;
    return tagName.length >= 1 && 
           tagName.length <= 30 && 
           RegExp(r'^[가-힣a-zA-Z0-9\-_]+$').hasMatch(tagName);
  }

  /// 설명 검증
  static bool isValidDescription(String? description) {
    if (description == null || description.isEmpty) return true; // 선택적 필드
    return description.length <= 1000 && 
           !SecurityValidators.containsMaliciousInput(description);
  }

  /// 주소 검증
  static bool isValidAddress(String? address) {
    if (address == null || address.isEmpty) return false;
    return address.length >= 5 && 
           address.length <= 200 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-.,()]+$').hasMatch(address);
  }

  /// 회사명 검증
  static bool isValidCompanyName(String? companyName) {
    if (companyName == null || companyName.isEmpty) return false;
    return companyName.length >= 2 && 
           companyName.length <= 100 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-_().,&]+$').hasMatch(companyName);
  }

  /// 사업자등록번호 검증 (형식만) - 기존 호환성 유지
  static bool isValidBusinessNumber(String? businessNumber) {
    if (businessNumber == null || businessNumber.isEmpty) return false;
    return _businessNumberPattern.hasMatch(businessNumber);
  }

  /// 통신판매업신고번호 검증 (형식만)
  static bool isValidTelecomNumber(String? telecomNumber) {
    if (telecomNumber == null || telecomNumber.isEmpty) return false;
    return RegExp(r'^제?\d{4}-\d{6}호?$').hasMatch(telecomNumber);
  }

  /// 은행계좌번호 검증 (형식만)
  static bool isValidBankAccount(String? accountNumber) {
    if (accountNumber == null || accountNumber.isEmpty) return false;
    final cleanAccount = accountNumber.replaceAll('-', '').replaceAll(' ', '');
    return RegExp(r'^\d{10,20}$').hasMatch(cleanAccount);
  }

  /// 신용카드번호 검증 (형식만, Luhn 알고리즘 미적용)
  static bool isValidCreditCard(String? cardNumber) {
    if (cardNumber == null || cardNumber.isEmpty) return false;
    final cleanCard = cardNumber.replaceAll('-', '').replaceAll(' ', '');
    return RegExp(r'^\d{13,19}$').hasMatch(cleanCard);
  }

  /// IPv4 주소 검증
  static bool isValidIPv4(String? ip) {
    if (ip == null || ip.isEmpty) return false;
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    return true;
  }

  /// MAC 주소 검증
  static bool isValidMacAddress(String? mac) {
    if (mac == null || mac.isEmpty) return false;
    return RegExp(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$').hasMatch(mac);
  }

  // ============ 추가 비즈니스 검증 메서드들 (business_validators.dart 호환) ============

  /// 가격 검증 (Boolean 반환) - 기존 호환성 유지
  static bool isValidPrice(int? price) {
    return isNonNegativeInt(price);
  }

  /// 수량 검증 (Boolean 반환) - 기존 호환성 유지  
  static bool isValidQuantity(int? quantity) {
    return isNonNegativeInt(quantity);
  }

  /// 판매자명 검증 (Boolean 반환)
  static bool isValidSellerName(String? sellerName) {
    if (sellerName == null || sellerName.isEmpty) return false;
    return sellerName.length >= 2 && 
           sellerName.length <= 50 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-_().,&]+$').hasMatch(sellerName);
  }

  /// 구매자명 검증 (Boolean 반환)
  static bool isValidBuyerName(String? buyerName) {
    if (buyerName == null || buyerName.isEmpty) return false;
    return buyerName.length >= 2 && 
           buyerName.length <= 50 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-_().,&]+$').hasMatch(buyerName);
  }

  /// 연락처 검증 (Boolean 반환) - 휴대폰 또는 이메일
  static bool isValidContact(String? contact) {
    if (contact == null || contact.isEmpty) return false;
    return isValidPhoneNumber(contact) || isValidEmail(contact);
  }

  /// 상품 목록 검증 (Boolean 반환) - 기존 호환성 유지
  static bool isValidProductList(String? productList) {
    return isNotEmpty(productList) && hasMaxLength(productList, 500);
  }

  /// 상품 목록 검증 (List 버전) - business_validators.dart 호환  
  static bool isValidProductListArray(List<String>? productList) {
    if (productList == null || productList.isEmpty) return false;
    return productList.every((product) => isValidProductName(product));
  }

  /// 은행명 검증 (Boolean 반환)
  static bool isValidBankName(String? bankName) {
    if (bankName == null || bankName.isEmpty) return false;
    return bankName.length >= 2 && 
           bankName.length <= 30 && 
           RegExp(r'^[가-힣a-zA-Z0-9\s\-_().,&]+$').hasMatch(bankName);
  }

  /// 계좌번호 검증 (Boolean 반환) - 기존 isValidBankAccount와 동일
  static bool isValidAccountNumber(String? accountNumber) {
    return isValidBankAccount(accountNumber);
  }

  /// 트위터 계정 검증 (Boolean 반환)
  static bool isValidTwitterAccount(String? twitter) {
    if (twitter == null || twitter.isEmpty) return false;
    return RegExp(r'^@?[a-zA-Z0-9_]{1,15}$').hasMatch(twitter);
  }

  /// 최대 길이 검증 (Boolean 반환)
  static bool hasMaxLength(String? value, int maxLength) {
    if (value == null) return true;
    return value.length <= maxLength;
  }

  // ============ validation_rules.dart 호환 메서드들 ============

  /// 가격 검증 (String 반환)
  static String? validatePrice(String? price) {
    return validateAmount(price, minAmount: 0, maxAmount: *********);
  }

  /// 재고 검증 (String 반환)
  static String? validateStock(String? stock) {
    return validateQuantity(stock, minQuantity: 0, maxQuantity: 999999);
  }

  /// 숫자 검증 (String 반환)
  static String? validateNumber(String? value, String? fieldName) {
    if (value == null || value.isEmpty) return null;
    
    if (!isNumeric(value)) {
      return fieldName != null ? '$fieldName은(는) 숫자만 입력 가능합니다.' : '숫자만 입력 가능합니다.';
    }
    return null;
  }

  /// 길이 범위 검증 (Boolean 반환)
  static bool isLengthInRange(String? value, int minLength, int maxLength) {
    if (value == null) return false;
    return value.length >= minLength && value.length <= maxLength;
  }
}

/// 보안 검증을 지원하는 유틸리티 클래스입니다.
/// - SQL Injection, XSS, Path Traversal, Command Injection 등 보안 위협 감지
/// - 2025년 기준 강화된 보안 패턴들 포함
class SecurityValidators {
  SecurityValidators._();

  // 강화된 SQL injection 패턴 - 더 포괄적인 감지
  static final RegExp _sqlInjectionPattern = RegExp(
    r"('|--|/\*|\*/|union\s+select|select\s+.*\s+from|"
    r"drop\s+table|insert\s+into|update\s+.*\s+set|delete\s+from|"
    r"exec\s*\(|execute\s*\(|sp_|xp_|sys\.|information_schema\.|"
    r"@@version|waitfor\s+delay|benchmark\s*\(|sleep\s*\(|"
    r"group_concat\s*\(|concat\s*\(|char\s*\(|ascii\s*\(|"
    r"substring\s*\(|mid\s*\(|length\s*\(|database\s*\(|"
    r"user\s*\(|version\s*\(|load_file\s*\(|into\s+outfile|"
    r"into\s+dumpfile|create\s+table|alter\s+table|truncate\s+table)",
    caseSensitive: false,
  );

  // 강화된 XSS 패턴 - 더 정교한 감지
  static final RegExp _xssPattern = RegExp(
    r'<[^>]*script[^>]*>|javascript:|vbscript:|on\w+\s*=|'
            r'<[^>]*iframe[^>]*>|<[^>]*embed[^>]*>|<[^>]*object[^>]*>|'
            r'<[^>]*link[^>]*>|<[^>]*meta[^>]*>|eval\s*\(|'
            r'expression\s*\(|url\s*\(.*javascript:|data:text/html|'
            r'style\s*\(|'
            r'<[^>]*svg[^>]*>|<[^>]*form[^>]*>|<[^>]*input[^>]*>|'
            r'alert\s*\(|confirm\s*\(|prompt\s*\(|'
            r'document\.cookie|document\.write|window\.location|'
            r'<[^>]*img[^>]*src\s*=\s*["' "'" r']javascript:|'
            r'<[^>]*body[^>]*onload\s*=|<[^>]*div[^>]*onclick\s*=',
    caseSensitive: false,
  );

  // 추가 보안 패턴들 - 2025년 강화
  static final RegExp _pathTraversalPattern = RegExp(
    r'(\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c|%252e%252e%252f|'
    r'%c0%ae%c0%ae%c0%af|%uff0e%uff0e%uff0f|'
    r'\.\.\/\.\.\/|\.\.\\\.\.\\|'
    r'\.\.%2f|\.\.%5c)',
    caseSensitive: false,
  );

  static final RegExp _commandInjectionPattern = RegExp(
    r'[;&|`\$(){}\[\]\\]|(cat\s+)|(ls\s+)|(rm\s+)|(wget\s+)|(curl\s+)|'
    r'(nc\s+)|(netcat\s+)|(ping\s+)|(nslookup\s+)|(whoami\s*)|(id\s*)|'
    r'(ps\s+)|(kill\s+)|(killall\s+)|(mount\s+)|(umount\s+)|'
    r'(chmod\s+)|(chown\s+)|(sudo\s+)|(su\s+)|(passwd\s+)|'
    r'(echo\s+.*>)|(echo\s+.*>>)|(\|\s*tee)|(\|\s*mail)|'
    r'(exec\s*\()|(system\s*\()|(shell_exec\s*\()|(passthru\s*\()|(&&)|(\|\|)',
    caseSensitive: false,
  );

  static final RegExp _htmlTagPattern = RegExp(
    r'<[^>]*>',
    caseSensitive: false,
  );

  // ============ 보안 검증 메서드들 ============

  /// 보안 검증: SQL Injection 시도 감지
  static bool containsSqlInjection(String? input) {
    if (input == null) return false;
    return _sqlInjectionPattern.hasMatch(input);
  }

  /// 보안 검증: XSS 시도 감지
  static bool containsXSS(String? input) {
    if (input == null) return false;
    return _xssPattern.hasMatch(input);
  }

  /// 보안 검증: Path Traversal 시도 감지
  static bool containsPathTraversal(String? input) {
    if (input == null) return false;
    return _pathTraversalPattern.hasMatch(input);
  }

  /// 보안 검증: Command Injection 시도 감지
  static bool containsCommandInjection(String? input) {
    if (input == null) return false;
    return _commandInjectionPattern.hasMatch(input);
  }

  /// 보안 검증: HTML 태그 포함 여부 감지
  static bool containsHtmlTags(String? input) {
    if (input == null) return false;
    return _htmlTagPattern.hasMatch(input);
  }

  /// 보안 검증: LDAP Injection 시도 감지 (2025년 추가)
  static bool containsLdapInjection(String? input) {
    if (input == null) return false;
    // 정상 이메일, 한글, 영문, 숫자 등은 오탐 방지
    if (RegExp(r'^[a-zA-Z0-9가-힣@._\- ]+$').hasMatch(input)) return false;
    final ldapPattern = RegExp(
      r'(\*\)|\(|\)|\&|\||!|=|<|>|~|\*|'
      r'objectclass|cn=|ou=|dc=|uid=|memberof=)',
      caseSensitive: false,
    );
    return ldapPattern.hasMatch(input);
  }

  /// 보안 검증: NoSQL Injection 시도 감지 (2025년 추가)
  static bool containsNoSqlInjection(String? input) {
    if (input == null) return false;
    // 정상 이메일, 한글, 영문, 숫자 등은 오탐 방지
    if (RegExp(r'^[a-zA-Z0-9가-힣@._\- ]+$').hasMatch(input)) return false;
    final nosqlPattern = RegExp(
      r'(\$where|\$ne|\$gt|\$lt|\$gte|\$lte|\$regex|\$exists|'
      r'\$in|\$nin|\$size|\$all|\$nor|\$or|\$and|'
      r'javascript\s*:|this\.|db\.|sleep\s*\()',
      caseSensitive: false,
    );
    return nosqlPattern.hasMatch(input);
  }

  /// 보안 검증: SSRF 시도 감지 (2025년 추가)
  static bool containsSsrfAttempt(String? input) {
    if (input == null) return false;
    // 정상 이메일, 한글, 영문, 숫자 등은 오탐 방지
    if (RegExp(r'^[a-zA-Z0-9가-힣@._\- ]+$').hasMatch(input)) return false;
    final ssrfPattern = RegExp(
      r'(localhost|127\.0\.0\.1|0\.0\.0\.0|169\.254\.|192\.168\.|'
      r'10\.|172\.(1[6-9]|2[0-9]|3[01])\.|'
      r'file://|ftp://|gopher://|dict://|ldap://|sftp://)',
      caseSensitive: false,
    );
    return ssrfPattern.hasMatch(input);
  }

  /// 보안 검증: 악성 파일 확장자 감지 (2025년 추가)
  static bool containsMaliciousFileExtension(String? filename) {
    if (filename == null) return false;
    final maliciousPattern = RegExp(
      r'\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|app|deb|rpm|dmg|pkg|msi|'
      r'ps1|sh|bin|run|action|apk|gadget|inf|ins|inx|ipa|isu|job|'
      r'lnk|msc|msp|mst|paf|pif|reg|rgs|scf|sct|shb|shs|u3p|vb|ws)$',
      caseSensitive: false,
    );
    // 악성 확장자가 붙으면 무조건 true
    if (maliciousPattern.hasMatch(filename)) return true;
    // 그 외 정상 파일명만 오탐 방지
    if (RegExp(r'^[a-zA-Z0-9가-힣._\- ]+$').hasMatch(filename)) return false;
    return false;
  }

  /// 보안 검증: 악성 입력 감지 (종합) - 2025년 강화
  static bool containsMaliciousInput(String? input) {
    // 정상 입력(이메일, 한글, 영문, 숫자, 일반 문장 등)은 오탐 방지
    if (input == null) return false;
    if (RegExp(r'^[a-zA-Z0-9가-힣@._\- ]+$').hasMatch(input)) return false;
    return containsSqlInjection(input) ||
        containsXSS(input) ||
        containsPathTraversal(input) ||
        containsCommandInjection(input) ||
        containsLdapInjection(input) ||
        containsNoSqlInjection(input) ||
        containsSsrfAttempt(input);
  }

  /// 보안 검증: 종합 보안 검사
  static Map<String, bool> performSecurityCheck(String? input) {
    return {
      'sqlInjection': containsSqlInjection(input),
      'xss': containsXSS(input),
      'pathTraversal': containsPathTraversal(input),
      'commandInjection': containsCommandInjection(input),
      'ldapInjection': containsLdapInjection(input),
      'nosqlInjection': containsNoSqlInjection(input),
      'ssrf': containsSsrfAttempt(input),
      'htmlTags': containsHtmlTags(input),
      'maliciousFile': containsMaliciousFileExtension(input),
    };
  }

  /// 보안 검증: 위험도 평가
  static SecurityRiskLevel assessSecurityRisk(String? input) {
    if (input == null) return SecurityRiskLevel.safe;

    final securityCheck = performSecurityCheck(input);
    final highRiskCount = securityCheck.values.where((risk) => risk).length;

    if (highRiskCount >= 3) return SecurityRiskLevel.critical;
    if (highRiskCount >= 2) return SecurityRiskLevel.high;
    if (highRiskCount >= 1) return SecurityRiskLevel.medium;
    return SecurityRiskLevel.safe;
  }
}

/// 보안 위험도 수준을 나타내는 열거형
enum SecurityRiskLevel {
  safe,     // 안전
  medium,   // 중간 위험
  high,     // 높은 위험
  critical, // 치명적 위험
}
