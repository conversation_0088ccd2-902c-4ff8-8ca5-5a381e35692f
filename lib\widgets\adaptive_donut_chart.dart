import 'dart:math';
import 'package:flutter/material.dart';

/// 데이터 모델 (기존 ChartData 와 필드명 호환 가정)
class DonutSegment {
  final String label;
  final double value;
  final Color color;
  DonutSegment({required this.label, required this.value, required this.color});
}

/// 어떤 해상도/디바이스에서도 모든 라벨을 강제로 배치하는 커스텀 도넛 차트
/// - Syncfusion 라벨 알고리즘 한계를 우회하기 위한 순수 Flutter 구현 (라벨 전용)
/// - 도넛 자체는 기본 CustomPainter 로 그림
class AdaptiveDonutChart extends StatelessWidget {
  const AdaptiveDonutChart({
    super.key,
    required this.segments,
    this.size = 260,
  this.donutScale = 0.8,
  this.fullWidth,
    this.innerRadiusFraction = 0.55,
  this.labelPadding = 14,
    this.connectorPadding = 4,
    this.maxLines = 2,
    this.maxFontSize = 11,
    this.minFontSize = 8,
    this.tryScaleDown = true,
    this.backgroundColor = Colors.white,
  });

  final List<DonutSegment> segments;
  final double size; // 정사각형 한 변
  final double donutScale; // 0~1 : size 대비 실제 도넛 반지름 비율 (작게하여 라벨 공간 확보)
  final double? fullWidth; // 전체 가용 폭 (null이면 size 사용)
  final double innerRadiusFraction; // 0~1
  final double labelPadding; // 도넛 외곽에서 라벨 시작 거리
  final double connectorPadding; // 라벨 박스와 커넥터 접점 내부 여백
  final int maxLines;
  final double maxFontSize;
  final double minFontSize;
  final bool tryScaleDown; // 겹침시 폰트 축소 시도
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    final filtered = segments.where((s) => s.value > 0).toList();
    final total = filtered.fold<double>(0, (p, e) => p + e.value);
    if (total == 0) {
      return SizedBox(
        width: size,
        height: size,
        child: const Center(child: Text('데이터 없음')),
      );
    }

    // 각도 계산
    final slices = <_Slice>[];
    double start = -pi / 2; // 12시 방향 시작
    for (final seg in filtered) {
      final sweep = (seg.value / total) * 2 * pi;
      slices.add(_Slice(segment: seg, startAngle: start, sweepAngle: sweep));
      start += sweep;
    }

    final totalWidth = fullWidth ?? size;
    return SizedBox(
      width: totalWidth,
      height: size,
      child: _AdaptiveLayout(
        slices: slices,
        innerRadiusFraction: innerRadiusFraction,
        labelPadding: labelPadding,
        connectorPadding: connectorPadding,
        maxLines: maxLines,
        maxFontSize: maxFontSize,
        minFontSize: minFontSize,
        tryScaleDown: tryScaleDown,
        backgroundColor: backgroundColor,
        donutScale: donutScale,
        donutBaseSize: size,
        totalWidth: totalWidth,
      ),
    );
  }
}

class _Slice {
  final DonutSegment segment;
  final double startAngle;
  final double sweepAngle;
  _Slice({required this.segment, required this.startAngle, required this.sweepAngle});
  double get midAngle => startAngle + sweepAngle / 2;
}

class _AdaptiveLayout extends StatefulWidget {
  const _AdaptiveLayout({
    required this.slices,
    required this.innerRadiusFraction,
    required this.labelPadding,
    required this.connectorPadding,
    required this.maxLines,
    required this.maxFontSize,
    required this.minFontSize,
    required this.tryScaleDown,
    required this.backgroundColor,
  required this.donutScale,
  required this.donutBaseSize,
  required this.totalWidth,
  });
  final double donutScale;
  final double donutBaseSize;
  final double totalWidth; // 전체 레이아웃 폭

  final List<_Slice> slices;
  final double innerRadiusFraction;
  final double labelPadding;
  final double connectorPadding;
  final int maxLines;
  final double maxFontSize;
  final double minFontSize;
  final bool tryScaleDown;
  final Color backgroundColor;

  @override
  State<_AdaptiveLayout> createState() => _AdaptiveLayoutState();
}

class _AdaptiveLayoutState extends State<_AdaptiveLayout> {
  late double _fontSize;

  @override
  void initState() {
    super.initState();
    _fontSize = widget.maxFontSize;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final donutSize = widget.donutBaseSize;
        final totalWidth = widget.totalWidth;
        final center = Offset(totalWidth / 2, donutSize / 2);
        final outerRadius = (donutSize / 2) * widget.donutScale;
        final donutOuter = outerRadius;
        final donutInner = donutOuter * widget.innerRadiusFraction;

        // 초기 target positions
        final left = <_LabelPos>[];
        final right = <_LabelPos>[];
        for (final s in widget.slices) {
          final angle = s.midAngle;
          final isRight = cos(angle) >= 0;
          final base = Offset(
            center.dx + cos(angle) * (donutOuter + widget.labelPadding),
            center.dy + sin(angle) * (donutOuter + widget.labelPadding),
          );
          final lp = _LabelPos(slice: s, target: base, isRight: isRight);
          (isRight ? right : left).add(lp);
        }

        // y 정렬 & 충돌 해결 (단순 greedy)
        void resolve(List<_LabelPos> list) {
          list.sort((a, b) => a.target.dy.compareTo(b.target.dy));
          const minGap = 4.0;
          for (int i = 1; i < list.length; i++) {
            final prev = list[i - 1];
            final curr = list[i];
            if (curr.target.dy - prev.target.dy < minGap) {
              final shift = (minGap - (curr.target.dy - prev.target.dy));
              list[i] = curr.shifted(Offset(0, shift));
            }
          }
        }
        resolve(left);
        resolve(right);

        // 화면 밖 클램프 (x,y)
        List<_LabelPos> clamp(List<_LabelPos> list) {
          return list.map((lp) {
            final clampedY = lp.target.dy.clamp(8, donutSize - 8).toDouble();
            double x = lp.target.dx.clamp(8, totalWidth - 8).toDouble();
            return lp.copyWith(target: Offset(x, clampedY));
          }).toList();
        }
        final leftC = clamp(left);
        final rightC = clamp(right);

  final all = [...leftC, ...rightC];

        // 필요시 폰트 축소 (라벨 수 많고 겹칠 가능성 높을 때)
        if (widget.tryScaleDown && all.length >= 6) {
          _fontSize = (widget.maxFontSize - 1).clamp(widget.minFontSize, widget.maxFontSize);
        }

        // 1차 라벨 측정 후 박스 위치/앵커 계산
        final labelStyle = TextStyle(
          fontSize: _fontSize,
          fontFamily: 'Pretendard',
          fontWeight: FontWeight.w600,
          color: Colors.black87,
          height: 1.1,
        );
        List<_LabelBox> buildLayouts(double fontSize) {
          final style = labelStyle.copyWith(fontSize: fontSize);
          final list = <_LabelBox>[];
          for (final lp in all) {
            final raw = lp.slice.segment.label;
            final display = _break(raw, widget.maxLines);
            final tp = TextPainter(
              text: TextSpan(text: display, style: style),
              maxLines: widget.maxLines,
              textDirection: TextDirection.ltr,
            )..layout(maxWidth: donutSize * 0.30);
            final boxWidth = tp.width + 8;
            final boxHeight = tp.height + 4;
            double leftEdge = lp.isRight ? lp.target.dx : lp.target.dx - boxWidth;
            if (leftEdge < 0) leftEdge = 0;
            if (leftEdge + boxWidth > totalWidth) leftEdge = totalWidth - boxWidth;
            final topEdge = lp.target.dy - boxHeight / 2;
            final box = Rect.fromLTWH(leftEdge, topEdge, boxWidth, boxHeight);
            final anchorX = lp.isRight ? box.left : box.right;
            final anchor = Offset(anchorX, (box.center.dy).roundToDouble());
            list.add(_LabelBox(slice: lp.slice, isRight: lp.isRight, box: box, display: display, anchor: anchor));
          }
          return list;
        }

        void resolveOverlap(List<_LabelBox> boxes) {
          if (boxes.isEmpty) return;
          boxes.sort((a,b)=>a.box.top.compareTo(b.box.top));
          const gap = 2.0; // 최소 간격
          for (int i=1;i<boxes.length;i++) {
            final prev = boxes[i-1];
            final curr = boxes[i];
            final overlap = (prev.box.bottom + gap) - curr.box.top;
            if (overlap > 0) {
              final shifted = curr.box.shift(Offset(0, overlap));
              boxes[i] = boxes[i].copyWith(
                box: shifted,
                anchor: Offset(curr.isRight ? shifted.left : shifted.right, shifted.center.dy.roundToDouble()),
              );
            }
          }
          // 하단 넘침 시 전체 위로 당김
          final bottomLimit = donutSize - 8;
            final overflow = boxes.last.box.bottom - bottomLimit;
            if (overflow > 0) {
              for (int i=0;i<boxes.length;i++) {
                final shifted = boxes[i].box.shift(Offset(0, -overflow));
                boxes[i] = boxes[i].copyWith(
                  box: shifted,
                  anchor: Offset(boxes[i].isRight ? shifted.left : shifted.right, shifted.center.dy.roundToDouble()),
                );
              }
            }
          // 상단 다시 체크 (위로 당기며 위로 나갔을 가능성) -> 필요시 아래로 미세 보정
          final topLimit = 8.0;
          final firstOverflow = topLimit - boxes.first.box.top;
          if (firstOverflow > 0) {
            for (int i=0;i<boxes.length;i++) {
              final shifted = boxes[i].box.shift(Offset(0, firstOverflow));
              boxes[i] = boxes[i].copyWith(
                box: shifted,
                anchor: Offset(boxes[i].isRight ? shifted.left : shifted.right, shifted.center.dy.roundToDouble()),
              );
            }
          }
        }

        bool hasOverlap(List<_LabelBox> boxes) {
          for (int i=1;i<boxes.length;i++) {
            if (boxes[i-1].box.overlaps(boxes[i].box)) return true;
          }
          return false;
        }

        List<_LabelBox> layouts = buildLayouts(_fontSize);
        // 좌/우 분리 후 겹침 해결
        List<_LabelBox> leftBoxes = layouts.where((e)=>!e.isRight).toList();
        List<_LabelBox> rightBoxes = layouts.where((e)=>e.isRight).toList();
        resolveOverlap(leftBoxes);
        resolveOverlap(rightBoxes);

        // 여전히 겹치면 (매우 촘촘) 폰트 감소 재시도
        if (widget.tryScaleDown) {
          while (_fontSize > widget.minFontSize && (hasOverlap(leftBoxes) || hasOverlap(rightBoxes))) {
            _fontSize -= 1;
            layouts = buildLayouts(_fontSize);
            leftBoxes = layouts.where((e)=>!e.isRight).toList();
            rightBoxes = layouts.where((e)=>e.isRight).toList();
            resolveOverlap(leftBoxes);
            resolveOverlap(rightBoxes);
          }
        }
        layouts = [...leftBoxes, ...rightBoxes];

        return Stack(
          children: [
            Positioned.fill(
              child: CustomPaint(
                painter: _DonutPainter(
                  slices: widget.slices,
                  center: center,
                  outerRadius: donutOuter,
                  innerRadius: donutInner,
                  labels: layouts,
                  connectorPadding: widget.connectorPadding,
                  fontSize: _fontSize,
                ),
              ),
            ),
            // 라벨 박스
            for (final lb in layouts)
              Positioned(
                left: lb.box.left,
                top: lb.box.top,
                width: lb.box.width,
                height: lb.box.height,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: widget.backgroundColor,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey.shade300),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.06),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: Alignment.center,
                    child: Text(
                      lb.display,
                      textAlign: TextAlign.center,
                      maxLines: widget.maxLines,
                      style: labelStyle,
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  String _break(String s, int maxLines) {
    if (maxLines <= 1) return s;
    if (s.length <= 5) return s; // 짧으면 그대로
    // 특정 단어 패턴
    if (s == '아크릴스탠드') return '아크릴\n스탠드';
    if (s == '아크릴키링') return '아크릴\n키링';
    // 중앙 분리
    final mid = (s.length / 2).floor();
    return s.substring(0, mid) + '\n' + s.substring(mid);
  }
}

class _LabelPos {
  final _Slice slice;
  final Offset target; // 라벨 기준점 (좌/우에 따라 박스 좌/우가 됨)
  final bool isRight;
  const _LabelPos({required this.slice, required this.target, required this.isRight});
  _LabelPos shifted(Offset delta) => _LabelPos(slice: slice, target: target + delta, isRight: isRight);
  _LabelPos copyWith({Offset? target}) => _LabelPos(slice: slice, target: target ?? this.target, isRight: isRight);
}

class _LabelBox {
  final _Slice slice;
  final bool isRight;
  final Rect box;
  final String display;
  final Offset anchor; // 커넥터가 들어오는 박스 가장자리 점
  _LabelBox({required this.slice, required this.isRight, required this.box, required this.display, required this.anchor});
  _LabelBox copyWith({Rect? box, Offset? anchor}) => _LabelBox(
    slice: slice,
    isRight: isRight,
    box: box ?? this.box,
    display: display,
    anchor: anchor ?? this.anchor,
  );
}

class _DonutPainter extends CustomPainter {
  final List<_Slice> slices;
  final Offset center;
  final double outerRadius;
  final double innerRadius;
  final List<_LabelBox> labels;
  final double connectorPadding;
  final double fontSize;
  _DonutPainter({
    required this.slices,
    required this.center,
    required this.outerRadius,
    required this.innerRadius,
    required this.labels,
    required this.connectorPadding,
    required this.fontSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
  final rect = Rect.fromCircle(center: center, radius: outerRadius);
    final paint = Paint()..style = PaintingStyle.fill;

    for (final s in slices) {
      paint.color = s.segment.color;
      final path = Path()
        ..moveTo(center.dx, center.dy)
        ..arcTo(rect, s.startAngle, s.sweepAngle, false)
        ..close();
      // 도넛: 외부 조각을 그리고 내부 구멍을 뚫기 위해서 saveLayer + BlendMode.clear 방식도 가능하지만
      // 여기서는 간단하게 전체를 그린 뒤 마지막에 흰색 원 덮어 구멍 표현
      canvas.drawPath(path, paint);
    }
    // 내부 구멍
    final holePaint = Paint()..color = Colors.white;
    canvas.drawCircle(center, innerRadius, holePaint);

    // 커넥터 라인
    final linePaint = Paint()
      ..color = Colors.grey.shade600
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    for (final lb in labels) {
      final angle = lb.slice.midAngle;
      final start = Offset(
        center.dx + cos(angle) * outerRadius,
        center.dy + sin(angle) * outerRadius,
      );
      // 라벨 박스 가장자리 점 (패딩 보정 없이 바로 박스 엣지로 맞춤)
      final end = lb.anchor;
      final mid = Offset(
        center.dx + cos(angle) * (outerRadius + (innerRadius * 0.15)),
        center.dy + sin(angle) * (outerRadius + (innerRadius * 0.15)),
      );
      final path = Path()
        ..moveTo(start.dx, start.dy)
        ..quadraticBezierTo(mid.dx, mid.dy, end.dx, end.dy);
      canvas.drawPath(path, linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant _DonutPainter oldDelegate) =>
      oldDelegate.slices != slices || oldDelegate.labels != labels || oldDelegate.fontSize != fontSize;
}
