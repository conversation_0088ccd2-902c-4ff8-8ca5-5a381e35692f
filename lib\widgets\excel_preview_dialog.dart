import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import 'dart:io';
import '../services/excel_export_service.dart';
import '../utils/app_colors.dart';
import '../utils/currency_utils.dart';
import '../utils/toast_utils.dart';
import '../utils/dialog_theme.dart' as custom_dialog;
import '../providers/unified_workspace_provider.dart';

/// 엑셀 내보내기 미리보기 다이얼로그
class ExcelPreviewDialog extends ConsumerStatefulWidget {
  final Map<String, dynamic> statsData;
  final String? seller;
  final DateTimeRange? dateRange;

  const ExcelPreviewDialog({
    super.key,
    required this.statsData,
    this.seller,
    this.dateRange,
  });

  @override
  ConsumerState<ExcelPreviewDialog> createState() => _ExcelPreviewDialogState();
}

class _ExcelPreviewDialogState extends ConsumerState<ExcelPreviewDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return custom_dialog.DialogTheme.buildResponsiveLargeDialog(
      child: Column(
          children: [
            // 헤더
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primarySeed.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.table_chart,
                    color: AppColors.primarySeed,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '엑셀 내보내기 요약',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                        if (widget.seller != null || widget.dateRange != null)
                          const SizedBox(height: 4),
                        if (widget.seller != null || widget.dateRange != null)
                          Text(
                            _getFilterText(),
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            
            // 탭 바
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: TabBar(
                controller: _tabController,
                isScrollable: false,
                labelColor: AppColors.primarySeed,
                unselectedLabelColor: Colors.grey.shade600,
                indicatorColor: AppColors.primarySeed,
                tabs: const [
                  Tab(text: '종합 리포트'),
                  Tab(text: '상품별 상세'),
                  Tab(text: '세트 판매 상세'),
                ],
              ),
            ),
            
            // 탭 뷰
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildComprehensivePreview(),
                  _buildProductDetailPreview(),
                  _buildSetDiscountDetailPreview(),
                ],
              ),
            ),
            
            // 버튼들
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _isExporting ? null : _downloadFile,
                          icon: Icon(
                            Icons.download,
                            size: 18,
                            color: AppColors.primarySeed,
                          ),
                          label: const Text('다운로드'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.primarySeed,
                            side: BorderSide(color: AppColors.primarySeed),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isExporting ? null : _exportAndShare,
                          icon: Icon(
                            Icons.share,
                            size: 18,
                            color: Colors.white,
                          ),
                          label: const Text('공유'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primarySeed,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (_isExporting)
                    const Padding(
                      padding: EdgeInsets.only(top: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            '파일을 생성하고 있습니다...',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        )
    );
  }

  String _getFilterText() {
    final parts = <String>[];
    
    if (widget.seller != null) {
      parts.add('판매자: ${widget.seller}');
    }
    
    if (widget.dateRange != null) {
      final start = widget.dateRange!.start;
      final end = widget.dateRange!.end;
      parts.add('기간: ${start.year}-${start.month.toString().padLeft(2, '0')}-${start.day.toString().padLeft(2, '0')} ~ ${end.year}-${end.month.toString().padLeft(2, '0')}-${end.day.toString().padLeft(2, '0')}');
    }
    
    return parts.join(' | ');
  }

  /// 종합 리포트 미리보기 (핵심지표 + 카테고리 + 판매자 + 선입금)
  Widget _buildComprehensivePreview() {
    final categoryStats = widget.statsData['categoryStats'] as Map<String, Map<String, int>>? ?? {};
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));
    
    final sellerStats = widget.statsData['sellerStats'] as Map<String, Map<String, int>>? ?? {};
    final sortedSellers = sellerStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));
    
    final totalAmount = widget.statsData['totalPrepaymentAmount'] ?? 0;
    final receivedAmount = widget.statsData['receivedPrepaymentAmount'] ?? 0;
    final receivedRate = totalAmount > 0 ? (receivedAmount / totalAmount * 100) : 0.0;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 기본 지표 섹션
          Text(
            '📊 기본 지표',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable([
            ['항목', '값'],
            ['총 매출', CurrencyUtils.formatCurrency(widget.statsData['totalRevenue'] ?? 0)],
            ['총 거래 건수', '${widget.statsData['totalTransactions'] ?? 0}건'],
            ['평균 거래액', CurrencyUtils.formatCurrency(widget.statsData['averageTransaction'] ?? 0)],
            ['총 판매량', '${widget.statsData['totalQuantity'] ?? 0}개'],
          ]),
          
          const SizedBox(height: 24),
          
          // 선입금 현황 섹션
          Text(
            '💰 선입금 현황',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable([
            ['항목', '금액', '건수'],
            ['총 선입금', CurrencyUtils.formatCurrency(widget.statsData['totalPrepaymentAmount'] ?? 0), '${widget.statsData['totalPrepaymentCount'] ?? 0}건'],
            ['수령 완료', CurrencyUtils.formatCurrency(widget.statsData['receivedPrepaymentAmount'] ?? 0), '${widget.statsData['receivedPrepaymentCount'] ?? 0}건'],
            ['미수령', CurrencyUtils.formatCurrency(widget.statsData['pendingPrepaymentAmount'] ?? 0), '${widget.statsData['pendingPrepaymentCount'] ?? 0}건'],
          ]),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.info,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '수령률: ${receivedRate.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: AppColors.info,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 카테고리별 분석 섹션
          Text(
            '📂 카테고리별 매출',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable([
            ['카테고리', '판매량', '총 매출'],
            ...sortedCategories.take(5).map((entry) => [
              entry.key,
              '${entry.value['quantity'] ?? 0}개',
              CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0),
            ]),
          ]),
          if (sortedCategories.length > 5)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                '...외 ${sortedCategories.length - 5}개 카테고리',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ),
          
          const SizedBox(height: 24),
          
          // 할인 내역 섹션
          Text(
            '💸 할인 내역',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable([
            ['할인 종류', '건수', '할인액'],
            ['세트 할인', '${widget.statsData['setDiscountCount'] ?? 0}건', CurrencyUtils.formatCurrency(widget.statsData['totalSetDiscountAmount'] ?? 0)],
            ['수동 할인', '${widget.statsData['manualDiscountCount'] ?? 0}건', CurrencyUtils.formatCurrency(widget.statsData['totalManualDiscountAmount'] ?? 0)],
          ]),
          
          const SizedBox(height: 24),
          
          // 서비스 제공 섹션
          Text(
            '🎁 서비스 제공',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable([
            ['항목', '값'],
            ['서비스 상품 종류', '${widget.statsData['serviceProductTypes'] ?? 0}종류'],
            ['서비스 제공 횟수', '${widget.statsData['serviceCount'] ?? 0}건'],
            ['서비스 제공 수량', '${widget.statsData['totalServiceQuantity'] ?? 0}개'],
          ]),
          
          const SizedBox(height: 24),
          
          // 판매자별 통계 섹션
          Text(
            '👥 판매자별 통계',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable([
            ['판매자', '거래 건수', '총 매출'],
            ...sortedSellers.take(5).map((entry) => [
              entry.key,
              '${entry.value['count'] ?? 0}건',
              CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0),
            ]),
          ]),
          if (sortedSellers.length > 5)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                '...외 ${sortedSellers.length - 5}명 판매자',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 상품별 상세 미리보기 (모든 상품 포함)
  Widget _buildProductDetailPreview() {
    final productStats = widget.statsData['productStats'] as Map<String, Map<String, int>>? ?? {};
    final sortedProducts = productStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));
    
    final tableData = [
      ['상품명', '판매량', '총 매출'],
      ...sortedProducts.map((entry) => [
        entry.key,
        '${entry.value['quantity'] ?? 0}개',
        CurrencyUtils.formatCurrency(entry.value['revenue'] ?? 0),
      ]),
    ];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🛍️ 상품별 매출 상세 (${sortedProducts.length}개 상품)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewTable(tableData),
        ],
      ),
    );
  }

  /// 세트 판매 상세 미리보기
  Widget _buildSetDiscountDetailPreview() {
    final setDiscountStats = widget.statsData['setDiscountStats'] as Map<String, Map<String, int>>? ?? {};
    final sortedSets = setDiscountStats.entries.toList()
      ..sort((a, b) => (b.value['totalDiscount'] ?? 0).compareTo(a.value['totalDiscount'] ?? 0));
    
    if (sortedSets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '세트 할인 데이터가 없습니다',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    final tableData = [
      ['세트명', '적용 횟수', '총 할인액'],
      ...sortedSets.map((entry) => [
        entry.key,
        '${entry.value['count'] ?? 0}회',
        CurrencyUtils.formatCurrency(entry.value['totalDiscount'] ?? 0),
      ]),
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '💰 세트 판매 상세 (총 ${sortedSets.length}개 세트)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primarySeed,
            ),
          ),
          const SizedBox(height: 16),
          _buildPreviewTable(tableData),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.savings_outlined,
                  color: AppColors.success,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '총 할인액: ${CurrencyUtils.formatCurrency(sortedSets.fold<int>(0, (sum, entry) => sum + (entry.value['totalDiscount'] ?? 0)))}',
                  style: TextStyle(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 컬럼 헤더에 따른 적절한 flex 값 반환
  double _getColumnFlex(String header, int columnIndex, List<String> headers) {
    // 상품별 상세 테이블인지 확인 (상품명 헤더로 판단)
    final isProductDetailTable = headers.contains('상품명');
    
    if (isProductDetailTable) {
      // 상품별 상세 전용 비율 조정
      if (columnIndex == 0) return 6.0; // 상품명 
      if (header.contains('판매량')) return 2.0; // 판매량  
      if (header.contains('총 매출') || header.contains('매출')) return 3; // 총매출 
    }
    
   
    if (header.contains('금액') || header.contains('총 매출') || header.contains('매출')) {
      return 2.5;
    }
    
    if (header.contains('판매량') || header.contains('거래 건수') || header.contains('건수')) {
      return 1.5;
    }
    
    if (columnIndex == 0) {
      return 4.0;
    }
    
    // 기본값
    return 2.0;
  }

  /// 테이블 미리보기 위젯 (개선된 스타일)
  Widget _buildPreviewTable(List<List<String>> data) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 1.5),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: data.asMap().entries.map((entry) {
            final index = entry.key;
            final row = entry.value;
            final isHeader = index == 0;
            final isEven = index % 2 == 0;
            
            return Container(
              decoration: BoxDecoration(
                color: isHeader 
                    ? AppColors.primarySeed.withValues(alpha: 0.1)
                    : isEven 
                        ? Colors.white 
                        : Colors.grey.shade50,
                border: index < data.length - 1 
                    ? Border(bottom: BorderSide(color: Colors.grey.shade300, width: 0.8))
                    : null,
              ),
              child: IntrinsicHeight(
                child: Row(
                  children: row.asMap().entries.map((cellEntry) {
                    final cellIndex = cellEntry.key;
                    final cell = cellEntry.value;
                    final isNumericColumn = cellIndex > 0 && !isHeader;
                    final isLastColumn = cellIndex == row.length - 1;
                    
                    // 컬럼 너비 조절 - 헤더 기반으로 적절한 flex 값 계산
                    final header = data.isNotEmpty ? data[0][cellIndex] : '';
                    final flex = _getColumnFlex(header, cellIndex, data.isNotEmpty ? data[0] : []);
                    
                    return Expanded(
                      flex: flex.round(),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          vertical: isHeader ? 12 : 10, 
                          horizontal: 12
                        ),
                        decoration: !isLastColumn
                            ? BoxDecoration(
                                border: Border(
                                  right: BorderSide(color: Colors.grey.shade300, width: 0.8)
                                ),
                              )
                            : null,
                        child: Text(
                          cell,
                          style: TextStyle(
                            fontWeight: isHeader ? FontWeight.bold : FontWeight.w500,
                            fontSize: isHeader ? 13 : 12,
                            color: isHeader 
                                ? AppColors.primarySeed
                                : Colors.grey.shade700,
                          ),
                          textAlign: isNumericColumn 
                              ? TextAlign.right 
                              : cellIndex == 0 
                                  ? TextAlign.left 
                                  : TextAlign.center,
                          overflow: TextOverflow.clip,
                          maxLines: 1,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 파일 직접 다운로드
  Future<void> _downloadFile() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // 현재 이벤트 이름 가져오기
      final eventName = ref.read(currentWorkspaceNameProvider);
      
      // 엑셀 파일 생성 - 전체 통계 데이터로 직접 생성
      final file = await ExcelExportService.exportFullStatsData(
        statsData: widget.statsData,
        dateRange: widget.dateRange,
        eventName: eventName,
      );

      // Android API 레벨 확인 및 적절한 저장 방법 선택
      try {
        // 파일명 생성 - 엑셀 서비스에서 생성된 원본 파일명 사용
        final now = DateTime.now();
        final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
        
        // 원본 파일명에서 확장자를 제거하고 타임스탬프 추가
        final originalFileName = file.path.split('/').last;
        final baseName = originalFileName.replaceAll('.xlsx', '');
        final fileName = '${baseName}_$timestamp.xlsx';
        
        // Android 10+ (API 29+)에서는 Downloads 공용 폴더에 직접 저장
        final downloadsDir = Directory('/storage/emulated/0/Download');
        final downloadPath = '${downloadsDir.path}/$fileName';
        final downloadFile = File(downloadPath);
        
        // 파일 복사
        await file.copy(downloadFile.path);
        
        if (mounted) {
          Navigator.of(context).pop();
          ToastUtils.showSuccess(
            context, 
            '파일이 다운로드 폴더에 저장되었습니다!\n📁 $fileName'
          );
          
          // 파일 자동으로 열기 (약간의 지연 후)
          Future.delayed(const Duration(milliseconds: 500), () async {
            try {
              final result = await OpenFile.open(downloadFile.path);
              if (result.type != ResultType.done) {
                // 파일 열기에 실패한 경우 추가 안내
                if (mounted) {
                  ToastUtils.showInfo(context, 
                    '자동 열기에 실패했습니다.\n'
                    '다운로드 폴더에서 ${fileName}을 직접 열어주세요.'
                  );
                }
              }
            } catch (e) {
              debugPrint('파일 열기 실패: $e');
              if (mounted) {
                ToastUtils.showInfo(context, 
                  '파일이 저장되었지만 자동으로 열 수 없습니다.\n'
                  '다운로드 폴더에서 확인해주세요.'
                );
              }
            }
          });
        }
      } catch (e) {
        // 직접 저장이 실패하면 시스템 공유로 대체
        if (mounted) {
          ToastUtils.showInfo(context, '직접 저장이 실패하여 시스템 공유를 사용합니다.');
          await _exportAndShare();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
        ToastUtils.showError(context, '파일 생성 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 엑셀 파일 내보내기 및 공유
  Future<void> _exportAndShare() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // 현재 이벤트 이름 가져오기
      final eventName = ref.read(currentWorkspaceNameProvider);
      
      // 엑셀 파일 생성 - 전체 통계 데이터로 직접 생성
      final file = await ExcelExportService.exportFullStatsData(
        statsData: widget.statsData,
        dateRange: widget.dateRange,
        eventName: eventName,
      );

      // 날짜 형식 생성
      final now = DateTime.now();
      final dateStr = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

        // 파일 공유
        final result = await SharePlus.instance.share(
          ShareParams(
            files: [XFile(file.path)],
            subject: '바라 부스 매니저 통계 리포트 - $dateStr',
            text: '📊 바라 부스 매니저 통계 데이터 ($dateStr)\n\n'
                  '${widget.seller != null ? "판매자: ${widget.seller}\n" : ""}'
                  '${widget.dateRange != null ? _getFilterText() : ""}\n\n'
                  '바라 부스 매니저 통계 엑셀 파일',
          ),
        );      if (mounted) {
        Navigator.of(context).pop();
        
        switch (result.status) {
          case ShareResultStatus.success:
            ToastUtils.showSuccess(context, '📤 파일이 성공적으로 공유되었습니다!');
            break;
          case ShareResultStatus.dismissed:
            ToastUtils.showInfo(context, '공유가 취소되었습니다.');
            break;
          case ShareResultStatus.unavailable:
            ToastUtils.showError(context, '공유 기능을 사용할 수 없습니다.');
            break;
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
        ToastUtils.showError(context, '엑셀 파일 생성 중 오류가 발생했습니다: $e');
      }
    }
  }
}
