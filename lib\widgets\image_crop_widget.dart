import 'dart:io';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:crop_your_image/crop_your_image.dart';
import '../utils/app_colors.dart';
import '../utils/logger_utils.dart';

/// 크롭 타입 정의
enum CropShape { circle, square, roundedSquare }

/// 이미지에 라운드 모서리를 적용하는 헬퍼 함수
img.Image _applyRoundedCornersHelper(img.Image image, double radius) {
  final width = image.width;
  final height = image.height;
  final result = img.Image(width: width, height: height);

  // 투명 배경으로 초기화
  img.fill(result, color: img.ColorUint8.rgba(0, 0, 0, 0));

  for (int y = 0; y < height; y++) {
    for (int x = 0; x < width; x++) {
      // 각 모서리에서의 거리 계산
      double distanceFromCorner = double.infinity;

      // 좌상단 모서리
      if (x < radius && y < radius) {
        distanceFromCorner = sqrt((x - radius) * (x - radius) + (y - radius) * (y - radius));
      }
      // 우상단 모서리
      else if (x > width - radius && y < radius) {
        distanceFromCorner = sqrt((x - (width - radius)) * (x - (width - radius)) + (y - radius) * (y - radius));
      }
      // 좌하단 모서리
      else if (x < radius && y > height - radius) {
        distanceFromCorner = sqrt((x - radius) * (x - radius) + (y - (height - radius)) * (y - (height - radius)));
      }
      // 우하단 모서리
      else if (x > width - radius && y > height - radius) {
        distanceFromCorner = sqrt((x - (width - radius)) * (x - (width - radius)) + (y - (height - radius)) * (y - (height - radius)));
      }

      // 모서리 영역이 아니거나 반지름 내부인 경우 픽셀 복사
      if (distanceFromCorner == double.infinity || distanceFromCorner <= radius) {
        final pixel = image.getPixel(x, y);
        result.setPixel(x, y, pixel);
      }
    }
  }

  return result;
}

/// 이미지 크롭 유틸리티
class ImageCropUtils {
  /// crop_your_image 기반 크롭 다이얼로그 (원형/정사각형)
  static Future<File?> cropImage({
    required BuildContext context,
    required String imagePath,
    CropShape shape = CropShape.circle, // 기본값: 프로필(원형)
    double? aspectRatio, // 상품: 1.0(정사각형), 프로필: 1.0(원형)
  }) async {
    final bytes = await File(imagePath).readAsBytes();
    final result = await showDialog<Uint8List?>(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        final controller = CropController();
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          child: SizedBox(
            width: 350,
            height: 480,
            child: _CropYourImageDialog(
              imageBytes: bytes,
              controller: controller,
              shape: shape,
              aspectRatio: aspectRatio,
            ),
          ),
        );
      },
    );
    if (result == null) return null;
    try {
      // PNG 바이트를 img.Image로 디코딩
      final img.Image? decoded = img.decodeImage(result);
      if (decoded == null) return null;

      // 라운드 사각형인 경우 모서리를 둥글게 처리
      img.Image processedImage = decoded;
      if (shape == CropShape.roundedSquare) {
        processedImage = _applyRoundedCornersHelper(decoded, 12);
      }

      // 투명 배경을 흰색으로 처리 (JPG는 알파 채널 없음)
      final img.Image whiteBg = img.Image(width: processedImage.width, height: processedImage.height);
      img.fill(whiteBg, color: img.ColorUint8.rgb(255, 255, 255));
      img.compositeImage(whiteBg, processedImage);

      // JPG로 인코딩 (quality: 80으로 용량 최적화)
      final jpgBytes = img.encodeJpg(whiteBg, quality: 80);

      // 앱의 영구 디렉토리에 저장 (임시 디렉토리 대신)
      final tempDir = await Directory.systemTemp.createTemp('crop_temp');
      final fileName = 'cropped_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(jpgBytes);

      // 파일이 제대로 생성되었는지 확인
      if (await file.exists() && await file.length() > 0) {
        return file;
      } else {
        LoggerUtils.logError(
          '크롭 파일 생성 실패',
          tag: 'ImageCropWidget',
        );
        return null;
      }
    } catch (e) {
      LoggerUtils.logError(
        '크롭 이미지 저장 실패',
        error: e,
        tag: 'ImageCropWidget',
      );
      return null;
    }
  }

  /// 이미지 크롭 다이얼로그를 표시하고 크롭된 이미지 바이트 데이터를 반환
  static Future<Uint8List?> cropImageBytes({
    required BuildContext context,
    required String imagePath,
    CropShape shape = CropShape.circle,
    double? aspectRatio,
  }) async {
    final file = await cropImage(
      context: context,
      imagePath: imagePath,
      shape: shape,
      aspectRatio: aspectRatio,
    );
    if (file == null) return null;
    return await file.readAsBytes();
  }

  /// 이미지 크롭 다이얼로그를 표시하고 크롭된 이미지를 img.Image 객체로 반환
  static Future<img.Image?> cropImageToImage({
    required BuildContext context,
    required String imagePath,
    CropShape shape = CropShape.circle,
    double? aspectRatio,
  }) async {
    final bytes = await cropImageBytes(
      context: context,
      imagePath: imagePath,
      shape: shape,
      aspectRatio: aspectRatio,
    );
    if (bytes == null) return null;
    return img.decodeImage(bytes);
  }
}

/// crop_your_image 다이얼로그 위젯 (원형/정사각형)
class _CropYourImageDialog extends StatefulWidget {
  final Uint8List imageBytes;
  final CropController controller;
  final CropShape shape;
  final double? aspectRatio;
  const _CropYourImageDialog({
    required this.imageBytes,
    required this.controller,
    required this.shape,
    this.aspectRatio,
  });
  @override
  State<_CropYourImageDialog> createState() => _CropYourImageDialogState();
}

class _CropYourImageDialogState extends State<_CropYourImageDialog> {
  bool _cropping = false;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 16),
        Text(
          widget.shape == CropShape.circle ? '프로필 이미지 편집' : '이미지 크롭',
          style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontWeight: FontWeight.bold, fontSize: 18),
        ),
        const SizedBox(height: 8),
        // 안내 문구 추가
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            '두 손가락으로 조정해 크기와 위치를 조정하세요',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 14,
              color: AppColors.neutral60,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Center(
            child: Container(
              width: 280, // 크롭 영역을 280x280으로 제한
              height: 280,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (!_cropping)
                    ClipRRect(
                      borderRadius: widget.shape == CropShape.roundedSquare
                          ? BorderRadius.circular(12)
                          : BorderRadius.zero,
                      child: Container(
                        width: 280,
                        height: 280,
                        child: Crop(
                          image: widget.imageBytes,
                          controller: widget.controller,
                          withCircleUi: widget.shape == CropShape.circle,
                          aspectRatio: widget.aspectRatio ?? 1.0,
                          maskColor: Colors.black.withValues(alpha: 0.6),
                          baseColor: Colors.white,
                          interactive: true,
                          fixCropRect: true,
                          cornerDotBuilder: (size, edgeAlignment) => const SizedBox.shrink(),
                          progressIndicator: const SizedBox.shrink(),
                          onCropped: (result) {
                            switch (result) {
                              case CropSuccess(:final croppedImage):
                                Navigator.of(context).pop(croppedImage);
                              case CropFailure():
                                Navigator.of(context).pop();
                            }
                          },
                        ),
                      ),
                    ),
                  // 라운드 테두리 오버레이
                  if (!_cropping)
                    IgnorePointer(
                      child: Container(
                        width: 280,
                        height: 280,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.primarySeed, // 앱 테마 색상
                            width: 3,
                          ),
                          borderRadius: widget.shape == CropShape.roundedSquare
                              ? BorderRadius.circular(12)
                              : widget.shape == CropShape.circle
                                  ? BorderRadius.circular(140) // 원형
                                  : BorderRadius.zero, // 사각형
                        ),
                      ),
                    ),
              if (_cropping)
                Positioned.fill(
                  child: Container(
                    color: Colors.white.withValues(alpha: 0.85),
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        if (!_cropping)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('취소'),
              ),
              ElevatedButton(
                onPressed: () async {
                  setState(() => _cropping = true);
                  if (widget.shape == CropShape.circle) {
                    widget.controller.cropCircle();
                  } else {
                    widget.controller.crop();
                  }
                },
                child: const Text('완료'),
              ),
            ],
          ),
        if (_cropping)
          const SizedBox(height: 48), // 버튼 영역 로딩 제거
      ],
    );
  }
  @override
  void initState() {
    super.initState();
    // controller.onCropped 제거 (공식 문서 방식 적용)
  }

  @override
  void dispose() {
    // CropController는 외부 패키지로 자동 정리됨
    super.dispose();
  }
}
