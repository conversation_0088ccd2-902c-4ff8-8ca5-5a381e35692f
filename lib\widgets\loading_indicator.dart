import 'package:flutter/material.dart';

/// 로딩(진행중) 상태를 표시하는 공통 인디케이터 위젯입니다.
/// - CircularProgressIndicator, 커스텀 메시지/색상 등 지원
/// - 재사용성, UX 일관성, 접근성 향상 목적
///
/// [size]로 크기를 지정할 수 있으며, [color]로 색상을 지정할 수 있습니다.
/// [message]를 통해 로딩 중 표시할 메시지를 설정할 수 있습니다.
/// [backgroundColor]를 통해 배경색을 설정할 수 있습니다.
class LoadingIndicator extends StatelessWidget {
  final double size;
  final Color? color;
  final String? message;
  final Color? backgroundColor;

  const LoadingIndicator({
    super.key,
    this.size = 40.0,
    this.color,
    this.message,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final indicatorColor = color ?? theme.colorScheme.primary;

    return Container(
      color: backgroundColor,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: 3.0,
                valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
              ),
            ),
            if (message != null) ...[
              const SizedBox(height: 16.0),
              Text(
                message!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: indicatorColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 전체 화면을 차지하는 로딩 인디케이터
class FullScreenLoadingIndicator extends StatelessWidget {
  final String? message;
  final Color? backgroundColor;

  const FullScreenLoadingIndicator({
    super.key,
    this.message,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingIndicator(
      message: message,
      backgroundColor: backgroundColor ?? Colors.black.withValues(alpha: 0.3),
    );
  }
}

/// 오버레이 형태의 로딩 인디케이터
class OverlayLoadingIndicator extends StatelessWidget {
  final String? message;

  const OverlayLoadingIndicator({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const ModalBarrier(dismissible: false, color: Colors.black54),
        LoadingIndicator(message: message, color: Colors.white),
      ],
    );
  }
}
