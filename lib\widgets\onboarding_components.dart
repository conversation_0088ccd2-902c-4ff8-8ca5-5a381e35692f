import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/responsive_helper.dart';

/// 온보딩 플로우에서 사용하는 공통 UI 컴포넌트들
/// 
/// 일관된 디자인과 반응형 레이아웃을 제공하는 재사용 가능한 위젯들
class OnboardingComponents {
  OnboardingComponents._();

  // === 배경 컴포넌트 ===
  
  /// 온보딩 기본 배경 위젯
  static Widget buildBackground({
    required Widget child,
    bool useGradient = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: useGradient ? AppColors.backgroundGradient : null,
        color: useGradient ? null : AppColors.background,
      ),
      child: child,
    );
  }

  // === 카드 컴포넌트 ===
  
  /// 온보딩 카드 위젯
  static Widget buildCard({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
    double? maxWidth,
    bool hasShadow = true,
  }) {
    return Center(
      child: Container(
        width: maxWidth ?? ResponsiveHelper.getCardMaxWidth(context),
        margin: ResponsiveHelper.getScreenPadding(context),
        padding: padding ?? ResponsiveHelper.getCardPadding(context),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          boxShadow: hasShadow ? [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: child,
      ),
    );
  }

  // === 버튼 컴포넌트 ===
  
  /// 메인 액션 버튼
  static Widget buildPrimaryButton({
    required BuildContext context,
    required String text,
    required VoidCallback? onPressed,
    bool isLoading = false,
    IconData? icon,
  }) {
    return SizedBox(
      width: double.infinity,
      height: ResponsiveHelper.getButtonHeight(context),
      child: Container(
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          boxShadow: [
            BoxShadow(
              color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isLoading ? null : onPressed,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
            child: Container(
              padding: ResponsiveHelper.getButtonPadding(context),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isLoading) ...[
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.onboardingTextOnPrimary),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ] else if (icon != null) ...[
                    Icon(
                      icon,
                      color: AppColors.onboardingTextOnPrimary,
                      size: ResponsiveHelper.getIconSize(context),
                    ),
                    const SizedBox(width: 12),
                  ],
                  Text(
                    text,
                    style: TextStyle(
                      color: AppColors.onboardingTextOnPrimary,
                      fontSize: ResponsiveHelper.getBodyFontSize(context),
                      fontWeight: FontWeight.w600,
                      inherit: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 보조 버튼 (아웃라인)
  static Widget buildSecondaryButton({
    required BuildContext context,
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
  }) {
    return SizedBox(
      width: double.infinity,
      height: ResponsiveHelper.getButtonHeight(context),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.onboardingPrimary, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          ),
          padding: ResponsiveHelper.getButtonPadding(context),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: AppColors.onboardingPrimary,
                size: ResponsiveHelper.getIconSize(context),
              ),
              const SizedBox(width: 12),
            ],
            Text(
              text,
              style: TextStyle(
                color: AppColors.onboardingPrimary,
                fontSize: ResponsiveHelper.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
                inherit: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // === 텍스트 컴포넌트 ===
  
  /// 메인 제목 텍스트
  static Widget buildTitle({
    required BuildContext context,
    required String text,
    TextAlign textAlign = TextAlign.center,
    Color? color,
  }) {
    return Text(
      text,
      textAlign: textAlign,
      style: TextStyle(
        fontSize: ResponsiveHelper.getTitleFontSize(context),
        fontWeight: FontWeight.bold,
        color: color ?? AppColors.onboardingTextPrimary,
        height: 1.2,
        inherit: true,
      ),
    );
  }
  
  /// 부제목 텍스트
  static Widget buildSubtitle({
    required BuildContext context,
    required String text,
    TextAlign textAlign = TextAlign.center,
    Color? color,
  }) {
    return Text(
      text,
      textAlign: textAlign,
      style: TextStyle(
        fontSize: ResponsiveHelper.getSubtitleFontSize(context),
        fontWeight: FontWeight.w400,
        color: color ?? AppColors.onboardingTextSecondary,
        height: 1.4,
        inherit: true,
      ),
    );
  }
  
  /// 본문 텍스트
  static Widget buildBody({
    required BuildContext context,
    required String text,
    TextAlign textAlign = TextAlign.center,
    Color? color,
  }) {
    return Text(
      text,
      textAlign: textAlign,
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context),
        fontWeight: FontWeight.w400,
        color: color ?? AppColors.onboardingTextSecondary,
        height: 1.5,
        inherit: true,
      ),
    );
  }

  // === 입력 필드 컴포넌트 ===
  
  /// 온보딩 텍스트 필드
  static Widget buildTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    String? hint,
    IconData? prefixIcon,
    bool obscureText = false,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    FocusNode? focusNode,
    TextInputAction textInputAction = TextInputAction.next,
    Function(String)? onSubmitted,
  }) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      obscureText: obscureText,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      onFieldSubmitted: onSubmitted,
      validator: validator,
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context),
        color: AppColors.onboardingTextPrimary,
        inherit: true,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: prefixIcon != null ? Icon(
          prefixIcon,
          color: AppColors.onboardingPrimary,
          size: ResponsiveHelper.getIconSize(context),
        ) : null,
        labelStyle: TextStyle(
          color: AppColors.onboardingTextSecondary,
          fontSize: ResponsiveHelper.getBodyFontSize(context),
          inherit: true,
        ),
        hintStyle: TextStyle(
          color: AppColors.onboardingTextTertiary,
          fontSize: ResponsiveHelper.getBodyFontSize(context),
          inherit: true,
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: AppColors.onboardingPrimary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: ResponsiveHelper.getButtonPadding(context),
      ),
    );
  }

  // === 아이콘 컴포넌트 ===
  
  /// 메인 아이콘 (로고 등)
  static Widget buildMainIcon({
    required BuildContext context,
    required IconData icon,
    Color? color,
    bool hasGradient = true,
  }) {
    final iconSize = ResponsiveHelper.getMainIconSize(context);
    
    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        gradient: hasGradient ? AppColors.primaryGradient : null,
        color: hasGradient ? null : (color ?? AppColors.onboardingPrimary),
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Icon(
        icon,
        size: iconSize * 0.5,
        color: AppColors.onboardingTextOnPrimary,
      ),
    );
  }

  // === 소셜 로그인 버튼 ===

  /// 전체 너비 소셜 로그인 버튼
  static Widget buildSocialButton({
    required BuildContext context,
    required String assetPath,
    required VoidCallback onPressed,
    required String label,
    Color? backgroundColor,
    Color? textColor,
  }) {
    return SizedBox(
      width: double.infinity,
      height: ResponsiveHelper.getButtonHeight(context),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.surface,
          side: BorderSide(color: AppColors.secondary, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          ),
          padding: ResponsiveHelper.getButtonPadding(context),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              assetPath,
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                color: textColor ?? AppColors.onboardingTextPrimary,
                fontSize: ResponsiveHelper.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
                inherit: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 소셜 로그인 버튼 컬럼
  static Widget buildSocialButtonColumn({
    required BuildContext context,
    required List<Map<String, dynamic>> socialButtons,
  }) {
    return Column(
      children: socialButtons.asMap().entries.map((entry) {
        final index = entry.key;
        final button = entry.value;

        return Column(
          children: [
            buildSocialButton(
              context: context,
              assetPath: button['asset'],
              onPressed: button['onPressed'],
              label: button['label'],
              backgroundColor: button['backgroundColor'],
              textColor: button['textColor'],
            ),
            if (index < socialButtons.length - 1)
              const SizedBox(height: 12),
          ],
        );
      }).toList(),
    );
  }

  // === 간격 컴포넌트 ===

  /// 섹션 간격
  static Widget buildSectionSpacing(BuildContext context) {
    return SizedBox(height: ResponsiveHelper.getSectionSpacing(context));
  }

  /// 작은 간격
  static Widget buildSmallSpacing(BuildContext context) {
    return SizedBox(height: ResponsiveHelper.getSectionSpacing(context) * 0.5);
  }
}
