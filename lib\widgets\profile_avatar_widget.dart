import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/nickname.dart';
import '../providers/nickname_provider.dart';
import '../utils/logger_utils.dart';


/// 프로필 아바타 위젯
///
/// 로컬 이미지를 우선적으로 표시하고, 없으면 기본 아이콘을 표시합니다.
/// NicknameProvider와 연동하여 일관된 프로필 이미지 표시를 제공합니다.
///
/// 개선사항:
/// - 빠른 동기적 파일 확인으로 로딩 없는 즉시 표시
/// - 캐시 무효화를 위한 타임스탬프 키 사용
/// - 불필요한 로딩 상태 제거
class ProfileAvatarWidget extends ConsumerWidget {
  final double radius;
  final Color? backgroundColor;
  final Color? iconColor;
  final IconData defaultIcon;
  final Nickname? nickname;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const ProfileAvatarWidget({
    super.key,
    this.radius = 24.0,
    this.backgroundColor,
    this.iconColor,
    this.defaultIcon = Icons.person,
    this.nickname,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // nickname이 직접 전달되지 않았으면 provider에서 가져오기
    final currentNickname = nickname ?? ref.watch(nicknameProvider);

    return Container(
      decoration: showBorder ? BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: borderColor ?? Theme.of(context).colorScheme.primary,
          width: borderWidth,
        ),
      ) : null,
      child: _buildAvatar(context, currentNickname),
    );
  }

  Widget _buildAvatar(BuildContext context, Nickname? nickname) {
    // 로컬 이미지 경로가 설정된 경우 즉시 확인 (동기적)
    final path = nickname?.profileImagePath;
    if (path != null && path.isNotEmpty) {
      try {
        final file = File(path);
        // 동기적 파일 존재 확인 (로컬 파일이므로 빠름)
        if (file.existsSync() && file.lengthSync() > 0) {
          LoggerUtils.logDebug('프로필 이미지 파일 확인 성공: $path', tag: 'ProfileAvatarWidget');
          return CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? Colors.grey[200],
            backgroundImage: FileImage(file),
            // 캐시 무효화를 위해 파일 경로와 수정 시간을 키로 사용
            key: ValueKey('profile_${path}_${file.lastModifiedSync().millisecondsSinceEpoch}'),
            onBackgroundImageError: (exception, stackTrace) {
              LoggerUtils.logError('프로필 이미지 로딩 실패: $path', tag: 'ProfileAvatarWidget', error: exception);
            },
          );
        } else {
          LoggerUtils.logWarning('프로필 이미지 파일이 존재하지 않거나 비어있음: $path', tag: 'ProfileAvatarWidget');
        }
      } catch (e) {
        LoggerUtils.logError('프로필 이미지 파일 확인 실패: $path', tag: 'ProfileAvatarWidget', error: e);
      }
    }

    // 기본 아이콘 표시
    return _buildDefaultAvatar(context);
  }

  Widget _buildDefaultAvatar(BuildContext context) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey[200],
      child: Icon(
        defaultIcon,
        size: radius * 0.8,
        color: iconColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }
}

/// 편의를 위한 프리셋 프로필 아바타들
class ProfileAvatars {
  /// 작은 프로필 아바타 (반지름 16)
  static Widget small({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 16.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
  );

  /// 중간 프로필 아바타 (반지름 24)
  static Widget medium({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 24.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
  );

  /// 큰 프로필 아바타 (반지름 32)
  static Widget large({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
    bool showBorder = false,
    Color? borderColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 32.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
    showBorder: showBorder,
    borderColor: borderColor,
  );

  /// 매우 큰 프로필 아바타 (반지름 48)
  static Widget extraLarge({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
    bool showBorder = false,
    Color? borderColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 48.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
    showBorder: showBorder,
    borderColor: borderColor,
  );
}
