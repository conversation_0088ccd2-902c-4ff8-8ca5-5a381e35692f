import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/sales_log.dart';
import '../models/transaction_type.dart';
import '../utils/currency_utils.dart';
import '../utils/app_colors.dart';
import '../utils/dialog_theme.dart' as custom_dialog;

/// 통합된 판매 기록 상세 다이얼로그
///
/// 세트할인을 별도 카드로 표시하고 테두리로 묶어서 표현합니다.
class SalesRecordDetailDialog extends ConsumerStatefulWidget {
  final List<SalesLog> salesLogs;
  final String? selectedSeller;
  final Map<int, String>? productCategoryMap;
  final Function(SalesLog)? onItemDelete;

  const SalesRecordDetailDialog({
    super.key,
    required this.salesLogs,
    this.selectedSeller,
    this.productCategoryMap,
    this.onItemDelete,
  });

  /// 다이얼로그 표시 메서드
  static Future<void> show({
    required BuildContext context,
    required List<SalesLog> salesLogs,
    String? selectedSeller,
    Map<int, String>? productCategoryMap,
    Function(SalesLog)? onItemDelete,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SalesRecordDetailDialog(
        salesLogs: salesLogs,
        selectedSeller: selectedSeller,
        productCategoryMap: productCategoryMap,
        onItemDelete: onItemDelete,
      ),
    );
  }

  @override
  ConsumerState<SalesRecordDetailDialog> createState() => _SalesRecordDetailDialogState();
}

class _SalesRecordDetailDialogState extends ConsumerState<SalesRecordDetailDialog> {
  late List<SalesLog> currentSalesLogs;

  @override
  void initState() {
    super.initState();
    currentSalesLogs = List.from(widget.salesLogs);
  }

  /// 개별 상품 삭제 처리
  Future<void> _handleItemDelete(SalesLog item) async {
    // 삭제 확인 다이얼로그 표시
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('판매 기록 삭제'),
        content: Text('${item.productName} 판매 기록을 삭제하시겠습니까?\n\n재고가 복구됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('삭제'),
          ),
        ],
      ),
    );

    if (shouldDelete == true && widget.onItemDelete != null) {
      // 삭제 콜백 호출
      widget.onItemDelete!(item);

      // 로컬 상태에서 해당 아이템 제거
      setState(() {
        currentSalesLogs.removeWhere((log) => log.id == item.id);
      });

      // 모든 아이템이 삭제되면 다이얼로그 닫기
      if (currentSalesLogs.isEmpty) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final screenHeight = screenSize.height;

    // 총 수량과 금액 계산
    final totalQuantity = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.soldQuantity);
    final totalOriginalAmount = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.totalAmount); // 할인 전 원가 합계
    final totalSetDiscount = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
    final totalManualDiscount = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.manualDiscountAmount);
    final totalFinalAmount = totalOriginalAmount - totalSetDiscount - totalManualDiscount; // 최종 금액 (모든 할인 적용)

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: custom_dialog.DialogTheme.isLandscape(context) ? 16 : (isTablet ? 40 : 20),
        vertical: custom_dialog.DialogTheme.isLandscape(context) ? 8 : screenHeight * 0.1,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
          maxWidth: isTablet ? 600 : double.infinity,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // 헤더
            _buildHeader(
              isTablet: isTablet,
              title: '판매 상세',
              icon: Icons.shopping_cart,
              totalQuantity: totalQuantity,
              originalAmount: totalOriginalAmount,
              setDiscountAmount: totalSetDiscount,
              manualDiscountAmount: totalManualDiscount,
              finalAmount: totalFinalAmount,
            ),

            // 날짜시간 정보
            _buildDateTimeInfo(isTablet),

            // 상품 목록 (세트할인 그룹화)
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 24.0 : 20.0,
                  vertical: isTablet ? 16.0 : 12.0,
                ),
                child: _buildGroupedSalesItems(isTablet),
              ),
            ),

            // 하단 여백
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 세트할인 그룹화된 상품 목록
  Widget _buildGroupedSalesItems(bool isTablet) {
    // 세트할인별로 그룹화
    final Map<String, List<SalesLog>> setDiscountGroups = {};
    final List<SalesLog> noDiscountItems = [];

    for (final item in currentSalesLogs) {
      if (item.setDiscountAmount > 0 && item.setDiscountNames != null && item.setDiscountNames!.isNotEmpty) {
        // 세트할인이 적용된 상품
        final discountKey = item.setDiscountNames!;
        setDiscountGroups.putIfAbsent(discountKey, () => []).add(item);
      } else {
        // 세트할인이 적용되지 않은 상품
        noDiscountItems.add(item);
      }
    }

    final List<Widget> widgets = [];

    // 세트할인 그룹들 먼저 표시
    for (final entry in setDiscountGroups.entries) {
      widgets.add(_buildSetDiscountGroup(entry.key, entry.value, isTablet));
    }

    // 세트할인이 없는 개별 상품들
    for (int i = 0; i < noDiscountItems.length; i++) {
      widgets.add(_buildSingleItem(noDiscountItems[i], isTablet));
    }

    return ListView(
      children: widgets,
    );
  }

  /// 세트할인 그룹 위젯
  Widget _buildSetDiscountGroup(String discountNames, List<SalesLog> items, bool isTablet) {
    // 총 세트할인 금액 계산
    final totalSetDiscount = items.fold<int>(0, (sum, item) => sum + item.setDiscountAmount);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(0),
        color: AppColors.onboardingPrimary.withValues(alpha: 0.05),
      ),
      child: Column(
        children: [
          // 세트 상품들
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return _buildSetDiscountItem(item, index, items.length, isTablet);
          }),

          // 세트할인 정보 카드
          _buildSetDiscountInfoCard(discountNames, totalSetDiscount, isTablet),
        ],
      ),
    );
  }

  /// 세트할인 정보 카드
  Widget _buildSetDiscountInfoCard(String discountNames, int totalDiscount, bool isTablet) {
    return Container(
      margin: const EdgeInsets.all(0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.onboardingPrimary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(0),
        border: Border.all(
          color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_offer,
            color: AppColors.onboardingPrimary,
            size: isTablet ? 20 : 18,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '세트할인',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: isTablet ? 14 : 13,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onboardingPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  discountNames,
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: isTablet ? 12 : 11,
                    color: AppColors.onboardingTextPrimary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '-${CurrencyUtils.formatCurrency(totalDiscount)}원',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: isTablet ? 16 : 15,
              fontWeight: FontWeight.w700,
              color: AppColors.onboardingPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// 세트할인 그룹 내 개별 상품
  Widget _buildSetDiscountItem(SalesLog item, int index, int totalCount, bool isTablet) {
    final isCurrentSeller = widget.selectedSeller == '전체 판매자' ||
                           (item.sellerName ?? '알 수 없음') == widget.selectedSeller;

    return Container(
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border.all(color: Colors.grey[300]!, width: 1),
        borderRadius: BorderRadius.circular(0),
      ),
      child: InkWell(
        onLongPress: widget.onItemDelete != null ? () => _handleItemDelete(item) : null,
        child: Opacity(
          opacity: isCurrentSeller ? 1.0 : 0.6,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
              // 상품 정보
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 상품명
                    Text(
                      _buildProductDisplayName(item),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: isCurrentSeller
                            ? AppColors.onSurface
                            : AppColors.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // 판매자 및 거래 유형
                    Text(
                      '${item.transactionType.displayName} • ${item.sellerName ?? '알 수 없음'}',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 13,
                        color: isCurrentSeller
                            ? AppColors.onSurfaceVariant
                            : AppColors.neutral50,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // 수량과 금액 (세트할인 제외한 원가)
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${item.soldQuantity}개',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: isCurrentSeller
                          ? AppColors.onSurfaceVariant
                          : AppColors.neutral50,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.transactionType == TransactionType.service
                        ? '서비스'
                        : '${CurrencyUtils.formatCurrency(item.soldPrice * item.soldQuantity)}원', // 원래 가격 (단가 × 수량)
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: isCurrentSeller
                          ? AppColors.onSurface
                          : AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 개별 상품 (세트할인 없음)
  Widget _buildSingleItem(SalesLog item, bool isTablet) {
    final isCurrentSeller = widget.selectedSeller == '전체 판매자' ||
                           (item.sellerName ?? '알 수 없음') == widget.selectedSeller;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border.all(color: Colors.grey[300]!, width: 1),
        borderRadius: BorderRadius.circular(0),
      ),
      child: InkWell(
        onLongPress: widget.onItemDelete != null ? () => _handleItemDelete(item) : null,
        child: Opacity(
          opacity: isCurrentSeller ? 1.0 : 0.6,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
              // 상품 정보
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 상품명
                    Text(
                      _buildProductDisplayName(item),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: isCurrentSeller
                            ? AppColors.onSurface
                            : AppColors.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // 판매자 및 거래 유형
                    Text(
                      '${item.transactionType.displayName} • ${item.sellerName ?? '알 수 없음'}',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 13,
                        color: isCurrentSeller
                            ? AppColors.onSurfaceVariant
                            : AppColors.neutral50,
                      ),
                    ),
                    // 수동 할인 정보만 표시 (세트할인은 별도 카드에서 처리)
                    if (item.manualDiscountAmount > 0)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: isCurrentSeller
                                ? AppColors.success.withValues(alpha: 0.1)
                                : AppColors.neutral20,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.money_off,
                                size: 12,
                                color: isCurrentSeller
                                    ? AppColors.success
                                    : AppColors.neutral50,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '수동할인 -${CurrencyUtils.formatCurrency(item.manualDiscountAmount)}원',
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 11,
                                  color: isCurrentSeller
                                      ? AppColors.success
                                      : AppColors.neutral50,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // 수량과 금액
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${item.soldQuantity}개',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: isCurrentSeller
                          ? AppColors.onSurfaceVariant
                          : AppColors.neutral50,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.transactionType == TransactionType.service
                        ? '서비스'
                        : '${CurrencyUtils.formatCurrency(item.soldPrice * item.soldQuantity)}원',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: isCurrentSeller
                          ? AppColors.onSurface
                          : AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 상품 표시명 생성
  String _buildProductDisplayName(SalesLog item) {
    if (widget.productCategoryMap != null && item.productId != null) {
      final categoryName = widget.productCategoryMap![item.productId];
      if (categoryName != null) {
        return '$categoryName-${item.productName}';
      }
    }
    return item.productName;
  }

  /// 공통 헤더
  Widget _buildHeader({
    required bool isTablet,
    required String title,
    required IconData icon,
    required int totalQuantity,
    required int originalAmount,
    required int setDiscountAmount,
    int manualDiscountAmount = 0,
    required int finalAmount,
  }) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
      decoration: BoxDecoration(
        color: AppColors.primarySeed.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        border: Border(
          bottom: BorderSide(
            color: AppColors.neutral20,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primarySeed.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primarySeed,
                  size: isTablet ? 24 : 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.w700,
                        color: AppColors.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    // 수량과 금액 표시 (세트할인 여부에 따라 다른 형식)
                    Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Text(
                          '총 $totalQuantity개 • ',
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: isTablet ? 14 : 13,
                            color: AppColors.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (setDiscountAmount > 0) ...[
                          // 세트할인이 있는 경우: N원 - N원 = 최종금액
                          Text(
                            '${CurrencyUtils.formatCurrency(originalAmount)}원',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 14 : 13,
                              color: AppColors.onSurfaceVariant,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            ' - ',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 14 : 13,
                              color: AppColors.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '${CurrencyUtils.formatCurrency(setDiscountAmount)}원',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 14 : 13,
                              color: AppColors.success,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            ' = ',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 14 : 13,
                              color: AppColors.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '${CurrencyUtils.formatCurrency(finalAmount)}원',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 14 : 13,
                              color: AppColors.primarySeed,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ] else ...[
                          // 세트할인이 없는 경우: 단순히 금액만 표시
                          Text(
                            '${CurrencyUtils.formatCurrency(originalAmount)}원',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 14 : 13,
                              color: AppColors.onSurfaceVariant,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              // X 닫기 버튼 추가
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(
                  Icons.close,
                  color: AppColors.onSurfaceVariant,
                  size: isTablet ? 24 : 22,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.surface.withValues(alpha: 0.8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 날짜 시간 정보
  Widget _buildDateTimeInfo(bool isTablet) {
    if (currentSalesLogs.isEmpty) return const SizedBox.shrink();

    final firstLog = currentSalesLogs.first;
    final dateTime = DateTime.fromMillisecondsSinceEpoch(firstLog.saleTimestamp);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 24.0 : 20.0,
        vertical: isTablet ? 12.0 : 10.0,
      ),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.5),
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            size: isTablet ? 18 : 16,
            color: AppColors.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            _formatDateTime(dateTime),
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: isTablet ? 14 : 13,
              color: AppColors.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 날짜 시간 포맷팅
  String _formatDateTime(DateTime dateTime) {
    const weekdays = ['월요일', '화요일', '수요일', '목요일', '금요일', '토요일', '일요일'];
    final weekday = weekdays[dateTime.weekday - 1];
    return '${dateTime.year}년 ${dateTime.month}월 ${dateTime.day}일 ($weekday) ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
