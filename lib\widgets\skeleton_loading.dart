import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// 스켈레톤 로딩 위젯 모음
/// 
/// 다양한 UI 요소에 대한 스켈레톤 로딩 애니메이션을 제공합니다.
class SkeletonLoading {
  
  /// 기본 스켈레톤 색상 (더 마일드하게)
  static const Color _baseColor = Color(0xFFF0F0F0);
  static const Color _highlightColor = Color(0xFFF8F8F8);

  /// 이미지 스켈레톤
  static Widget image({
    double? width,
    double? height,
    BorderRadius? borderRadius,
  }) {
    return Shimmer.fromColors(
      baseColor: _baseColor,
      highlightColor: _highlightColor,
      period: const Duration(milliseconds: 1500), // 더 느린 애니메이션
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: _baseColor,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 텍스트 스켈레톤
  static Widget text({
    double width = 100,
    double height = 16,
    BorderRadius? borderRadius,
  }) {
    return Shimmer.fromColors(
      baseColor: _baseColor,
      highlightColor: _highlightColor,
      period: const Duration(milliseconds: 1500), // 더 느린 애니메이션
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: _baseColor,
          borderRadius: borderRadius ?? BorderRadius.circular(4),
        ),
      ),
    );
  }

  /// 원형 스켈레톤 (아바타 등)
  static Widget circle({
    double radius = 20,
  }) {
    return Shimmer.fromColors(
      baseColor: _baseColor,
      highlightColor: _highlightColor,
      period: const Duration(milliseconds: 1500), // 더 느린 애니메이션
      child: Container(
        width: radius * 2,
        height: radius * 2,
        decoration: const BoxDecoration(
          color: _baseColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  /// 상품 카드 스켈레톤
  static Widget productCard({
    double? width,
    double? height,
  }) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 이미지 영역
            image(
              width: width,
              height: height ?? 120,
              borderRadius: BorderRadius.circular(8),
            ),
            const SizedBox(height: 12),
            // 제목
            text(width: (width ?? 200) * 0.8, height: 18),
            const SizedBox(height: 8),
            // 가격
            text(width: (width ?? 200) * 0.6, height: 16),
            const SizedBox(height: 8),
            // 설명
            text(width: (width ?? 200) * 0.9, height: 14),
            const SizedBox(height: 4),
            text(width: (width ?? 200) * 0.7, height: 14),
          ],
        ),
      ),
    );
  }

  /// 리스트 아이템 스켈레톤
  static Widget listItem({
    bool hasLeading = true,
    bool hasTrailing = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          if (hasLeading) ...[
            circle(radius: 24),
            const SizedBox(width: 16),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                text(width: 200, height: 16),
                const SizedBox(height: 8),
                text(width: 150, height: 14),
              ],
            ),
          ),
          if (hasTrailing) ...[
            const SizedBox(width: 16),
            text(width: 60, height: 16),
          ],
        ],
      ),
    );
  }

  /// 그리드 아이템 스켈레톤
  static Widget gridItem({
    double? width,
    double? height,
  }) {
    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.all(4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: image(
              width: width,
              height: null,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                text(width: width ?? 100, height: 14),
                const SizedBox(height: 4),
                text(width: (width ?? 100) * 0.7, height: 12),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 커스텀 스켈레톤
  static Widget custom({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
  }) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? _baseColor,
      highlightColor: highlightColor ?? _highlightColor,
      period: const Duration(milliseconds: 1500), // 더 느린 애니메이션
      child: child,
    );
  }
}
