import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_colors.dart';

import '../utils/toast_utils.dart';

/// 슬라이딩으로 수량을 조절할 수 있는 위젯
class SliderQuantityWidget extends StatefulWidget {
  final int quantity;
  final int maxQuantity;
  final Function(int) onQuantityChanged;
  final bool isTablet;
  final bool isService;

  const SliderQuantityWidget({
    super.key,
    required this.quantity,
    required this.maxQuantity,
    required this.onQuantityChanged,
    required this.isTablet,
    this.isService = false,
  });

  @override
  State<SliderQuantityWidget> createState() => _SliderQuantityWidgetState();
}

class _SliderQuantityWidgetState extends State<SliderQuantityWidget> {
  late int _currentQuantity;
  double _dragStartX = 0;
  double _dragCurrentX = 0;
  bool _isDragging = false;

  /// 수량에 따른 표시 너비 계산 (3자리수 대응)
  double _calculateQuantityDisplayWidth(int quantity, bool isTablet) {
    if (quantity < 10) {
      // 1자리수: 기본 너비
      return isTablet ? 24 : 20;
    } else if (quantity < 100) {
      // 2자리수: 약간 더 넓게
      return isTablet ? 32 : 28;
    } else {
      // 3자리수 이상: 충분히 넓게
      return isTablet ? 40 : 36;
    }
  }

  @override
  void initState() {
    super.initState();
    _currentQuantity = widget.quantity;
  }

  @override
  void didUpdateWidget(SliderQuantityWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.quantity != widget.quantity) {
      _currentQuantity = widget.quantity;
    }
  }

  void _handlePanStart(DragStartDetails details) {
    _dragStartX = details.localPosition.dx;
    _dragCurrentX = _dragStartX;
    _isDragging = true;
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    _dragCurrentX = details.localPosition.dx;
    final dragDistance = _dragCurrentX - _dragStartX;
    
    // 30픽셀마다 수량 1씩 변경
    final quantityChange = (dragDistance / 30).round();
    final maxAllowed = widget.isService ? 99 : widget.maxQuantity.clamp(0, 99); // 최대 99로 제한
    final newQuantity = (widget.quantity + quantityChange).clamp(0, maxAllowed);
    
    if (newQuantity != _currentQuantity) {
      setState(() {
        _currentQuantity = newQuantity;
      });
      
      // 햅틱 피드백
      HapticFeedback.selectionClick();
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    if (!_isDragging) return;

    // 항상 드래그 상태를 해제하고 UI 업데이트
    setState(() {
      _isDragging = false;
    });

    if (_currentQuantity != widget.quantity) {
      widget.onQuantityChanged(_currentQuantity);
    } else {
      // 수량이 변경되지 않았어도 원래 색상으로 복원
      setState(() {
        // 이미 _isDragging = false로 설정했으므로 UI가 업데이트됨
      });
    }
  }

  void _showQuantityInputDialog() {
    final controller = TextEditingController(text: _currentQuantity.toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          '수량 입력',
          style: TextStyle(
            fontSize: widget.isTablet ? 18 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
          ),
        ),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            labelText: '수량',
            hintText: widget.isService ? '1-99' : '1-${widget.maxQuantity}',
            border: const OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              '취소',
              style: TextStyle(
                color: AppColors.onSurfaceVariant,
                fontSize: widget.isTablet ? 14 : 12,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              final inputQuantity = int.tryParse(controller.text);
              if (inputQuantity == null || inputQuantity < 0) {
                ToastUtils.showError(context, '올바른 수량을 입력해주세요.');
                return;
              }
              
              final maxAllowed = widget.isService ? 99 : widget.maxQuantity.clamp(0, 99);
              if (inputQuantity > maxAllowed) {
                ToastUtils.showError(context, '최대 ${maxAllowed}개까지 가능합니다.');
                return;
              }
              
              Navigator.pop(context);
              widget.onQuantityChanged(inputQuantity);
            },
            child: Text(
              '확인',
              style: TextStyle(
                color: AppColors.primarySeed,
                fontSize: widget.isTablet ? 14 : 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handlePanCancel() {
    // 터치가 취소된 경우에도 드래그 상태 해제
    if (_isDragging) {
      setState(() {
        _isDragging = false;
        _currentQuantity = widget.quantity; // 원래 수량으로 복원
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      // Listener는 다른 제스처와 충돌하지 않는 더 낮은 레벨에서 처리
      onPointerDown: (event) {
        _handlePanStart(DragStartDetails(
          globalPosition: event.position,
          localPosition: event.localPosition,
        ));
      },
      onPointerMove: (event) {
        _handlePanUpdate(DragUpdateDetails(
          globalPosition: event.position,
          localPosition: event.localPosition,
          delta: event.delta,
        ));
      },
      onPointerUp: (event) {
        _handlePanEnd(DragEndDetails(
          primaryVelocity: 0, // Listener에서는 velocity 계산이 복잡하므로 0으로 설정
        ));
      },
      onPointerCancel: (event) {
        _handlePanCancel();
      },
      child: GestureDetector(
        // 탭 제스처는 별도로 처리
        behavior: HitTestBehavior.opaque,
        onTap: _showQuantityInputDialog,
      child: Container(
        width: widget.isTablet ? 70 : 60, // 두 자리 수 기준으로 크기 축소
        height: widget.isTablet ? 32 : 28,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _isDragging ? AppColors.primarySeed : AppColors.neutral30,
            width: _isDragging ? 2 : 1,
          ),
          boxShadow: _isDragging ? [
            BoxShadow(
              color: AppColors.primarySeed.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 좌측 화살표 힌트
            Icon(
              Icons.chevron_left,
              size: widget.isTablet ? 14 : 12,
              color: AppColors.onSurfaceVariant.withValues(alpha: 0.5),
            ),

            // 수량 표시 (동적 너비로 3자리수 대응)
            SizedBox(
              width: _calculateQuantityDisplayWidth(_currentQuantity, widget.isTablet),
              child: Center(
                child: Text(
                  _currentQuantity.toString(),
                  style: TextStyle(
                    fontSize: widget.isTablet ? 14 : 12,
                    fontWeight: FontWeight.w600,
                    color: _isDragging ? AppColors.primarySeed : AppColors.onSurface,
                  ),
                ),
              ),
            ),

            // 우측 화살표 힌트
            Icon(
              Icons.chevron_right,
              size: widget.isTablet ? 14 : 12,
              color: AppColors.onSurfaceVariant.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    ),
    );
  }
}
