import 'package:flutter/material.dart';
import '../utils/dialog_theme.dart' as custom_dialog;
import '../utils/app_colors.dart';

/// 미저장 변경사항 확인 다이얼로그 - 부드럽고 친근한 스타일
class UnsavedChangesDialog extends StatelessWidget {
  final String? customMessage;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const UnsavedChangesDialog({
    super.key,
    this.customMessage,
    this.onConfirm,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildResponsiveDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: SingleChildScrollView( // 스크롤 가능하도록 추가
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            // 제목 - 아이콘과 텍스트를 인라인으로 배치
            Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.edit_note_rounded,
                    size: isTablet ? 18.0 : 16.0, // 텍스트 폰트 사이즈와 동일
                    color: const Color.fromARGB(255, 245, 138, 51),
                  ),
                  const SizedBox(width: 8.0),
                  Text(
                    '편집 중인 내용이 있습니다.',
                    style: TextStyle(
                      fontSize: isTablet ? 18.0 : 16.0,
                      fontWeight: FontWeight.w500, // bold 대신 w500
                      color: AppColors.onboardingTextSecondary, // 부드러운 색상
                      fontFamily: 'Pretendard',
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            Center(
              child: Text(
                customMessage ?? '저장하지 않고\n나가시겠습니까?',
                style: TextStyle(
                  fontSize: isTablet ? 15.0 : 14.0,
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral60, // 더 부드러운 색상
                  height: 1.4,
                  fontFamily: 'Pretendard',
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 2),

            // 부드러운 버튼들
            Row(
              children: [
                // 계속 편집 버튼 (Secondary Style)
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '계속 편집',
                    onPressed: () {
                      Navigator.of(context).pop(false);
                      onCancel?.call();
                    },
                    isTablet: isTablet,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: 16.0),

                // 나가기 버튼 (Primary Style - 부드러운 톤)
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '나가기',
                    onPressed: () {
                      Navigator.of(context).pop(true);
                      onConfirm?.call();
                    },
                    isTablet: isTablet,
                    isPrimary: true,
                  ),
                ),
              ],
            ),
          ],
          ),
        ),
      ),
    );
  }

  /// 미저장 변경사항 확인 다이얼로그 표시
  static Future<bool?> show({
    required BuildContext context,
    String? message,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => UnsavedChangesDialog(
        customMessage: message,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }
}
