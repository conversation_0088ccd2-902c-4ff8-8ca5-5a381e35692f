<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>관리자 대시보드 - 바라부스매니저</title>
    <link rel="icon" type="image/png" href="favicon.png">

    <!-- Firebase SDK -->
    <script type="module">
        // Firebase 설정
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { initializeAppCheck, ReCaptchaV3Provider } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app-check.js";

        const firebaseConfig = {
            apiKey: "AIzaSyCqCc7aTMTXAGJfpb7fYe713EuTbGKEzMI",
            authDomain: "parabara-1a504.firebaseapp.com",
            projectId: "parabara-1a504",
            storageBucket: "parabara-1a504.firebasestorage.app",
            messagingSenderId: "699872938105",
            appId: "1:699872938105:web:c4c31cde360147caf3aca8",
            measurementId: "G-5R2T1KEEGH"
        };

        // Firebase 초기화
        const app = initializeApp(firebaseConfig);

        // App Check 초기화 (v3 백그라운드)
        const appCheck = initializeAppCheck(app, {
            provider: new ReCaptchaV3Provider('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S'),
            isTokenAutoRefreshEnabled: true
        });

        console.log('Firebase App Check 초기화 완료');
        window.firebaseApp = app;
        window.appCheck = appCheck;
    </script>

    <!-- reCAPTCHA v3 SDK (백그라운드) -->
    <script src="https://www.google.com/recaptcha/api.js?render=6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S" async defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F8F9FA;
            color: #495057;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header Section */
        .header-section {
            background: white;
            border-bottom: 1px solid #E9ECEF;
            padding: 16px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #495057;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #495057;
            color: white;
        }

        .btn-primary:hover {
            background: #343A40;
        }

        .btn-secondary {
            background: #F8F9FA;
            color: #495057;
            border: 1px solid #DEE2E6;
        }

        .btn-secondary:hover {
            background: #E9ECEF;
        }

        /* Main Content */
        .main-content {
            padding: 24px 0;
        }

        /* Stats Cards - 원본과 동일하게 */
        .stats-grid {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
        }

        .stats-card {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
        }

        .stats-card-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stats-icon {
            font-size: 32px;
            color: #6C757D;
            margin-right: auto;
        }

        .stats-value {
            font-size: 32px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .stats-title {
            font-size: 14px;
            color: #6C757D;
            font-weight: 500;
        }

        /* System Monitor */
        .system-monitor {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
        }

        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .monitor-item {
            text-align: center;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
        }

        .monitor-label {
            font-size: 12px;
            color: #6C757D;
            margin-bottom: 4px;
        }

        .monitor-value {
            font-size: 18px;
            font-weight: 600;
            color: #495057;
        }

        /* Users Table */
        .users-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #DEE2E6;
            border-radius: 6px;
            font-size: 14px;
            min-width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #DEE2E6;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #E9ECEF;
        }

        .users-table th {
            background: #F8F9FA;
            font-weight: 600;
            color: #495057;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #E9ECEF;
        }

        .pagination-info {
            font-size: 14px;
            color: #6C757D;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #495057;
        }

        .page-size-selector select {
            padding: 4px 8px;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            font-size: 14px;
        }

        .pagination-buttons {
            display: flex;
            gap: 4px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #CED4DA;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #E9ECEF;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #007BFF;
            color: white;
            border-color: #007BFF;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #D4EDDA;
            color: #155724;
        }

        .status-inactive {
            background: #F8D7DA;
            color: #721C24;
        }

        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn-view {
            background: #E3F2FD;
            color: #1976D2;
        }

        .action-btn-view:hover {
            background: #BBDEFB;
        }

        .action-btn-manage {
            background: #EDE7F6;
            color: #6366F1;
        }

        .action-btn-manage:hover {
            background: #D1C4E9;
        }

        /* 구독 관리 모달 스타일 */
        .subscription-status {
            margin-bottom: 24px;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
            border: 1px solid #E9ECEF;
        }

        .subscription-status h4 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
        }

        .subscription-actions {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .action-section {
            padding: 16px;
            border: 1px solid #E9ECEF;
            border-radius: 8px;
        }

        .action-section h4 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
        }

        .action-section p {
            margin: 0 0 12px 0;
            color: #6C757D;
            font-size: 12px;
        }

        .input-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .input-group input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #E9ECEF;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #6366F1;
            color: white;
        }

        .btn-primary:hover {
            background: #5B5BD6;
        }

        .btn-success {
            background: #10B981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #DC3545;
            color: white;
        }

        .btn-danger:hover {
            background: #C82333;
        }

        .btn-secondary {
            background: #6C757D;
            color: white;
        }

        .btn-secondary:hover {
            background: #5A6268;
        }

        /* 구독 상세 정보 스타일 */
        .subscription-details {
            margin: 20px 0;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
            border: 1px solid #E9ECEF;
        }

        .subscription-details h4 {
            margin: 0 0 12px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #E9ECEF;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-item label {
            font-weight: 600;
            color: #495057;
            font-size: 12px;
        }

        .detail-item span {
            color: #6C757D;
            font-size: 12px;
        }

        /* 구독 상태 정보 스타일 */
        .status-info {
            display: inline-block;
            padding: 8px 12px;
            background: #E3F2FD;
            color: #1976D2;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 사용자 상세 모달 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 24px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #495057;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #6C757D;
        }

        .close:hover {
            color: #495057;
        }

        .info-section {
            margin-bottom: 24px;
        }

        .info-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #F8F9FA;
        }

        .info-label {
            font-weight: 500;
            color: #6C757D;
        }

        .info-value {
            color: #495057;
        }

        /* Footer */
        .footer {
            background: white;
            border-top: 1px solid #E9ECEF;
            padding: 40px 24px;
            text-align: center;
            margin-top: 48px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
        }

        .footer-logo-icon {
            padding: 8px;
            background: linear-gradient(135deg, #E09A74, #D08052);
            border-radius: 8px;
            margin-right: 12px;
            font-size: 20px;
        }

        .footer-logo-text {
            font-size: 18px;
            font-weight: 700;
            color: #495057;
        }

        .footer-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .footer-info-item {
            font-size: 14px;
            color: #6C757D;
        }

        .copyright {
            font-size: 12px;
            color: #ADB5BD;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .footer-info {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <section class="header-section">
        <div class="container">
            <div class="header-content">
                <h1 class="header-title">바라부스매니저 관리자</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="refreshData()">
                        🔄 새로고침
                    </button>
                    <button class="btn btn-secondary" onclick="viewLogs()">
                        📄 시스템 로그
                    </button>
                    <button class="btn btn-primary" onclick="logout()">
                        🚪 로그아웃
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="main-content">
        <div class="container">




            <!-- 사용자 관리 -->
            <div class="users-section">
                <h2 class="section-title">사용자 관리</h2>
                <div class="table-controls">
                    <input type="text" class="search-input" placeholder="사용자 검색..." id="searchInput">
                    <select class="filter-select" id="statusFilter">
                        <option value="all">전체 유저</option>
                        <option value="plus">플러스 플랜</option>
                        <option value="free">무료 플랜</option>
                    </select>
                </div>
                <div class="table-wrapper">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>이메일</th>
                                <th>닉네임</th>
                                <th>전화번호</th>
                                <th>구독 상태</th>
                                <th>가입일</th>
                                <th>작업</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="6" style="text-align: center; padding: 40px;">
                                    데이터를 불러오는 중...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 페이지네이션 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span id="usersPaginationInfo">총 0개 중 0-0개 표시</span>
                    </div>
                    <div class="pagination-controls">
                        <div class="page-size-selector">
                            <span>페이지당</span>
                            <select id="usersPageSize" onchange="changeUsersPageSize(this.value)">
                                <option value="20">20개</option>
                                <option value="50">50개</option>
                                <option value="100">100개</option>
                            </select>
                        </div>
                        <div class="pagination-buttons" id="usersPaginationButtons">
                            <!-- 페이지 버튼들이 여기에 동적으로 생성됩니다 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- 사용자 상세보기 모달 -->
    <div id="userDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">사용자 상세 정보</h2>
                <span class="close" onclick="closeUserDetailModal()">&times;</span>
            </div>

            <div class="info-section">
                <div class="info-section-title">기본 정보</div>
                <div class="info-row">
                    <span class="info-label">UID</span>
                    <span class="info-value" id="modalUid">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">이메일</span>
                    <span class="info-value" id="modalEmail">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">닉네임</span>
                    <span class="info-value" id="modalNickname">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">전화번호</span>
                    <span class="info-value" id="modalPhone">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">가입일</span>
                    <span class="info-value" id="modalCreatedAt">-</span>
                </div>
            </div>

            <div class="info-section">
                <div class="info-section-title">구독 정보</div>
                <div class="info-row">
                    <span class="info-label">구독 상태</span>
                    <span class="info-value" id="modalSubscriptionStatus">-</span>
                </div>
            </div>



            <div style="text-align: right; margin-top: 24px;">
                <button class="btn btn-secondary" onclick="closeUserDetailModal()">닫기</button>
            </div>
        </div>
    </div>

    <!-- 구독 관리 모달 -->
    <div id="subscriptionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="subscriptionModalTitle">구독 관리</h2>
                <span class="close" onclick="closeSubscriptionModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="subscriptionModalContent">
                    <!-- 동적으로 생성됨 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 전역 변수
        let currentPage = 1;
        let totalPages = 1;
        let pageSize = 20;
        let totalUsers = 0;
        let isLoading = false;
        let authToken = null;

        // API 엔드포인트
        const API_ENDPOINTS = {
            adminDashboard: 'https://admindashboard-kahfshl2oa-uc.a.run.app',
            adminUsers: 'https://adminusers-kahfshl2oa-uc.a.run.app',
            adminSubscriptionManagement: 'https://us-central1-parabara-1a504.cloudfunctions.net/adminSubscriptionManagement',
            getAdminSystemInfo: 'https://us-central1-parabara-1a504.cloudfunctions.net/getAdminSystemInfo'
        };

        // 인증 헤더 생성
        function getAuthHeaders() {
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };
        }

        // 토큰 확인 및 로그인 상태 검증
        function checkAuth() {
            authToken = localStorage.getItem('admin_token');
            if (!authToken) {
                window.location.href = '/admin-login.html';
                return false;
            }
            return true;
        }

        // 로딩 상태 설정
        function setLoading(loading) {
            isLoading = loading;
            const refreshBtn = document.querySelector('[onclick="refreshData()"]');
            if (refreshBtn) {
                refreshBtn.disabled = loading;
                refreshBtn.textContent = loading ? '🔄 로딩중...' : '🔄 새로고침';
            }
        }

        // 대시보드 통계 로드 (제거됨)
        async function loadDashboardStats() {
            // 통계 카드가 제거되어 더 이상 필요하지 않음
            return Promise.resolve();
        }

        // 시스템 정보 로드
        async function loadSystemInfo() {
            try {
                const response = await fetch(API_ENDPOINTS.getAdminSystemInfo);
                const data = await response.json();

                if (data.success && data.data) {
                    const systemInfo = data.data;

                    // systemStats가 있는 경우 해당 정보 사용
                    if (systemInfo.systemStats) {
                        const stats = systemInfo.systemStats;
                        document.getElementById('functionCalls').textContent = stats.totalFunctionCalls || '-';
                        document.getElementById('responseTime').textContent = stats.averageResponseTime || '-';
                        document.getElementById('errorRate').textContent = stats.errorRate || '-';
                    } else {
                        // 기본값 설정
                        document.getElementById('functionCalls').textContent = '-';
                        document.getElementById('responseTime').textContent = '-';
                        document.getElementById('errorRate').textContent = '-';
                    }
                } else {
                    // 데이터가 없는 경우 기본값 설정
                    document.getElementById('functionCalls').textContent = '-';
                    document.getElementById('responseTime').textContent = '-';
                    document.getElementById('errorRate').textContent = '-';
                }
            } catch (error) {
                console.error('시스템 정보 로드 실패:', error);
                // 오류 발생 시 기본값 설정
                document.getElementById('functionCalls').textContent = '오류';
                document.getElementById('responseTime').textContent = '오류';
                document.getElementById('errorRate').textContent = '오류';
            }
        }

        // 사용자 목록 로드
        async function loadUsers(page = 1, limit = 20) {
            try {
                // 쿼리 파라미터 구성
                const params = new URLSearchParams({
                    page: page.toString(),
                    limit: limit.toString()
                });

                if (currentStatusFilter && currentStatusFilter !== 'all') {
                    params.append('statusFilter', currentStatusFilter);
                }

                if (currentSearchQuery && currentSearchQuery.trim()) {
                    params.append('searchQuery', currentSearchQuery.trim());
                }

                const response = await fetch(`${API_ENDPOINTS.adminUsers}?${params.toString()}`, {
                    headers: getAuthHeaders()
                });

                if (response.status === 401) {
                    localStorage.removeItem('admin_token');
                    window.location.href = '/admin-login.html';
                    return;
                }

                const data = await response.json();
                if (data.success && data.data) {
                    const { users, pagination } = data.data;
                    displayUsers(users);
                    updateUsersPagination(pagination);
                }
            } catch (error) {
                console.error('사용자 목록 로드 실패:', error);
                showError('사용자 목록을 불러오는데 실패했습니다.');
            }
        }

        // 사용자 목록 표시
        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            if (!users || users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 40px;">사용자가 없습니다.</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => {
                // 구독 상태를 subscription.planType으로 올바르게 계산
                let planName = '무료 플랜';
                if (user.subscription && user.subscription.planType) {
                    if (user.subscription.planType === 'SubscriptionPlanType.plus') {
                        planName = '플러스 플랜';
                    } else if (user.subscription.planType === 'SubscriptionPlanType.free') {
                        planName = '무료 플랜';
                    }
                } else if (user.subscription && user.subscription.status === 'active') {
                    // planType이 없으면 status로 판단 (하위 호환성)
                    planName = '플러스 플랜';
                }

                const isPlus = planName.includes('플러스');

                return `
                <tr>
                    <td>${user.email || 'N/A'}</td>
                    <td>${user.displayName || user.nickname || 'N/A'}</td>
                    <td>${user.phone || 'N/A'}</td>
                    <td>
                        <span class="status-badge ${isPlus ? 'status-active' : 'status-inactive'}">
                            ${planName}
                        </span>
                    </td>
                    <td>${user.createdAt ? formatUserDate(user.createdAt) : 'N/A'}</td>
                    <td>
                        <button class="action-btn action-btn-view" onclick="showUserDetails('${user.uid}', '${user.email}', '${user.displayName || user.nickname || 'N/A'}', '${planName}', '${user.createdAt || ''}', '${user.phone || 'N/A'}')">
                            상세보기
                        </button>
                        <button class="action-btn action-btn-manage" onclick="showSubscriptionManagement('${user.uid}', '${user.email}', '${planName}', ${JSON.stringify(user.subscription).replace(/"/g, '&quot;')})">
                            구독관리
                        </button>
                    </td>
                </tr>
                `;
            }).join('');
        }

        // 사용자 날짜 포맷
        function formatUserDate(dateValue) {
            if (!dateValue) return 'N/A';

            try {
                let date;
                if (typeof dateValue === 'string') {
                    date = new Date(dateValue);
                } else if (typeof dateValue === 'object' && dateValue._seconds) {
                    date = new Date(dateValue._seconds * 1000);
                } else if (typeof dateValue === 'number') {
                    date = new Date(dateValue > 1000000000000 ? dateValue : dateValue * 1000);
                } else {
                    date = new Date(dateValue);
                }

                if (isNaN(date.getTime())) {
                    return 'N/A';
                }

                return date.toLocaleDateString('ko-KR');
            } catch (error) {
                return 'N/A';
            }
        }

        // 간단한 날짜 포맷팅 함수 (구독 관리용)
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return 'N/A';

                return date.toLocaleDateString('ko-KR', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (error) {
                console.error('날짜 포맷 오류:', error);
                return 'N/A';
            }
        }

        // 구독 상태 텍스트 생성
        function getSubscriptionStatusText(subscriptionData) {
            if (!subscriptionData) return 'N/A';

            // planType으로 플러스 플랜 확인
            if (subscriptionData.planType === 'SubscriptionPlanType.plus') {
                // status에 따른 세부 구분
                switch (subscriptionData.status) {
                    case 'active':
                        return subscriptionData.nextPaymentDate ? '활성 (자동결제)' : '활성 (플러스 플랜)';
                    case 'cancel_scheduled':
                        return '취소 예정 (만료일까지 유지)';
                    case 'admin_granted':
                        return '관리자 제공 (만료일까지)';
                    default:
                        return '활성 (플러스 플랜)';
                }
            } else if (subscriptionData.planType === 'SubscriptionPlanType.free') {
                return '무료 플랜';
            }

            // status 확인 (하위 호환성)
            if (subscriptionData.status) {
                switch (subscriptionData.status) {
                    case 'active': return '활성';
                    case 'cancel_scheduled': return '취소 예정';
                    case 'admin_granted': return '관리자 제공';
                    case 'cancelled': return '취소됨';
                    case 'free': return '무료 플랜';
                    default: return subscriptionData.status;
                }
            }

            return 'N/A';
        }

        // 구독 상태별 액션 버튼 생성
        function getSubscriptionActionButtons(uid, email, subscriptionData) {
            if (!subscriptionData) return '';

            const status = subscriptionData.status;
            const hasNextPayment = !!subscriptionData.nextPaymentDate;

            let buttons = `
                <div class="action-section">
                    <h4>구독 기간 연장</h4>
                    <div class="input-group">
                        <input type="number" id="extendDays" placeholder="일수" min="1" max="365">
                        <button onclick="extendSubscription('${uid}', '${email}')" class="btn btn-success">연장</button>
                    </div>
                </div>
            `;

            // 구독 상태별 버튼 구분
            if (status === 'active' && hasNextPayment) {
                // 실제 구독 (자동결제 활성화)
                buttons += `
                    <div class="action-section">
                        <h4>구독 취소</h4>
                        <p>다음 결제일까지 구독을 유지하고 이후 자동 취소됩니다.</p>
                        <button onclick="cancelSubscription('${uid}', '${email}')" class="btn btn-danger">구독 취소</button>
                    </div>
                `;
            } else if (status === 'cancel_scheduled') {
                // 구독 취소 예정 상태
                buttons += `
                    <div class="action-section">
                        <h4>재구독</h4>
                        <p>구독 취소를 해제하고 자동결제를 재개합니다.</p>
                        <button onclick="reactivateSubscription('${uid}', '${email}')" class="btn btn-primary">재구독</button>
                    </div>
                `;
            } else if (status === 'admin_granted') {
                // 관리자 제공 상태
                buttons += `
                    <div class="action-section">
                        <h4>관리자 제공 상태</h4>
                        <p>자동결제 없이 관리자가 제공한 플러스 기능입니다.</p>
                        <span class="status-info">만료일까지 플러스 기능 사용 가능</span>
                    </div>
                `;
            }

            return buttons;
        }

        // 페이지네이션 업데이트
        function updatePagination(pagination) {
            if (pagination) {
                currentPage = pagination.currentPage || 1;
                totalPages = pagination.totalPages || 1;
            }
        }

        // 에러 메시지 표시
        function showError(message) {
            // 간단한 알림으로 표시 (실제로는 더 나은 UI 구현 가능)
            alert(message);
        }

        // 사용자 페이지네이션 업데이트
        function updateUsersPagination(pagination) {
            if (!pagination) return;

            currentPage = pagination.currentPage;
            totalPages = pagination.totalPages;
            totalUsers = pagination.totalUsers;

            // 페이지네이션 정보 업데이트
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, totalUsers);
            document.getElementById('usersPaginationInfo').textContent =
                `총 ${totalUsers}개 중 ${startIndex}-${endIndex}개 표시`;

            // 페이지네이션 버튼 생성
            generateUsersPaginationButtons();
        }

        // 사용자 페이지네이션 버튼 생성
        function generateUsersPaginationButtons() {
            const container = document.getElementById('usersPaginationButtons');
            let buttons = '';

            // 첫 페이지 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(1)" ${currentPage === 1 ? 'disabled' : ''}>첫 페이지</button>`;

            // 이전 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>이전</button>`;

            // 페이지 번호 버튼들
            const maxButtons = 5;
            let start = Math.max(1, currentPage - Math.floor(maxButtons / 2));
            let end = Math.min(totalPages, start + maxButtons - 1);

            if (end - start + 1 < maxButtons) {
                start = Math.max(1, end - maxButtons + 1);
            }

            for (let i = start; i <= end; i++) {
                buttons += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changeUsersPage(${i})">${i}</button>`;
            }

            // 다음 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>다음</button>`;

            // 마지막 페이지 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>마지막 페이지</button>`;

            container.innerHTML = buttons;
        }

        // 사용자 페이지 변경
        async function changeUsersPage(page) {
            if (page < 1 || page > totalPages || page === currentPage || isLoading) return;

            currentPage = page;
            await loadUsers(currentPage, pageSize);
        }

        // 사용자 페이지 크기 변경
        async function changeUsersPageSize(size) {
            pageSize = parseInt(size);
            currentPage = 1; // 첫 페이지로 리셋
            await loadUsers(currentPage, pageSize);
        }

        // 모든 데이터 새로고침 (최적화: 순차 로딩으로 서버 부하 분산)
        async function refreshData() {
            if (isLoading) return;

            setLoading(true);
            try {
                // 사용자 목록을 우선 로드 (가장 중요한 데이터)
                await loadUsers(currentPage, pageSize);

                // 통계는 캐시가 있으므로 나중에 로드
                await loadDashboardStats();

                // 시스템 정보는 마지막에 로드 (덜 중요)
                await loadSystemInfo();
            } finally {
                setLoading(false);
            }
        }

        // 시스템 로그 페이지로 이동
        function viewLogs() {
            window.location.href = '/admin-logs.html';
        }

        // 로그아웃
        function logout() {
            if (confirm('로그아웃하시겠습니까?')) {
                localStorage.removeItem('admin_token');
                window.location.href = '/admin-login.html';
            }
        }

        // 현재 필터 상태
        let currentSearchQuery = '';
        let currentStatusFilter = 'all';

        // 검색 기능
        function handleSearch() {
            const searchQuery = document.getElementById('searchInput').value.trim();
            currentSearchQuery = searchQuery;
            refreshData();
        }

        // 필터 변경
        function handleFilterChange() {
            const filter = document.getElementById('statusFilter').value;
            currentStatusFilter = filter;
            refreshData();
        }

        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', async function() {
            if (!checkAuth()) return;

            // 검색 및 필터 이벤트 리스너 등록
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');

            if (searchInput) {
                searchInput.addEventListener('input', debounce(handleSearch, 500));
            }

            if (statusFilter) {
                statusFilter.addEventListener('change', handleFilterChange);
            }

            // 초기 데이터 로드
            await refreshData();
        });

        // 사용자 상세보기 모달 표시
        function showUserDetails(uid, email, nickname, subscriptionStatus, createdAt, phone) {
            document.getElementById('modalUid').textContent = uid;
            document.getElementById('modalEmail').textContent = email;
            document.getElementById('modalNickname').textContent = nickname;
            document.getElementById('modalPhone').textContent = phone;
            document.getElementById('modalCreatedAt').textContent = createdAt ? formatUserDate(createdAt) : 'N/A';
            document.getElementById('modalSubscriptionStatus').textContent = subscriptionStatus;

            document.getElementById('userDetailModal').style.display = 'block';
        }

        // 사용자 상세보기 모달 닫기
        function closeUserDetailModal() {
            document.getElementById('userDetailModal').style.display = 'none';
        }

        // 구독 관리 모달 표시
        function showSubscriptionManagement(uid, email, currentStatus, subscriptionData) {
            const modal = document.getElementById('subscriptionModal');
            const modalTitle = document.getElementById('subscriptionModalTitle');
            const modalContent = document.getElementById('subscriptionModalContent');

            modalTitle.textContent = `구독 관리 - ${email}`;

            const isPlus = currentStatus.includes('플러스');

            // 구독 상세 정보 생성
            let subscriptionDetails = '';
            if (subscriptionData && isPlus) {
                subscriptionDetails = `
                    <div class="subscription-details">
                        <h4>구독 상세 정보</h4>
                        <div class="detail-grid">
                            ${subscriptionData.createdAt ? `
                                <div class="detail-item">
                                    <label>구독 시작일:</label>
                                    <span>${formatDate(subscriptionData.createdAt)}</span>
                                </div>
                            ` : ''}
                            ${subscriptionData.nextPaymentDate ? `
                                <div class="detail-item">
                                    <label>다음 결제일:</label>
                                    <span>${formatDate(subscriptionData.nextPaymentDate)}</span>
                                </div>
                            ` : subscriptionData.subscriptionEndDate ? `
                                <div class="detail-item">
                                    <label>구독 만료일:</label>
                                    <span>${formatDate(subscriptionData.subscriptionEndDate)}</span>
                                </div>
                            ` : ''}
                            ${subscriptionData.lastPaymentDate ? `
                                <div class="detail-item">
                                    <label>마지막 결제일:</label>
                                    <span>${formatDate(subscriptionData.lastPaymentDate)}</span>
                                </div>
                            ` : ''}
                            <div class="detail-item">
                                <label>구독 상태:</label>
                                <span>${getSubscriptionStatusText(subscriptionData)}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            modalContent.innerHTML = `
                <div class="subscription-status">
                    <h4>현재 구독 상태</h4>
                    <span class="status-badge ${isPlus ? 'status-active' : 'status-inactive'}">
                        ${currentStatus}
                    </span>
                </div>

                ${subscriptionDetails}

                <div class="subscription-actions">
                    ${isPlus ? `
                        ${getSubscriptionActionButtons(uid, email, subscriptionData)}

                        <div class="action-section">
                            <h4>강제 무료 전환</h4>
                            <p>즉시 무료 플랜으로 강제 전환합니다 (주의: 되돌릴 수 없음)</p>
                            <button onclick="forceFreePlan('${uid}', '${email}')" class="btn btn-secondary">강제 무료 전환</button>
                        </div>
                    ` : `
                        <div class="action-section">
                            <h4>구독 활성화 (날짜 지정)</h4>
                            <div class="input-group">
                                <input type="date" id="endDate" min="${new Date().toISOString().split('T')[0]}">
                                <button onclick="activateSubscriptionWithDate('${uid}', '${email}')" class="btn btn-primary">활성화</button>
                            </div>
                        </div>

                        <div class="action-section">
                            <h4>구독 활성화 (일수 지정)</h4>
                            <div class="input-group">
                                <input type="number" id="activateDays" placeholder="일수" min="1" max="365">
                                <button onclick="activateSubscriptionWithDays('${uid}', '${email}')" class="btn btn-primary">활성화</button>
                            </div>
                        </div>
                    `}
                </div>
            `;

            modal.style.display = 'block';
        }

        // 구독 기간 연장
        async function extendSubscription(uid, email) {
            const days = document.getElementById('extendDays').value;
            if (!days || days <= 0) {
                alert('올바른 일수를 입력하세요.');
                return;
            }

            if (!confirm(`${email} 사용자의 구독을 ${days}일 연장하시겠습니까?`)) {
                return;
            }

            try {
                const response = await fetch(API_ENDPOINTS.adminSubscriptionManagement, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: 'add_days',
                        days: parseInt(days)
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`구독이 ${days}일 연장되었습니다.`);
                    closeSubscriptionModal();

                    // refreshData 에러를 별도 처리
                    try {
                        await refreshData();
                    } catch (refreshError) {
                        console.error('데이터 새로고침 오류:', refreshError);
                        // 구독은 성공했으므로 에러 메시지 표시하지 않음
                    }
                } else {
                    alert('구독 연장에 실패했습니다: ' + (data.error || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('구독 연장 오류:', error);
                alert('구독 연장 중 오류가 발생했습니다.');
            }
        }

        // 구독 취소
        async function cancelSubscription(uid, email) {
            if (!confirm(`${email} 사용자의 구독을 취소하시겠습니까?\n다음 결제일까지는 구독이 유지됩니다.`)) {
                return;
            }

            try {
                const response = await fetch(API_ENDPOINTS.adminSubscriptionManagement, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: 'cancel'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('구독이 취소되었습니다.');
                    closeSubscriptionModal();

                    // refreshData 에러를 별도 처리
                    try {
                        await refreshData();
                    } catch (refreshError) {
                        console.error('데이터 새로고침 오류:', refreshError);
                        // 구독은 성공했으므로 에러 메시지 표시하지 않음
                    }
                } else {
                    alert('구독 취소에 실패했습니다: ' + (data.error || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('구독 취소 오류:', error);
                alert('구독 취소 중 오류가 발생했습니다.');
            }
        }

        // 구독 활성화 (날짜 지정)
        async function activateSubscriptionWithDate(uid, email) {
            const endDate = document.getElementById('endDate').value;
            if (!endDate) {
                alert('종료 날짜를 선택하세요.');
                return;
            }

            if (!confirm(`${email} 사용자의 구독을 ${endDate}까지 활성화하시겠습니까?`)) {
                return;
            }

            try {
                const response = await fetch(API_ENDPOINTS.adminSubscriptionManagement, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: 'activate_until_date',
                        endDate: new Date(endDate).toISOString()
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`구독이 ${endDate}까지 활성화되었습니다.`);
                    closeSubscriptionModal();

                    // refreshData 에러를 별도 처리
                    try {
                        await refreshData();
                    } catch (refreshError) {
                        console.error('데이터 새로고침 오류:', refreshError);
                        // 구독은 성공했으므로 에러 메시지 표시하지 않음
                    }
                } else {
                    alert('구독 활성화에 실패했습니다: ' + (data.error || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('구독 활성화 오류:', error);
                alert('구독 활성화 중 오류가 발생했습니다.');
            }
        }

        // 구독 활성화 (일수 지정)
        async function activateSubscriptionWithDays(uid, email) {
            const days = document.getElementById('activateDays').value;
            if (!days || days <= 0) {
                alert('올바른 일수를 입력하세요.');
                return;
            }

            if (!confirm(`${email} 사용자의 구독을 ${days}일간 활성화하시겠습니까?`)) {
                return;
            }

            try {
                const response = await fetch(API_ENDPOINTS.adminSubscriptionManagement, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: 'activate_for_days',
                        days: parseInt(days)
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`구독이 ${days}일간 활성화되었습니다.`);
                    closeSubscriptionModal();

                    // refreshData 에러를 별도 처리
                    try {
                        await refreshData();
                    } catch (refreshError) {
                        console.error('데이터 새로고침 오류:', refreshError);
                        // 구독은 성공했으므로 에러 메시지 표시하지 않음
                    }
                } else {
                    alert('구독 활성화에 실패했습니다: ' + (data.error || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('구독 활성화 오류:', error);
                alert('구독 활성화 중 오류가 발생했습니다.');
            }
        }

        // 강제 무료 전환
        async function forceFreePlan(uid, email) {
            if (!confirm(`${email} 사용자를 강제로 무료 플랜으로 전환하시겠습니까?\n\n주의: 이 작업은 되돌릴 수 없으며, 즉시 적용됩니다.`)) {
                return;
            }

            try {
                const response = await fetch(API_ENDPOINTS.adminSubscriptionManagement, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: 'force_free'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('사용자가 강제로 무료 플랜으로 전환되었습니다.');
                    closeSubscriptionModal();

                    // refreshData 에러를 별도 처리
                    try {
                        await refreshData();
                    } catch (refreshError) {
                        console.error('데이터 새로고침 오류:', refreshError);
                        // 구독은 성공했으므로 에러 메시지 표시하지 않음
                    }
                } else {
                    alert('강제 무료 전환에 실패했습니다: ' + (data.error || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('강제 무료 전환 오류:', error);
                alert('강제 무료 전환 중 오류가 발생했습니다.');
            }
        }

        // 재구독 (구독 취소 해제)
        async function reactivateSubscription(uid, email) {
            if (!confirm(`${email} 사용자의 구독 취소를 해제하시겠습니까?\n자동결제가 재개됩니다.`)) {
                return;
            }

            try {
                const response = await fetch(API_ENDPOINTS.adminSubscriptionManagement, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: 'reactivate'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('재구독이 완료되었습니다.');
                    closeSubscriptionModal();

                    // refreshData 에러를 별도 처리
                    try {
                        await refreshData();
                    } catch (refreshError) {
                        console.error('데이터 새로고침 오류:', refreshError);
                    }
                } else {
                    alert('재구독에 실패했습니다: ' + (data.error || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('재구독 오류:', error);
                alert('재구독 중 오류가 발생했습니다.');
            }
        }

        // 구독 관리 모달 닫기
        function closeSubscriptionModal() {
            document.getElementById('subscriptionModal').style.display = 'none';
        }

        // 모달 외부 클릭 시 닫기
        window.onclick = function(event) {
            const userModal = document.getElementById('userDetailModal');
            const subscriptionModal = document.getElementById('subscriptionModal');

            if (event.target === userModal) {
                closeUserDetailModal();
            }
            if (event.target === subscriptionModal) {
                closeSubscriptionModal();
            }
        }

        // 디바운스 함수
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
