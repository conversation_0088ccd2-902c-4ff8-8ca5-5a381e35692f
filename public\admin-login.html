<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>관리자 로그인 - 바라부스매니저</title>
    <link rel="icon" type="image/png" href="favicon.png">

    <!-- Firebase SDK -->
    <script type="module">
        // Firebase 설정
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { initializeAppCheck, ReCaptchaV3Provider } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app-check.js";

        const firebaseConfig = {
            apiKey: "AIzaSyCqCc7aTMTXAGJfpb7fYe713EuTbGKEzMI",
            authDomain: "parabara-1a504.firebaseapp.com",
            projectId: "parabara-1a504",
            storageBucket: "parabara-1a504.firebasestorage.app",
            messagingSenderId: "699872938105",
            appId: "1:699872938105:web:c4c31cde360147caf3aca8",
            measurementId: "G-5R2T1KEEGH"
        };

        // Firebase 초기화
        const app = initializeApp(firebaseConfig);

        // App Check 초기화 (v3 백그라운드)
        const appCheck = initializeAppCheck(app, {
            provider: new ReCaptchaV3Provider('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S'),
            isTokenAutoRefreshEnabled: true
        });

        console.log('Firebase App Check 초기화 완료');
        window.firebaseApp = app;
        window.appCheck = appCheck;
    </script>

    <!-- reCAPTCHA v3 SDK (백그라운드) -->
    <script src="https://www.google.com/recaptcha/api.js?render=6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S" async defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F8F9FA;
            color: #495057;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            margin: 24px;
        }

        .login-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .login-subtitle {
            font-size: 16px;
            color: #6C757D;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #495057;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #495057;
            border-width: 2px;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #6C757D;
            font-size: 18px;
        }

        .login-button {
            width: 100%;
            height: 48px;
            background: #495057;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-top: 24px;
        }

        .login-button:hover:not(:disabled) {
            background: #343A40;
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #F8D7DA;
            color: #721C24;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
            border: 1px solid #F5C6CB;
        }

        .form-error {
            color: #DC3545;
            font-size: 14px;
            margin-top: 4px;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-card {
                padding: 24px;
            }
            
            .login-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1 class="login-title">바라부스매니저 관리자</h1>
                <p class="login-subtitle">관리자 로그인</p>
            </div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">사용자명</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        required
                        autocomplete="username"
                    >
                    <div id="usernameError" class="form-error"></div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">비밀번호</label>
                    <div class="password-container">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            👁️
                        </button>
                    </div>
                    <div id="passwordError" class="form-error"></div>
                </div>

                <!-- reCAPTCHA v3는 백그라운드에서 자동 실행 -->

                <button type="submit" id="loginButton" class="login-button">
                    <span id="loginButtonText">로그인</span>
                    <div id="loginSpinner" class="loading-spinner" style="display: none;"></div>
                </button>
            </form>
        </div>
    </div>

    <script>
        let isLoading = false;
        let recaptchaV3Token = null;

        // reCAPTCHA v3 초기화 및 토큰 획득
        function initRecaptchaV3() {
            if (typeof grecaptcha !== 'undefined') {
                grecaptcha.ready(function() {
                    grecaptcha.execute('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S', {action: 'admin_login'})
                        .then(function(token) {
                            recaptchaV3Token = token;
                            console.log('reCAPTCHA v3 토큰 획득 완료');
                        })
                        .catch(function(error) {
                            console.error('reCAPTCHA v3 오류:', error);
                        });
                });
            }
        }

        // 페이지 로드 시 reCAPTCHA v3 초기화
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initRecaptchaV3, 1000);
        });

        // 비밀번호 표시/숨김 토글
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }

        // 에러 메시지 표시
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 에러 메시지 숨김
        function hideError() {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.style.display = 'none';
        }

        // 로딩 상태 설정
        function setLoading(loading) {
            isLoading = loading;
            const button = document.getElementById('loginButton');
            const buttonText = document.getElementById('loginButtonText');
            const spinner = document.getElementById('loginSpinner');
            
            if (loading) {
                button.disabled = true;
                buttonText.style.display = 'none';
                spinner.style.display = 'block';
            } else {
                button.disabled = false;
                buttonText.style.display = 'block';
                spinner.style.display = 'none';
            }
        }

        // 폼 유효성 검사
        function validateForm() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            let isValid = true;
            
            // 사용자명 검사
            const usernameError = document.getElementById('usernameError');
            if (!username) {
                usernameError.textContent = '사용자명을 입력해주세요';
                isValid = false;
            } else {
                usernameError.textContent = '';
            }
            
            // 비밀번호 검사
            const passwordError = document.getElementById('passwordError');
            if (!password) {
                passwordError.textContent = '비밀번호를 입력해주세요';
                isValid = false;
            } else {
                passwordError.textContent = '';
            }

            // reCAPTCHA v3는 백그라운드에서 자동 실행되므로 별도 검증 불필요

            return isValid;
        }

        // 관리자 로그인 API 호출
        async function adminLogin(username, password, recaptchaToken) {
            const response = await fetch('https://adminauth-kahfshl2oa-uc.a.run.app', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    recaptchaToken: recaptchaToken, // reCAPTCHA 토큰 추가
                }),
            });

            const data = await response.json();
            return data;
        }

        // 토큰 저장
        function saveToken(token) {
            localStorage.setItem('admin_token', token);
        }

        // 로그인 처리
        async function handleLogin(event) {
            event.preventDefault();
            
            if (isLoading) return;
            if (!validateForm()) return;
            
            hideError();
            setLoading(true);
            
            try {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                
                // 로그인 시 새로운 v3 토큰 획득
                if (!recaptchaV3Token) {
                    await new Promise((resolve) => {
                        grecaptcha.ready(function() {
                            grecaptcha.execute('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S', {action: 'admin_login'})
                                .then(function(token) {
                                    recaptchaV3Token = token;
                                    resolve();
                                });
                        });
                    });
                }

                const response = await adminLogin(username, password, recaptchaV3Token);
                
                if (response.success && response.token) {
                    saveToken(response.token);
                    // 대시보드로 이동
                    window.location.href = '/admin-dashboard-web.html';
                } else {
                    showError(response.message || '로그인에 실패했습니다.');
                }
            } catch (error) {
                console.error('로그인 오류:', error);
                showError('로그인 중 오류가 발생했습니다: ' + error.message);
            } finally {
                setLoading(false);
            }
        }

        // 이벤트 리스너 등록
        document.getElementById('loginForm').addEventListener('submit', handleLogin);

        // 엔터 키로 로그인
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && !isLoading) {
                handleLogin(event);
            }
        });

        // 페이지 로드 시 토큰 확인
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('admin_token');
            if (token) {
                // 이미 로그인된 경우 대시보드로 이동
                window.location.href = '/admin-dashboard-web.html';
            }
        });
    </script>
</body>
</html>
