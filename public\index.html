<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>바라 부스 매니저 - Parabara</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FAFAFA;
            color: #1F2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(to bottom, #F8FAFC, #E2E8F0);
            padding: 80px 24px;
            text-align: center;
        }

        .hero-icon {
            width: 96px;
            height: 96px;
            background: linear-gradient(135deg, #E09A74, #D08052);
            border-radius: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 32px;
            box-shadow: 0 4px 12px rgba(224, 154, 116, 0.15);
            font-size: 56px;
        }

        .hero-title {
            font-size: 36px;
            font-weight: 800;
            color: #1F2937;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-description {
            font-size: 18px;
            color: #6B7280;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 16px 24px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #E09A74;
            color: white;
        }

        .btn-primary:hover {
            background-color: #D08052;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: #6366F1;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5856EB;
            transform: translateY(-1px);
        }

        /* Features Section */
        .features-section {
            padding: 80px 24px;
            text-align: center;
        }

        .section-title {
            font-size: 32px;
            font-weight: 800;
            color: #1F2937;
            margin-bottom: 16px;
        }

        .section-subtitle {
            font-size: 16px;
            color: #6B7280;
            margin-bottom: 48px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-top: 48px;
        }

        @media (max-width: 1200px) {
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        .feature-card {
            background: white;
            padding: 32px;
            border-radius: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
            border: 1px solid #F3F4F6;
            text-align: center;
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #E09A74, #D08052);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 28px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 12px;
        }

        .feature-description {
            font-size: 14px;
            color: #6B7280;
            line-height: 1.5;
        }

        /* Links Section */
        .links-section {
            background-color: #F9FAFB;
            padding: 80px 24px;
            text-align: center;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-top: 40px;
        }

        .link-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
            border: 1px solid #F3F4F6;
            text-decoration: none;
            color: inherit;
            transition: all 0.2s;
            display: block;
        }

        .link-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 0 16px 40px rgba(0, 0, 0, 0.04);
        }

        .link-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }

        .link-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
        }

        /* Footer */
        .footer {
            background: white;
            border-top: 1px solid #E5E7EB;
            padding: 40px 24px;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .company-info {
            margin-bottom: 24px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .company-details {
            font-size: 14px;
            color: #6B7280;
            line-height: 1.6;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .footer-link {
            color: #6B7280;
            text-decoration: none;
            font-size: 14px;
        }

        .footer-link:hover {
            color: #E09A74;
        }

        .copyright {
            font-size: 12px;
            color: #9CA3AF;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 28px;
            }

            .hero-description {
                font-size: 16px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 280px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .links-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-icon">
                🏪
            </div>
            <h1 class="hero-title">바라 부스 매니저</h1>
            <p class="hero-description">
                창작자를 위한 행사 부스 통합 관리 애플리케이션<br>
                매출, 재고, 고객을 하나의 앱으로 스마트하게 관리하세요
            </p>
            <div class="hero-buttons">
                <a href="https://apps.apple.com/app/id6738046369" class="btn btn-primary" target="_blank">
                    🍎 App Store
                </a>
                <a href="https://play.google.com/store/apps/details?id=com.parabara.booth_manager" class="btn btn-secondary" target="_blank">
                    🤖 Google Play
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <h2 class="section-title">주요 기능</h2>
            <p class="section-subtitle">부스 운영에 필요한 모든 기능을 하나의 앱으로</p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">매출 관리</h3>
                    <p class="feature-description">실시간 매출 현황 추적과<br>상세한 분석 리포트</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📦</div>
                    <h3 class="feature-title">재고 관리</h3>
                    <p class="feature-description">상품별 재고 현황을<br>실시간으로 관리</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3 class="feature-title">고객 관리</h3>
                    <p class="feature-description">고객 정보와 구매 이력을<br>체계적으로 관리</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3 class="feature-title">모바일 최적화</h3>
                    <p class="feature-description">언제 어디서나 스마트폰으로<br>빠르게 확인</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Links Section -->
    <section class="links-section">
        <div class="container">
            <h2 class="section-title">도움이 필요하신가요?</h2>

            <div class="links-grid">
                <a href="/privacy-policy" class="link-card">
                    <div class="link-icon">📋</div>
                    <div class="link-title">개인정보 처리방침</div>
                </a>
                <a href="/account-deletion" class="link-card">
                    <div class="link-icon">🗑️</div>
                    <div class="link-title">계정 삭제</div>
                </a>
                <a href="/support" class="link-card">
                    <div class="link-icon">🎧</div>
                    <div class="link-title">고객 지원</div>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">


            <!-- 회사 정보 -->
            <div style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 16px;">
                <div style="font-size: 14px; color: #6B7280;"><strong>상호명:</strong> 바라부스매니저</div>
                <div style="font-size: 14px; color: #6B7280;"><strong>대표:</strong> 권태영</div>
                <div style="font-size: 14px; color: #6B7280;"><strong>전화:</strong> 070-8080-4308</div>
                <div style="font-size: 14px; color: #6B7280;"><strong>이메일:</strong> <EMAIL></div>
            </div>

            <div style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 24px;">
                <div style="font-size: 14px; color: #6B7280;"><strong>주소:</strong> 서울 강서구 마곡동 마곡중앙로 36 1504동 1301호</div>
                <div style="font-size: 14px; color: #6B7280;"><strong>사업자등록번호:</strong> 184-53-01069</div>
                <div style="font-size: 14px; color: #6B7280;"><strong>통신판매업신고:</strong> 제2025-서울강서-2441호</div>
                <div style="font-size: 14px; color: #6B7280;"><strong>개인정보관리책임자:</strong> 권태영</div>
            </div>

            <hr style="border: none; border-top: 1px solid #E5E7EB; margin-bottom: 16px;">

            <div class="footer-links">
                <a href="/privacy-policy" class="footer-link">개인정보 처리방침</a>
                <a href="/account-deletion" class="footer-link">계정 삭제</a>
                <a href="/support" class="footer-link">고객지원</a>
            </div>

            <div class="copyright">
                © 2025 바라부스매니저. All rights reserved.
            </div>
        </div>
    </footer>
</body>
</html>