<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>고객 지원 - 바라부스매니저</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FFFFFF;
            color: #1F2937;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header Section */
        .header-section {
            background-color: #F8F9FA;
            padding: 60px 24px;
            text-align: center;
        }

        .header-icon {
            width: 80px;
            height: 80px;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 40px;
            color: #2563EB;
        }

        .header-title {
            font-size: 28px;
            font-weight: 800;
            color: #1F2937;
            margin-bottom: 12px;
        }

        .header-description {
            font-size: 16px;
            color: #6B7280;
            line-height: 1.6;
        }

        /* Content Section */
        .content-section {
            padding: 60px 24px;
        }

        .contact-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 20px;
            padding: 32px;
            text-align: center;
            color: white;
            margin-bottom: 48px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
        }

        .contact-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .contact-email {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            color: #FFD700;
        }

        .response-time {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
        }

        .response-time-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .response-time-text {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
        }

        /* FAQ Section */
        .faq-title {
            font-size: 28px;
            font-weight: 700;
            color: #1F2937;
            text-align: center;
            margin-bottom: 32px;
        }

        .faq-item {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
        }

        .faq-question {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 12px;
        }

        .faq-icon {
            margin-right: 12px;
            color: #10b981;
            font-size: 20px;
        }

        .faq-answer {
            color: #6B7280;
            line-height: 1.5;
            padding-left: 32px;
        }

        /* Footer */
        .footer {
            background: white;
            border-top: 1px solid #E5E7EB;
            padding: 40px 24px;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
        }

        .footer-logo-icon {
            padding: 8px;
            background: linear-gradient(135deg, #E09A74, #D08052);
            border-radius: 8px;
            margin-right: 12px;
            font-size: 20px;
        }

        .footer-logo-text {
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
        }

        .footer-info {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .footer-info-item {
            font-size: 14px;
            color: #6B7280;
        }

        .footer-divider {
            border: none;
            border-top: 1px solid #E5E7EB;
            margin: 24px 0 16px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .footer-link {
            color: #6B7280;
            text-decoration: none;
            font-size: 14px;
        }

        .footer-link:hover {
            color: #E09A74;
        }

        .copyright {
            font-size: 12px;
            color: #9CA3AF;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-title {
                font-size: 24px;
            }
            
            .header-description {
                font-size: 14px;
            }
            
            .contact-email {
                font-size: 20px;
            }
            
            .faq-answer {
                padding-left: 0;
                margin-top: 8px;
            }
            
            .footer-info {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
            
            .footer-links {
                flex-direction: column;
                align-items: center;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <section class="header-section">
        <div class="container">
            <div class="header-icon">🎧</div>
            <h1 class="header-title">고객 지원</h1>
            <p class="header-description">
                바라 부스 매니저 사용 중 문의사항이나 도움이 필요하시면<br>
                언제든지 연락해 주세요.
            </p>
        </div>
    </section>

    <!-- Content Section -->
    <section class="content-section">
        <div class="container">
            <!-- 연락처 카드 -->
            <div class="contact-card">
                <div class="contact-title">📧 고객지원 이메일</div>
                <div class="contact-email"><EMAIL></div>
                
                <div class="response-time">
                    <div class="response-time-title">📞 응답 시간</div>
                    <div class="response-time-text">
                        • 평일: 48시간 이내 응답<br>
                        • 주말/공휴일: 72시간 이내 응답<br>
                        • 긴급한 문제: 가능한 한 빠른 시간 내 응답
                    </div>
                </div>
            </div>

            <!-- FAQ 섹션 -->
            <h2 class="faq-title">자주 묻는 질문 (FAQ)</h2>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">📱</span>
                    Q. 앱이 실행되지 않아요.
                </div>
                <div class="faq-answer">
                    A. 앱을 완전히 종료한 후 다시 실행해 보세요. 문제가 지속되면 앱을 재설치하거나 기기를 재시작해 보세요.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">🔄</span>
                    Q. 데이터가 동기화되지 않아요.
                </div>
                <div class="faq-answer">
                    A. 인터넷 연결을 확인하고, 앱 설정에서 동기화 기능이 활성화되어 있는지 확인해 주세요.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">🗑️</span>
                    Q. 계정을 삭제하고 싶어요.
                </div>
                <div class="faq-answer">
                    A. 앱 내 설정 → 계정 관리에서 삭제하거나, 계정 삭제 페이지를 참고해 주세요.
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">❌</span>
                    Q. 구독을 취소하고 싶어요.
                </div>
                <div class="faq-answer">
                    A. 앱 내 설정 → 구독 관리에서 취소하거나, 고객 지원으로 연락해 주세요.
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">


            <!-- 회사 정보 -->
            <div class="footer-info">
                <div class="footer-info-item"><strong>상호명:</strong> 바라부스매니저</div>
                <div class="footer-info-item"><strong>대표:</strong> 권태영</div>
                <div class="footer-info-item"><strong>전화:</strong> 070-8080-4308</div>
                <div class="footer-info-item"><strong>이메일:</strong> <EMAIL></div>
            </div>
            
            <div class="footer-info">
                <div class="footer-info-item"><strong>주소:</strong> 서울 강서구 마곡동 마곡중앙로 36 1504동 1301호</div>
                <div class="footer-info-item"><strong>사업자등록번호:</strong> 184-53-01069</div>
                <div class="footer-info-item"><strong>통신판매업신고:</strong> 제2025-서울강서-2441호</div>
                <div class="footer-info-item"><strong>개인정보관리책임자:</strong> 권태영</div>
            </div>
            
            <hr class="footer-divider">
            
            <div class="footer-links">
                <a href="/" class="footer-link">홈</a>
                <a href="/privacy-policy.html" class="footer-link">개인정보 처리방침</a>
                <a href="/account-deletion.html" class="footer-link">계정 삭제</a>
                <a href="/support.html" class="footer-link">고객지원</a>
            </div>
            
            <div class="copyright">
                © 2025 바라부스매니저. All rights reserved.
            </div>
        </div>
    </footer>
</body>
</html>
