# 바라 부스 매니저 - Local CI Script (PowerShell)
# Windows 환경에서 CI 파이프라인을 실행하는 스크립트

param(
    [switch]$SkipBuild,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 바라 부스 매니저 - Local CI Pipeline 시작" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

$StartTime = Get-Date

function Write-LogInfo {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}
function Write-LogSuccess {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}
function Write-LogWarning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}
function Write-LogError {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-LogInfo "1. 의존성 설치 중..."
flutter pub get
if ($LASTEXITCODE -eq 0) {
    Write-LogSuccess "의존성 설치 완료"
} else {
    Write-LogError "의존성 설치 실패"
    exit 1
}

Write-LogInfo "2. 코드 품질 검사 중..."
Write-LogInfo "  - 코드 분석 실행..."
flutter analyze
if ($LASTEXITCODE -eq 0) {
    Write-LogSuccess "코드 분석 완료"
} else {
    Write-LogError "코드 분석 실패"
    exit 1
}
Write-LogInfo "  - 코드 포맷 검사..."
try {
    dart format --set-exit-if-changed .
    Write-LogSuccess "코드 포맷 검사 완료"
} catch {
    Write-LogWarning "코드 포맷 문제 발견. 'dart format .' 명령으로 수정하세요."
}
Write-LogInfo "  - 커스텀 린트 검사..."
if (Test-Path "custom_lint.log") {
    $lintContent = Get-Content "custom_lint.log" -Raw
    if ($lintContent -match "ERROR") {
        Write-LogError "커스텀 린트 오류 발견:"
        Get-Content "custom_lint.log" | Where-Object { $_ -match "ERROR" }
        exit 1
    } else {
        Write-LogSuccess "커스텀 린트 검사 완료"
    }
} else {
    Write-LogInfo "커스텀 린트 로그 파일이 없습니다."
}

Write-LogInfo "3. 테스트 실행 중..."
flutter test --coverage
if ($LASTEXITCODE -eq 0) {
    Write-LogSuccess "테스트 완료"
} else {
    Write-LogError "테스트 실패"
    exit 1
}

Write-LogInfo "4. 성능 테스트 실행 중..."
flutter test test/utils/memory_manager_test.dart --verbose
flutter test test/utils/batch_processor_test.dart --verbose
Write-LogSuccess "성능 테스트 완료"

Write-LogInfo "5. 빌드 검증 단계..."
if (-not $SkipBuild) {
    Write-LogInfo "  - 웹 빌드..."
    flutter build web --release
    if ($LASTEXITCODE -eq 0) {
        Write-LogSuccess "웹 빌드 완료"
    } else {
        Write-LogError "웹 빌드 실패"
        exit 1
    }
    Write-LogInfo "  - Windows 빌드..."
    flutter build windows
    if ($LASTEXITCODE -eq 0) {
        Write-LogSuccess "Windows 빌드 완료"
    } else {
        Write-LogWarning "Windows 빌드 실패 (계속 진행)"
    }
} else {
    Write-LogInfo "5. 빌드 검증 건너뛰기 (SkipBuild 옵션)"
}

Write-LogInfo "6. 보안 검사 중..."
flutter pub deps --style=tree
Write-LogSuccess "보안 검사 완료"

$EndTime = Get-Date
$Duration = ($EndTime - $StartTime).TotalSeconds

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-LogSuccess "🎉 Local CI Pipeline 완료!"
Write-Host "총 소요 시간: $([math]::Round($Duration, 2))초" -ForegroundColor White
Write-Host ""
Write-Host "📊 결과 요약:" -ForegroundColor White
Write-Host "- ✅ 의존성 설치" -ForegroundColor Green
Write-Host "- ✅ 코드 품질 검사" -ForegroundColor Green
Write-Host "- ✅ 테스트 실행" -ForegroundColor Green
Write-Host "- ✅ 성능 테스트" -ForegroundColor Green
if (-not $SkipBuild) {
    Write-Host "- ✅ 빌드 검증" -ForegroundColor Green
} else {
    Write-Host "- ⏭️ 빌드 검증 (건너뜀)" -ForegroundColor Yellow
}
Write-Host "- ✅ 보안 검사" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 모든 검사가 통과했습니다!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

if ($Verbose) {
    Write-Host ""
    Write-Host "💡 사용법:" -ForegroundColor Cyan
    Write-Host "  .\scripts\ci_local.ps1              # 전체 CI 실행"
    Write-Host "  .\scripts\ci_local.ps1 -SkipBuild   # 빌드 제외하고 실행"
    Write-Host "  .\scripts\ci_local.ps1 -Verbose     # 상세 정보 출력"
} 
