@echo off
echo 🔒 Parabara 최고 보안 수준 빌드 스크립트
echo ===============================================

echo.
echo [1/4] 보안 환경 변수 입력...
echo ⚠️  패스워드는 화면에 표시되지 않습니다
set /p PARABARA_KEYSTORE_PASSWORD=키스토어 비밀번호 입력: 
set /p PARABARA_KEY_PASSWORD=키 비밀번호 입력: 
set PARABARA_KEY_ALIAS=parabara-release
set PARABARA_KEYSTORE_FILE=app-release-key.jks

echo.
echo [2/4] Flutter 정리 중...
flutter clean

echo [3/4] 종속성 업데이트 중...
flutter pub get

echo [4/4] 보안 강화된 릴리즈 빌드 중...
flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info

echo.
echo ✅ 빌드 완료!
echo 📁 출력 파일: build\app\outputs\bundle\release\app-release.aab
echo 🔒 최고 보안 수준으로 컴파일됨
echo.

REM 보안을 위해 환경 변수 즉시 삭제
set PARABARA_KEYSTORE_PASSWORD=
set PARABARA_KEY_PASSWORD=
set PARABARA_KEY_ALIAS=
set PARABARA_KEYSTORE_FILE=

pause
