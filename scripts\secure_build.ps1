# 🔒 Parabara 최고 보안 수준 빌드 스크립트 (PowerShell)
Write-Host "🔒 Parabara 최고 보안 수준 빌드 스크립트" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "[1/4] 보안 환경 변수 입력..." -ForegroundColor Yellow

# 패스워드를 안전하게 입력받기 (화면에 표시되지 않음)
$keystorePassword = Read-Host -Prompt "키스토어 비밀번호 입력" -AsSecureString
$keyPassword = Read-Host -Prompt "키 비밀번호 입력" -AsSecureString

# SecureString을 환경 변수로 설정
$env:PARABARA_KEYSTORE_PASSWORD = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($keystorePassword))
$env:PARABARA_KEY_PASSWORD = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($keyPassword))
$env:PARABARA_KEY_ALIAS = "parabara-release"
$env:PARABARA_KEYSTORE_FILE = "app-release-key.jks"

Write-Host ""
Write-Host "[2/4] Flutter 정리 중..." -ForegroundColor Yellow
flutter clean

Write-Host "[3/4] 종속성 업데이트 중..." -ForegroundColor Yellow  
flutter pub get

Write-Host "[4/4] 보안 강화된 릴리즈 빌드 중..." -ForegroundColor Yellow
flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info

Write-Host ""
Write-Host "✅ 빌드 완료!" -ForegroundColor Green
Write-Host "📁 출력 파일: build\app\outputs\bundle\release\app-release.aab" -ForegroundColor Cyan
Write-Host "🔒 최고 보안 수준으로 컴파일됨" -ForegroundColor Green

# 보안을 위해 환경 변수 즉시 삭제
Remove-Variable keystorePassword
Remove-Variable keyPassword
$env:PARABARA_KEYSTORE_PASSWORD = $null
$env:PARABARA_KEY_PASSWORD = $null
$env:PARABARA_KEY_ALIAS = $null  
$env:PARABARA_KEYSTORE_FILE = $null

Write-Host ""
Read-Host -Prompt "아무 키나 눌러서 종료"
