@echo off
chcp 65001 >nul
echo.
echo === Firebase App Check 설정 확인 ===
echo.
echo 1. Firebase App Check 패키지 확인...
findstr "firebase_app_check" pubspec.yaml >nul
if %errorlevel%==0 (
    echo ✅ Firebase App Check 패키지가 설치되어 있습니다.
) else (
    echo ❌ Firebase App Check 패키지가 설치되지 않았습니다.
    exit /b 1
)

echo.
echo 2. 의존성 설치 확인...
flutter pub get
if %errorlevel%==0 (
    echo ✅ 의존성 설치가 완료되었습니다.
) else (
    echo ❌ 의존성 설치에 실패했습니다.
    exit /b 1
)

echo.
echo 3. 코드 분석...
flutter analyze
if %errorlevel%==0 (
    echo ✅ 코드 분석이 완료되었습니다. 오류가 없습니다.
) else (
    echo ⚠️ 코드 분석에서 문제가 발견되었습니다.
)

echo.
echo 4. 설정 파일 확인...
if exist "lib\utils\app_check_utils.dart" (
    echo ✅ lib/utils/app_check_utils.dart 파일이 존재합니다.
) else (
    echo ❌ lib/utils/app_check_utils.dart 파일이 없습니다.
)

if exist "lib\utils\app_check_config.dart" (
    echo ✅ lib/utils/app_check_config.dart 파일이 존재합니다.
) else (
    echo ❌ lib/utils/app_check_config.dart 파일이 없습니다.
)

if exist "docs\FIREBASE_APP_CHECK_SETUP.md" (
    echo ✅ docs/FIREBASE_APP_CHECK_SETUP.md 파일이 존재합니다.
) else (
    echo ❌ docs/FIREBASE_APP_CHECK_SETUP.md 파일이 없습니다.
)

echo.
echo 5. Firebase 설정 파일 확인...
if exist "android\app\google-services.json" (
    echo ✅ android/app/google-services.json 파일이 존재합니다.
) else (
    echo ⚠️ android/app/google-services.json 파일이 없습니다.
)

if exist "ios\Runner\GoogleService-Info.plist" (
    echo ✅ ios/Runner/GoogleService-Info.plist 파일이 존재합니다.
) else (
    echo ⚠️ ios/Runner/GoogleService-Info.plist 파일이 없습니다.
)

echo.
echo === 다음 단계 ===
echo 1. Firebase Console에서 App Check 활성화
echo 2. 디버그 토큰 등록:
echo    - Android: 783023AA-6A8F-47D1-8D0A-F4F3CBE6C5BC
echo    - iOS: 94180427-6269-4224-87D6-DC182E09EA70
echo 3. 앱 실행 후 로그 확인: flutter run
echo 4. 자세한 가이드: docs/FIREBASE_APP_CHECK_SETUP.md 참조
echo.
echo === 설정 완료! ===
pause
