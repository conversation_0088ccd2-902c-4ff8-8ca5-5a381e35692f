# Firebase App Check 테스트 스크립트
# PowerShell용 - UTF-8 인코딩

# 인코딩 설정 (한글 깨짐 방지)
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# UTF-8 코드페이지 설정
chcp 65001 | Out-Null

Write-Host "=== Firebase App Check 설정 확인 ===" -ForegroundColor Green

# 1. 패키지 확인
Write-Host "`n1. Firebase App Check 패키지 확인..." -ForegroundColor Yellow
$pubspecContent = Get-Content "pubspec.yaml" | Select-String "firebase_app_check"
if ($pubspecContent) {
    Write-Host "✅ Firebase App Check 패키지가 설치되어 있습니다: $pubspecContent" -ForegroundColor Green
} else {
    Write-Host "❌ Firebase App Check 패키지가 설치되지 않았습니다." -ForegroundColor Red
    exit 1
}

# 2. 의존성 설치 확인
Write-Host "`n2. 의존성 설치 확인..." -ForegroundColor Yellow
try {
    flutter pub get
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 의존성 설치가 완료되었습니다." -ForegroundColor Green
    } else {
        Write-Host "❌ 의존성 설치에 실패했습니다." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Flutter 명령 실행에 실패했습니다: $_" -ForegroundColor Red
    exit 1
}

# 3. 코드 분석
Write-Host "`n3. 코드 분석..." -ForegroundColor Yellow
try {
    flutter analyze
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 코드 분석이 완료되었습니다. 오류가 없습니다." -ForegroundColor Green
    } else {
        Write-Host "⚠️ 코드 분석에서 문제가 발견되었습니다." -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 코드 분석 실행에 실패했습니다: $_" -ForegroundColor Red
}

# 4. 설정 파일 확인
Write-Host "`n4. 설정 파일 확인..." -ForegroundColor Yellow

$configFiles = @(
    "lib/utils/app_check_utils.dart",
    "lib/utils/app_check_config.dart",
    "docs/FIREBASE_APP_CHECK_SETUP.md"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file 파일이 존재합니다." -ForegroundColor Green
    } else {
        Write-Host "❌ $file 파일이 없습니다." -ForegroundColor Red
    }
}

# 5. Firebase 설정 파일 확인
Write-Host "`n5. Firebase 설정 파일 확인..." -ForegroundColor Yellow

$firebaseFiles = @(
    "android/app/google-services.json",
    "ios/Runner/GoogleService-Info.plist"
)

foreach ($file in $firebaseFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file 파일이 존재합니다." -ForegroundColor Green
    } else {
        Write-Host "⚠️ $file 파일이 없습니다." -ForegroundColor Yellow
    }
}

# 결과 요약
Write-Host "`n=== 다음 단계 ===" -ForegroundColor Cyan
Write-Host "1. Firebase Console에서 App Check 활성화" -ForegroundColor White
Write-Host "2. 디버그 토큰 등록:" -ForegroundColor White
Write-Host "   - Android: 783023AA-6A8F-47D1-8D0A-F4F3CBE6C5BC" -ForegroundColor Gray
Write-Host "   - iOS: 94180427-6269-4224-87D6-DC182E09EA70" -ForegroundColor Gray
Write-Host "3. 앱 실행 후 로그 확인: flutter run" -ForegroundColor White
Write-Host "4. 자세한 가이드: docs/FIREBASE_APP_CHECK_SETUP.md 참조" -ForegroundColor White

Write-Host "`n=== 설정 완료! ===" -ForegroundColor Green
