#!/usr/bin/env pwsh

# 바라 부스 매니저 - 빠른 테스트 스크립트
# 수정된 코드가 정상 작동하는지 확인

Write-Host "🚀 바라 부스 매니저 테스트 시작" -ForegroundColor Green

# 1. 의존성 확인
Write-Host "📦 의존성 확인 중..." -ForegroundColor Yellow
flutter pub get

# 2. 코드 분석
Write-Host "🔍 코드 분석 중..." -ForegroundColor Yellow
flutter analyze --no-fatal-infos

# 3. 단위 테스트 실행 (간단한 것만)
Write-Host "🧪 단위 테스트 실행 중..." -ForegroundColor Yellow
flutter test test/services/database_service_test.dart --reporter=compact

# 4. 핫 리로드 가능한 디버그 실행
Write-Host "🔥 디버그 모드 실행 (핫 리로드 가능)" -ForegroundColor Green
Write-Host "👉 상품 목록이 정상적으로 표시되는지 확인하세요!" -ForegroundColor Cyan

flutter run -d windows --debug
