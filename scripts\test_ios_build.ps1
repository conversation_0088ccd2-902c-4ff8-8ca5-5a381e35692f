# iOS 빌드 테스트 스크립트
# Firebase JSON 오류 해결을 위한 정리 및 빌드

Write-Host "=== iOS 빌드 테스트 시작 ===" -ForegroundColor Green

# 1. Flutter Clean
Write-Host "1. Flutter Clean 수행..." -ForegroundColor Yellow
flutter clean

# 2. iOS Pods 정리
Write-Host "2. iOS Pods 정리..." -ForegroundColor Yellow
if (Test-Path "ios/Pods") {
    Remove-Item -Recurse -Force ios/Pods
}
if (Test-Path "ios/Podfile.lock") {
    Remove-Item -Force ios/Podfile.lock
}

# 3. Flutter Pub Get
Write-Host "3. Flutter Pub Get..." -ForegroundColor Yellow
flutter pub get

# 4. iOS Pod Install
Write-Host "4. iOS Pod Install..." -ForegroundColor Yellow
Set-Location ios
pod install --repo-update
Set-Location ..

# 5. iOS 빌드 테스트
Write-Host "5. iOS 빌드 테스트..." -ForegroundColor Yellow
flutter build ios --debug --no-codesign

Write-Host "=== iOS 빌드 테스트 완료 ===" -ForegroundColor Green
Write-Host "오류가 없다면 Xcode에서 다시 실행해보세요." -ForegroundColor Cyan
