diff --git a/lib/screens/records_and_statistics/statistics_tab_content.dart b/lib/screens/records_and_statistics/statistics_tab_content.dart
index 9d1cdd7..2cc5db2 100644
--- a/lib/screens/records_and_statistics/statistics_tab_content.dart
+++ b/lib/screens/records_and_statistics/statistics_tab_content.dart
@@ -1,6 +1,7 @@
 import 'package:flutter/material.dart';
 import 'package:flutter_riverpod/flutter_riverpod.dart';
 import 'package:syncfusion_flutter_charts/charts.dart';
+import 'package:parabara/widgets/adaptive_donut_chart.dart';
 import 'package:shared_preferences/shared_preferences.dart';
 
 import '../../providers/sales_log_provider.dart';
@@ -17,124 +18,18 @@ import '../../utils/logger_utils.dart';
 import '../../models/sales_log.dart';
 import '../../models/product.dart';
 import '../../models/prepayment.dart';
-import '../../models/event_workspace.dart';
-import '../../utils/dialog_theme.dart' as custom_dialog;
-/// 차트 데이터 모델
+
+// 실험적: Syncfusion 대신 완전 커스텀 라벨 배치를 사용하려면 true
+const bool kUseAdaptiveDonutChart = false; // 안정화 전까지 기본 false 유지
+
+// 기존 차트에서 사용하던 간단한 데이터 모델 (복원)
 class ChartData {
   final String category;
   final double value;
   final Color color;
-
   ChartData(this.category, this.value, this.color);
 }
 
-/// 실용적인 통계 탭 컨텐츠
-///
-/// 실제 판매자가 알아야 하는 핵심 비즈니스 데이터에 집중한 통계 화면입니다.
-/// - 매출, 할인, 선입금, 거래 유형별 상세 분석
-/// - 세트 할인 제공 금액 및 적용 현황
-/// - 판매자별 성과 (등수 없이)
-/// - 상품별 판매 현황
-/// - 일괄 판매 분석
-class StatisticsTabContent extends ConsumerStatefulWidget {
-  final String selectedSeller;
-  final DateTimeRange? selectedDateRange;
-  final TransactionType? selectedTransactionType;
-  final GlobalKey? chartKey; // 차트 캡처용 GlobalKey 추가
-
-  const StatisticsTabContent({
-    super.key,
-    required this.selectedSeller,
-    this.selectedDateRange,
-    this.selectedTransactionType,
-    this.chartKey,
-  });
-
-  @override
-  ConsumerState<StatisticsTabContent> createState() => _StatisticsTabContentState();
-}
-
-class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
-  int _setDiscountAmount = 0;
-  int _setDiscountCount = 0;
-
-  @override
-  void initState() {
-    super.initState();
-    _loadSetDiscountStats();
-    _loadTimeBasedSettings();
-  }
-
-  Future<void> _loadSetDiscountStats() async {
-    try {
-      final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
-      if (currentWorkspace != null) {
-        final repository = SetDiscountTransactionRepository();
-        final stats = await repository.getStatistics(currentWorkspace.id);
-        if (mounted) {
-          setState(() {
-            _setDiscountAmount = stats['totalDiscountAmount'] ?? 0;
-            _setDiscountCount = stats['totalAppliedCount'] ?? 0;
-          });
-        }
-      }
-    } catch (e) {
-      if (mounted) {
-        setState(() {
-          _setDiscountAmount = 0;
-          _setDiscountCount = 0;
-        });
-      }
-    }
-  }
-
-  /// 시간대별 매출 설정 불러오기
-  Future<void> _loadTimeBasedSettings() async {
-    try {
-      final prefs = await SharedPreferences.getInstance();
-      final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
-      if (currentWorkspace == null) return;
-
-      // 저장된 설정 불러오기
-      final savedDateString = prefs.getString('timeBased_selectedDate');
-      final savedStartHour = prefs.getInt('timeBased_startHour');
-      final savedEndHour = prefs.getInt('timeBased_endHour');
-
-      if (mounted) {
-        setState(() {
-          // 저장된 날짜가 있고 행사 기간 내라면 사용, 아니면 행사 첫날
-          if (savedDateString != null) {
-            final savedDate = DateTime.tryParse(savedDateString);
-            if (savedDate != null &&
-                !savedDate.isBefore(currentWorkspace.startDate) &&
-                !savedDate.isAfter(currentWorkspace.endDate)) {
-              _selectedDate = savedDate;
-            } else {
-              _selectedDate = currentWorkspace.startDate;
-            }
-          } else {
-            _selectedDate = currentWorkspace.startDate;
-          }
-
-          // 저장된 시간 설정이 있으면 사용, 아니면 기본값 (10시~17시)
-          _startHour = savedStartHour ?? 10;
-          _endHour = savedEndHour ?? 17;
-        });
-      }
-    } catch (e) {
-      LoggerUtils.logError('시간대별 매출 설정 불러오기 실패', error: e);
-      // 오류 시 기본값 설정
-      final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
-      if (currentWorkspace != null && mounted) {
-        setState(() {
-          _selectedDate = currentWorkspace.startDate;
-          _startHour = 10;
-          _endHour = 17;
-        });
-      }
-    }
-  }
-
   /// 시간대별 매출 설정 저장하기
   Future<void> _saveTimeBasedSettings() async {
     try {
@@ -607,6 +502,26 @@ class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
       );
   }
 
+  /// 긴 카테고리 라벨을 2줄로 분리하여 도넛 외부 라벨 겹침을 줄임
+  String _breakCategoryLabel(String category) {
+    // 특정 단어 맞춤 분리
+    if (category == '아크릴스탠드') return '아크릴\n스탠드';
+    if (category == '아크릴키링') return '아크릴\n키링';
+
+    // 너무 짧으면 그대로
+    if (category.length <= 4) return category;
+
+    // 가운데 기준 분리 (홀수 길이는 앞부분이 한 글자 더 많도록)
+    final mid = (category.length / 2).floor();
+    final first = category.substring(0, mid);
+    final second = category.substring(mid);
+
+    // 이미 분리된 형태면 그대로
+    if (first.endsWith('\n') || second.startsWith('\n')) return category;
+
+    return '$first\n$second';
+  }
+
   /// 지표 그리드 빌더 (2x2 형태)
   Widget _buildMetricsGrid(Map<String, dynamic> stats) {
     // 지표 데이터
@@ -855,11 +770,12 @@ class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
             RepaintBoundary(
               key: widget.chartKey,
               child: SizedBox(
-                height: 250, // 200 → 250으로 크기 증가
+                height: 480, // 380 → 480px로 더 증가하여 라벨 공간 확보
                 child: SfCircularChart(
+                margin: const EdgeInsets.symmetric(vertical: 80, horizontal: 40), // 마진 더 증가
                 legend: Legend(
                   isVisible: true,
-                  position: LegendPosition.right,
+                  position: LegendPosition.bottom,
                   textStyle: const TextStyle(
                     fontFamily: 'Pretendard',
                     fontSize: 11,
@@ -900,38 +816,55 @@ class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
                     xValueMapper: (ChartData data, _) => data.category,
                     yValueMapper: (ChartData data, _) => data.value,
                     pointColorMapper: (ChartData data, _) => data.color,
-                    innerRadius: '60%',
+                    dataLabelMapper: (ChartData data, _) => data.category,
+                    innerRadius: '50%', // 더 얇게 하여 외부 라벨 공간 극대화
                     dataLabelSettings: DataLabelSettings(
                       isVisible: true,
-                      labelPosition: ChartDataLabelPosition.outside, // 외부로 변경
+                      labelPosition: ChartDataLabelPosition.outside,
+                      // 겹침 숨김/이동 대신 모두 표시 후 커넥터로 분산
+                      labelIntersectAction: LabelIntersectAction.none,
+                      overflowMode: OverflowMode.none,
                       textStyle: const TextStyle(
                         fontFamily: 'Pretendard',
-                        fontSize: 10,
-                        fontWeight: FontWeight.w500,
+                        fontSize: 9,
+                        fontWeight: FontWeight.w600,
                         color: Colors.black87,
                       ),
-                      labelIntersectAction: LabelIntersectAction.shift,
+                      connectorLineSettings: const ConnectorLineSettings(
+                        type: ConnectorType.curve,
+                        length: '24%', // 좀 더 긴 커넥터로 라벨 분산
+                        width: 1.2,
+                        color: Colors.grey,
+                      ),
+                      // 커스텀 빌더로 긴 한글 카테고리 줄바꿈 처리
                       builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
-                        final chartData = data as ChartData;
+                        final ChartData d = data as ChartData;
+                        final display = _breakCategoryLabel(d.category);
                         return Container(
-                          padding: const EdgeInsets.all(4),
+                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                           decoration: BoxDecoration(
                             color: Colors.white,
                             borderRadius: BorderRadius.circular(4),
+                            border: Border.all(color: Colors.grey.shade300),
                             boxShadow: [
                               BoxShadow(
-                                color: Colors.black.withValues(alpha: 0.1),
+                                color: Colors.black.withValues(alpha: 0.08),
                                 blurRadius: 2,
+                                offset: const Offset(0, 1),
                               ),
                             ],
                           ),
                           child: Text(
-                            chartData.category,
+                            display,
+                            textAlign: TextAlign.center,
+                            softWrap: true,
+                            maxLines: 2,
                             style: const TextStyle(
                               fontFamily: 'Pretendard',
-                              fontSize: 10,
+                              fontSize: 9,
                               fontWeight: FontWeight.w600,
                               color: Colors.black87,
+                              height: 1.1,
                             ),
                           ),
                         );
@@ -944,7 +877,7 @@ class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
             )
           else
             const SizedBox(
-              height: 250, // 200 → 250으로 크기 증가
+              height: 380, // 240 → 380으로 맞춤
               child: Center(
                 child: Text(
                   '데이터가 없습니다',
