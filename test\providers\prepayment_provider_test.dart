import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:sqflite/sqflite.dart';

import 'package:parabara/models/prepayment.dart';
import 'package:parabara/models/prepayment_sort_order.dart';
import 'package:parabara/providers/prepayment_provider.dart';
import 'package:parabara/providers/prepayment_state.dart';
import 'package:parabara/repositories/prepayment_repository.dart';
import 'package:parabara/services/database_service.dart';
import '../test_helper.dart';

// Prepayment matcher
class PrepaymentMatcher extends Matcher {
  @override
  bool matches(item, Map matchState) => item is Prepayment;

  @override
  Description describe(Description description) =>
      description.add('is a Prepayment');
}

class MockPrepaymentRepository implements PrepaymentRepository {
  final List<Prepayment> _prepayments = [];
  int _nextId = 1;
  bool _shouldFail = false;

  void setShouldFail(bool shouldFail) {
    _shouldFail = shouldFail;
  }

  void addTestPrepayments(List<Prepayment> prepayments) {
    _prepayments.clear();
    for (final prepayment in prepayments) {
      final newPrepayment = prepayment.copyWith(id: _nextId++);
      _prepayments.add(newPrepayment);
    }
  }

  @override
  Future<List<Prepayment>> getAllPrepayments() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return List.from(_prepayments);
  }

  @override
  Future<List<Prepayment>> getPrepaymentsByEventId(int eventId) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _prepayments.where((p) => p.eventId == eventId).toList();
  }

  @override
  Future<int> insertPrepayment(Prepayment prepayment) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final int idToUse = prepayment.id;
    final newPrepayment = prepayment.copyWith(id: idToUse);
    // 이미 같은 id가 있으면 덮어쓰기(테스트 일관성)
    _prepayments.removeWhere((p) => p.id == idToUse);
    _prepayments.add(newPrepayment);
    // nextId 갱신
    if (idToUse >= _nextId) _nextId = idToUse + 1;
    return idToUse;
  }

  @override
  Future<int> updatePrepayment(Prepayment prepayment) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final index = _prepayments.indexWhere((p) => p.id == prepayment.id);
    if (index != -1) {
      _prepayments[index] = prepayment;
      return 1;
    }
    return 0;
  }

  @override
  Future<int> deletePrepayment(int prepaymentId) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final initialLength = _prepayments.length;
    _prepayments.removeWhere((p) => p.id == prepaymentId);
    return initialLength > _prepayments.length ? 1 : 0;
  }

  @override
  Future<Prepayment?> getPrepaymentById(int id) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    try {
      return _prepayments.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Prepayment>> getPrepaymentsSorted(PrepaymentSortOrder sortOrder, {int? eventId}) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    var result = List<Prepayment>.from(_prepayments);
    if (eventId != null) {
      result = result.where((p) => p.eventId == eventId).toList();
    }
    return result;
  }

  @override
  Future<List<Prepayment>> searchPrepayments(String searchQuery) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _prepayments.where((p) => 
      p.buyerName.toLowerCase().contains(searchQuery.toLowerCase()) ||
      p.buyerContact.toLowerCase().contains(searchQuery.toLowerCase()) ||
      p.productNameList.toLowerCase().contains(searchQuery.toLowerCase())
    ).toList();
  }

  @override
  Future<List<Prepayment>> getPrepaymentsByRegistrationDayOfWeek(int dayOfWeek) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _prepayments.where((p) => p.registrationActualDayOfWeek == dayOfWeek).toList();
  }

  @override
  Future<List<Prepayment>> getPrepaymentsByReceiveStatus(bool isReceived) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _prepayments.where((p) => p.isReceived == isReceived).toList();
  }

  @override
  Future<List<Prepayment>> getPrepaymentsByDateRange(DateTime startDate, DateTime endDate) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _prepayments.where((p) => 
      p.registrationTimestamp >= startDate.millisecondsSinceEpoch &&
      p.registrationTimestamp <= endDate.millisecondsSinceEpoch
    ).toList();
  }

  @override
  Future<int> deleteAllPrepayments() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final count = _prepayments.length;
    _prepayments.clear();
    return count;
  }

  @override
  Future<int> updateReceiveStatus(int prepaymentId, bool isReceived) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final index = _prepayments.indexWhere((p) => p.id == prepaymentId);
    if (index != -1) {
      _prepayments[index] = _prepayments[index].copyWith(isReceived: isReceived);
      return 1;
    }
    return 0;
  }

  @override
  Future<int> getTotalPrepaymentAmount() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    int total = 0;
    for (final prepayment in _prepayments) {
      total += prepayment.amount;
    }
    return total;
  }

  @override
  Future<int> getUnreceivedCount() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _prepayments.where((p) => !p.isReceived).length;
  }
}

class MockDatabaseService extends Mock implements DatabaseService {
  late Database _db;
  bool _isOpen = true;

  @override
  Future<Database> get database async => _db;

  void setDatabase(Database db) {
    _db = db;
  }

  bool get isOpen => _isOpen;
  void setIsOpen(bool value) => _isOpen = value;
}

void main() {
  late ProviderContainer container;
  late MockPrepaymentRepository mockRepository;

  final testPrepaymentNotifierProvider = StateNotifierProvider<PrepaymentNotifier, PrepaymentState>(
    (ref) => PrepaymentNotifier(ref, autoInit: false),
  );

  setUp(() {
    mockRepository = MockPrepaymentRepository();
    container = ProviderContainer(
      overrides: [
        prepaymentRepositoryProvider.overrideWithValue(mockRepository),
      ],
    );
    mockRepository.setShouldFail(false);
  });

  tearDown(() {
    container.dispose();
  });

  test('초기 상태 확인', () async {
    final state = container.read(testPrepaymentNotifierProvider);
    expect(state.prepayments, isEmpty);
    expect(state.isLoading, isFalse);
    expect(state.errorMessage, isNull);
  });

  test('선불 내역 목록 로드', () async {
    final prepayments = [
      Prepayment(
        id: 1,
        buyerName: '고객1',
        amount: 10000,
        isReceived: false,
        buyerContact: '010-1111-1111',
        pickupDays: ['월', '화'],
        productNameList: '상품A,상품B',
        registrationDate: DateTime.now(),
        registrationActualDayOfWeek: 1,
        bankName: '국민',
        email: '<EMAIL>',
        registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
      ),
      Prepayment(
        id: 2,
        buyerName: '고객2',
        amount: 20000,
        isReceived: true,
        buyerContact: '010-2222-2222',
        pickupDays: ['수', '목'],
        productNameList: '상품C',
        registrationDate: DateTime.now(),
        registrationActualDayOfWeek: 3,
        bankName: '신한',
        email: '<EMAIL>',
        registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
      ),
    ];

    // mock 설정
    for (final prepayment in prepayments) {
      await mockRepository.insertPrepayment(prepayment);
    }
    
    final notifier = container.read(testPrepaymentNotifierProvider.notifier);
    await notifier.loadPrepayments();
    
    // 상태 안정화 대기
    await CustomTestAsyncUtils.waitForProviderStabilization();
    
    final state = container.read(testPrepaymentNotifierProvider);
    expect(state.prepayments.length, equals(2));
    expect(state.prepayments.first.buyerName, equals('고객1'));
    expect(state.errorMessage, isNull);
  });

  test('선결제 추가', () async {
    final testPrepayment = Prepayment(
      id: 1,
      buyerName: '테스트 고객',
      buyerContact: '010-1234-5678',
      amount: 50000,
      pickupDays: ['토요일'],
      productNameList: '테스트 상품',
      registrationDate: DateTime.now(),
      isReceived: false,
      registrationActualDayOfWeek: 6,
      bankName: '테스트 은행',
      email: '<EMAIL>',
      registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
    );

    final notifier = container.read(testPrepaymentNotifierProvider.notifier);
    await notifier.addPrepayment(testPrepayment);
    
    // 데이터 로드하여 상태 업데이트
    await notifier.loadPrepayments();
    
    // 상태 안정화 대기
    await CustomTestAsyncUtils.waitForProviderStabilization();

    final state = container.read(testPrepaymentNotifierProvider);
    expect(state.prepayments.length, equals(1));
    expect(state.prepayments.first.buyerName, equals('테스트 고객'));
    expect(state.prepayments.first.amount, equals(50000));
    expect(state.prepayments.first.buyerContact, equals('010-1234-5678'));
    expect(state.errorMessage, isNull);
  });

  test('선결제 업데이트', () async {
    final updatedPrepayment = Prepayment(
      id: 1,
      buyerName: '수정된 고객',
      buyerContact: '010-1234-5678',
      amount: 60000,
      pickupDays: ['일요일'],
      productNameList: '수정된 상품',
      registrationDate: DateTime.now(),
      isReceived: true,
      registrationActualDayOfWeek: 7,
      bankName: '수정된 은행',
      email: '<EMAIL>',
      registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
    );

    // 먼저 원본 데이터 추가
    await mockRepository.insertPrepayment(updatedPrepayment.copyWith(
      buyerName: '원본 고객',
      isReceived: false,
    ));
    final notifier = container.read(testPrepaymentNotifierProvider.notifier);
    await notifier.loadPrepayments(); // 상태 동기화 추가
    await notifier.updatePrepayment(updatedPrepayment);
    // 상태 안정화 대기
    await CustomTestAsyncUtils.waitForProviderStabilization();
    final state = container.read(testPrepaymentNotifierProvider);
    expect(state.prepayments.first.buyerName, equals('수정된 고객'));
    expect(state.prepayments.first.isReceived, isTrue);
    expect(state.errorMessage, isNull);
  });

  test('선결제 삭제', () async {
    // 먼저 삭제할 데이터 추가
    final prepayment = Prepayment(
      id: 1,
      buyerName: '삭제될 고객',
      buyerContact: '010-1234-5678',
      amount: 50000,
      pickupDays: ['토요일'],
      productNameList: '테스트 상품',
      registrationDate: DateTime.now(),
      isReceived: false,
      registrationActualDayOfWeek: 6,
      bankName: '테스트 은행',
      email: '<EMAIL>',
      registrationTimestamp: DateTime.now().millisecondsSinceEpoch,
    );
    await mockRepository.insertPrepayment(prepayment);
    final notifier = container.read(testPrepaymentNotifierProvider.notifier);
    await notifier.loadPrepayments(); // 상태 동기화 추가
    await notifier.deletePrepayment(1);
    // 상태 안정화 대기
    await CustomTestAsyncUtils.waitForProviderStabilization();
    final state = container.read(testPrepaymentNotifierProvider);
    expect(state.prepayments, isEmpty);
    expect(state.errorMessage, isNull);
  });

  test('에러 처리 테스트 선불결제 로드 실패', () async {
    final mockRepo = MockPrepaymentRepository();
    mockRepo.setShouldFail(true);
    final container = ProviderContainer(
      overrides: [
        prepaymentRepositoryProvider.overrideWithValue(mockRepo),
      ],
    );
    addTearDown(container.dispose);

    await container.read(testPrepaymentNotifierProvider.notifier).loadPrepayments();
    await container.pump();
    await Future.delayed(Duration.zero);

    final state = container.read(testPrepaymentNotifierProvider);
    expect(state.errorMessage, isNotNull);
  });
}

/// 테스트용 데이터베이스 예외
class DatabaseException implements Exception {
  final String message;
  DatabaseException(this.message);
  
  @override
  String toString() => message;
}

/// 테스트용 Mock Database
class MockDatabase extends Mock implements Database {}
