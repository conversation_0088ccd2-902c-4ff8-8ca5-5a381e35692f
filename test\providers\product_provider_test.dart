import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:sqflite/sqflite.dart';

import 'package:parabara/models/product.dart';
import 'package:parabara/models/product_sort_option.dart';
import 'package:parabara/providers/product_provider.dart';
import 'package:parabara/repositories/product_repository.dart';
import 'package:parabara/services/database_service.dart';
import 'package:parabara/utils/batch_processor.dart';

// 완전한 Mock Database 구현
class MockDatabase extends Mock implements Database {
  @override
  bool get isOpen => true;
}

// 완전한 Mock DatabaseService 구현
class MockDatabaseService extends Mock implements DatabaseService {
  late Database _db;
  bool _isInitialized = true;

  MockDatabaseService() {
    _db = MockDatabase();
  }

  @override
  Future<Database> get database async => _db;

  bool get isInitialized => _isInitialized;

  void setDatabase(Database db) => _db = db;
  void setInitialized(bool value) => _isInitialized = value;
}

// 완전한 Mock ProductRepository 구현
class MockProductRepository implements ProductRepository {
  final List<Product> _products = [];
  int _nextId = 1;
  bool _shouldFail = false;

  void setShouldFail(bool shouldFail) {
    _shouldFail = shouldFail;
  }

  void addTestProducts(List<Product> products) {
    _products.clear();
    for (final product in products) {
      final newProduct = product.copyWith(id: _nextId++);
      _products.add(newProduct);
    }
  }

  @override
  Future<List<Product>> getAllProducts() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return List.from(_products);
  }

  @override
  Future<List<Product>> getProductsByEventId(int eventId, {int limit = 50, int offset = 0}) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final filtered = _products.where((p) => p.eventId == eventId).toList();
    final startIndex = offset;
    final endIndex = (startIndex + limit).clamp(0, filtered.length);
    return filtered.sublist(startIndex, endIndex);
  }

  @override
  Future<int> getProductCountByEventId(int eventId) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _products.where((p) => p.eventId == eventId).length;
  }

  @override
  Future<List<Product>> getProductsSorted(ProductSortOption sortOption) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return List.from(_products);
  }

  @override
  Future<Product?> getProductById(int id) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    try {
      return _products.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<String>> getAllSellerNames() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _products.map((p) => p.sellerName ?? '').where((name) => name.isNotEmpty).toSet().toList();
  }

  @override
  Future<List<Product>> getProductsBySeller(String sellerName) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _products.where((p) => (p.sellerName ?? '').toLowerCase() == sellerName.toLowerCase()).toList();
  }

  @override
  Future<int> insertProduct(Product product) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final newProduct = product.copyWith(id: _nextId++);
    _products.add(newProduct);
    return newProduct.id ?? 1;
  }

  @override
  Future<int> updateProduct(Product product, {bool updateRelatedSalesLogs = true}) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index] = product;
      return 1;
    }
    return 0;
  }

  @override
  Future<void> updateMultipleProducts(List<Product> products) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    for (final product in products) {
      await updateProduct(product);
    }
  }

  @override
  Future<int> deleteProduct(Product product) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final initialLength = _products.length;
    _products.removeWhere((p) => p.id == product.id);
    return initialLength > _products.length ? 1 : 0;
  }

  @override
  Future<int> updateStock(int productId, int changeInQuantity) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final index = _products.indexWhere((p) => p.id == productId);
    if (index != -1) {
      final newQuantity = _products[index].quantity + changeInQuantity;
      if (newQuantity < 0) return 0;
      _products[index] = _products[index].copyWith(quantity: newQuantity);
      return 1;
    }
    return 0;
  }

  @override
  Future<int> setStock(int productId, int newQuantity) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final index = _products.indexWhere((p) => p.id == productId);
    if (index != -1 && newQuantity >= 0) {
      _products[index] = _products[index].copyWith(quantity: newQuantity);
      return 1;
    }
    return 0;
  }

  @override
  Future<void> toggleProductActive(int productId) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    // 간단 Mock: 아무 동작 안함
  }

  @override
  Future<void> updateProductImage(int productId, String imagePath) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    // 간단 Mock: 아무 동작 안함
  }



  @override
  Future<int> deleteAllProducts() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final count = _products.length;
    _products.clear();
    return count;
  }

  @override
  Future<List<Product>> getActiveProducts() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _products.where((p) => p.isActive == true).toList();
  }

  @override
  Future<List<Product>> getProductsInStock() async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _products.where((p) => p.quantity > 0).toList();
  }

  @override
  Future<List<Product>> searchProducts(String searchQuery) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    return _products.where((p) => (p.name.toLowerCase().contains(searchQuery.toLowerCase()) || (p.sellerName ?? '').toLowerCase().contains(searchQuery.toLowerCase()))).toList();
  }

  @override
  Future<int> updateProductStock(int productId, int newQuantity) async {
    return setStock(productId, newQuantity);
  }

  @override
  Future<int> increaseStock(int productId, int quantity) async {
    return updateStock(productId, quantity);
  }

  @override
  Future<int> decreaseStock(int productId, int quantity) async {
    if (_shouldFail) return Future.error(Exception('Test error'));
    final index = _products.indexWhere((p) => p.id == productId);
    if (index != -1) {
      final newQuantity = _products[index].quantity - quantity;
      if (newQuantity < 0) return 0;
      _products[index] = _products[index].copyWith(quantity: newQuantity);
      return 1;
    }
    return 0;
  }

  @override
  Future<BatchResult<Product>> batchInsertProducts(List<Product> products) async {
    return BatchResult<Product>(
      succeeded: products,
      failed: [],
      processingTime: const Duration(milliseconds: 100),
      averageProcessingTimePerItem: 10.0,
      totalRetries: 0,
    );
  }

  @override
  Future<BatchResult<Product>> batchUpdateProducts(List<Product> products) async {
    return BatchResult<Product>(
      succeeded: products,
      failed: [],
      processingTime: const Duration(milliseconds: 100),
      averageProcessingTimePerItem: 10.0,
      totalRetries: 0,
    );
  }

  @override
  Future<BatchResult<int>> batchDeleteProducts(List<int> productIds) async {
    return BatchResult<int>(
      succeeded: productIds,
      failed: [],
      processingTime: const Duration(milliseconds: 100),
      averageProcessingTimePerItem: 10.0,
      totalRetries: 0,
    );
  }

  // 기존 BatchJob 관련 메서드들 삭제
}

void main() {
  late ProviderContainer container;
  late MockProductRepository mockRepository;
  late MockDatabaseService mockDatabaseService;
  late StateNotifierProvider<ProductNotifier, ProductState> testProductNotifierProvider;
  late List<Product> testProducts;

  setUp(() {
    mockRepository = MockProductRepository();
    mockDatabaseService = MockDatabaseService();
    
    testProducts = [
      Product(
        id: 1,
        name: '상품1',
        price: 10000,
        quantity: 10,
        sellerName: '판매자1',
      ),
      Product(
        id: 2,
        name: '상품2',
        price: 20000,
        quantity: 5,
        sellerName: '판매자2',
      ),
    ];
    
    testProductNotifierProvider = StateNotifierProvider<ProductNotifier, ProductState>(
      (ref) => ProductNotifier(mockRepository, ref, autoInit: false),
    );
    
    container = ProviderContainer(
      overrides: [
        databaseServiceProvider.overrideWithValue(mockDatabaseService),
        productRepositoryProvider.overrideWithValue(mockRepository),
      ],
    );
    mockRepository.setShouldFail(false);
  });

  tearDown(() {
    container.dispose();
  });

  group('기본 상태 및 초기화 테스트', () {
    test('초기 상태 확인', () {
      final state = container.read(testProductNotifierProvider);
      expect(state.products, isEmpty);
      expect(state.isLoading, isFalse);
      expect(state.errorMessage, isNull);
      expect(state.currentSortOption, equals(ProductSortOption.recentlyAdded)); // 실제 기본값으로 수정
      expect(state.selectedSellerFilter, isEmpty);
    });

    test('상품 목록 로드', () async {
      mockRepository.addTestProducts(testProducts);
      
      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.loadProducts();
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.products.length, equals(2));
      expect(state.products.first.name, equals('상품1'));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });
  });

  group('상품 CRUD 테스트', () {
    test('상품 추가', () async {
      final testProduct = Product(
        id: 3,
        name: '테스트 상품',
        price: 15000,
        quantity: 5,
        sellerName: '테스트 판매자',
      );

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.addProduct(testProduct);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.products.length, equals(1));
      expect(state.products.first.name, equals('테스트 상품'));
      expect(state.products.first.price, equals(15000));
      expect(state.products.first.quantity, equals(5));
      expect(state.products.first.sellerName, equals('테스트 판매자'));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });

    test('상품 업데이트', () async {
      mockRepository.addTestProducts(testProducts);
      
      final updatedProduct = testProducts.first.copyWith(
        name: '수정된 상품',
        price: 15000,
        quantity: 10,
      );

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.updateProduct(updatedProduct);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.products.first.name, equals('수정된 상품'));
      expect(state.products.first.price, equals(15000));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });

    test('상품 삭제', () async {
      mockRepository.addTestProducts(testProducts);

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.deleteProduct(testProducts.first);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.products.length, equals(1));
      expect(state.products, isNot(contains(testProducts.first)));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });
  });

  group('재고 관리 테스트', () {
    test('재고 업데이트', () async {
      mockRepository.addTestProducts(testProducts);

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.updateProductStock(1, 5);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.products.first.quantity, equals(5));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });

    test('재고 증가', () async {
      mockRepository.addTestProducts(testProducts);

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.increaseStock(1, 5);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.products.first.quantity, equals(15));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });
  });

  group('에러 처리 테스트', () {
    test('상품 로드 실패', () async {
      mockRepository.setShouldFail(true);

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.loadProducts();
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorCode, equals('PRD_LOAD_ERROR'));
      expect(state.isLoading, isFalse);
    });

    test('상품 추가 실패', () async {
      mockRepository.setShouldFail(true);
      
      final testProduct = Product(
        id: 3,
        name: '테스트 상품',
        price: 15000,
        quantity: 5,
        sellerName: '테스트 판매자',
      );

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.addProduct(testProduct);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorCode, equals('PRD_ADD_ERROR'));
      expect(state.isLoading, isFalse);
    });

    test('상품 업데이트 실패', () async {
      mockRepository.addTestProducts(testProducts);
      mockRepository.setShouldFail(true);
      
      final updatedProduct = testProducts.first.copyWith(
        name: '수정된 상품',
        price: 15000,
      );

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.updateProduct(updatedProduct);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorCode, equals('PRD_UPDATE_ERROR'));
      expect(state.isLoading, isFalse);
    });

    test('상품 삭제 실패', () async {
      mockRepository.addTestProducts(testProducts);
      mockRepository.setShouldFail(true);

      final notifier = container.read(testProductNotifierProvider.notifier);
      await notifier.deleteProduct(testProducts.first);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(testProductNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorCode, equals('PRD_DELETE_ERROR'));
      expect(state.isLoading, isFalse);
    });

    test('에러 복구', () async {
      final notifier = container.read(testProductNotifierProvider.notifier);
      mockRepository.setShouldFail(true);
      try {
        await notifier.loadProducts();
      } catch (_) {}
      await Future.delayed(const Duration(milliseconds: 200));
      final state = container.read(testProductNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.isLoading, isFalse);
    });
  });
}
