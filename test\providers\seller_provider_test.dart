import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:sqflite/sqflite.dart';

import 'package:parabara/models/seller.dart';
import 'package:parabara/providers/seller_provider.dart';
import 'package:parabara/repositories/seller_repository.dart';
import 'package:parabara/services/database_service.dart';
import 'package:parabara/utils/batch_processor.dart';
import '../test_helper.dart';

// 강제 상태 대기 함수
Future<void> waitForErrorMessage(ProviderContainer container, provider) async {
  for (int i = 0; i < 10; i++) {
    final state = container.read(provider);
    if (state.errorMessage != null) return;
    await Future.delayed(Duration(milliseconds: 50));
  }
}

// 완전한 Mock Database 구현
class MockDatabase extends Mock implements Database {
  @override
  bool get isOpen => true;
}

// 완전한 Mock DatabaseService 구현
class MockDatabaseService extends Mock implements DatabaseService {
  late Database _db;
  bool _isInitialized = true;

  MockDatabaseService() {
    _db = MockDatabase();
  }

  @override
  Future<Database> get database async => _db;

  bool get isInitialized => _isInitialized;

  void setDatabase(Database db) => _db = db;
  void setInitialized(bool value) => _isInitialized = value;
}

// 완전한 Mock SellerRepository 구현
class MockSellerRepository implements SellerRepository {
  final List<Seller> _sellers = [];
  int _nextId = 1;
  bool _shouldFail = false;

  void setShouldFail(bool shouldFail) {
    _shouldFail = shouldFail;
  }

  void addTestSellers(List<Seller> sellers) {
    _sellers.clear();
    for (final seller in sellers) {
      final newSeller = seller.copyWith(id: _nextId++);
      _sellers.add(newSeller);
    }
  }

  @override
  Future<List<Seller>> getAllSellers() async {
    if (_shouldFail) throw Exception('Test error');
    return List.from(_sellers);
  }

  @override
  Future<List<Seller>> getSellersByEventId(int eventId) async {
    if (_shouldFail) throw Exception('Test error');
    return _sellers.where((s) => s.eventId == eventId).toList();
  }

  @override
  Future<int> insertSeller(Seller seller) async {
    if (_shouldFail) throw Exception('Test error');
    final newSeller = seller.copyWith(id: _nextId++);
    _sellers.add(newSeller);
    return newSeller.id ?? 1;
  }

  @override
  Future<int> updateSeller(Seller seller) async {
    if (_shouldFail) throw Exception('Test error');
    final index = _sellers.indexWhere((s) => s.id == seller.id);
    if (index != -1) {
      _sellers[index] = seller;
      return 1;
    }
    return 0;
  }

  @override
  Future<int> deleteSeller(int sellerId) async {
    if (_shouldFail) throw Exception('Test error');
    final initialLength = _sellers.length;
    _sellers.removeWhere((s) => s.id == sellerId);
    return initialLength > _sellers.length ? 1 : 0;
  }

  @override
  Future<Seller?> getSellerById(int id) async {
    if (_shouldFail) throw Exception('Test error');
    try {
      return _sellers.firstWhere((s) => s.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<Seller?> getSellerByName(String name) async {
    if (_shouldFail) throw Exception('Test error');
    try {
      return _sellers.firstWhere((s) => s.name == name);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Seller>> searchSellersByName(String searchQuery) async {
    if (_shouldFail) throw Exception('Test error');
    return _sellers.where((s) => s.name.toLowerCase().contains(searchQuery.toLowerCase())).toList();
  }

  @override
  Future<int> deleteAllSellers() async {
    if (_shouldFail) throw Exception('Test error');
    final count = _sellers.length;
    _sellers.clear();
    return count;
  }

  @override
  Future<BatchResult<Seller>> batchInsertSellers(List<Seller> sellers) async {
    return BatchResult<Seller>(
      succeeded: sellers,
      failed: [],
      processingTime: const Duration(milliseconds: 100),
      averageProcessingTimePerItem: 10.0,
      totalRetries: 0,
    );
  }

  @override
  Future<BatchResult<Seller>> batchUpdateSellers(List<Seller> sellers) async {
    return BatchResult<Seller>(
      succeeded: sellers,
      failed: [],
      processingTime: const Duration(milliseconds: 100),
      averageProcessingTimePerItem: 10.0,
      totalRetries: 0,
    );
  }

  @override
  Future<Seller?> getDefaultSeller() async {
    if (_shouldFail) throw Exception('Test error');
    try {
      return _sellers.firstWhere((s) => s.isDefault);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<int> setDefaultSeller(int sellerId) async {
    if (_shouldFail) throw Exception('Test error');
    
    // 모든 판매자의 대표 설정 해제
    for (int i = 0; i < _sellers.length; i++) {
      _sellers[i] = _sellers[i].copyWith(isDefault: false);
    }
    
    // 지정된 판매자를 대표로 설정
    final index = _sellers.indexWhere((s) => s.id == sellerId);
    if (index != -1) {
      _sellers[index] = _sellers[index].copyWith(isDefault: true);
      return 1;
    }
    return 0;
  }

  @override
  Future<int> unsetDefaultSeller(int sellerId) async {
    if (_shouldFail) throw Exception('Test error');
    
    final index = _sellers.indexWhere((s) => s.id == sellerId);
    if (index != -1) {
      _sellers[index] = _sellers[index].copyWith(isDefault: false);
      return 1;
    }
    return 0;
  }

  @override
  Future<bool> isDefaultSeller(int sellerId) async {
    if (_shouldFail) throw Exception('Test error');

    try {
      final seller = _sellers.firstWhere((s) => s.id == sellerId);
      return seller.isDefault;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Seller?> getDefaultSellerByEventId(int eventId) async {
    if (_shouldFail) throw Exception('Test error');
    try {
      return _sellers.firstWhere((s) => s.isDefault && s.eventId == eventId);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<int> setDefaultSellerByEventId(int sellerId, int eventId) async {
    if (_shouldFail) throw Exception('Test error');
    
    // 해당 행사의 모든 판매자의 대표 설정 해제
    for (int i = 0; i < _sellers.length; i++) {
      if (_sellers[i].eventId == eventId) {
        _sellers[i] = _sellers[i].copyWith(isDefault: false);
      }
    }
    
    // 지정된 판매자를 대표로 설정
    final index = _sellers.indexWhere((s) => s.id == sellerId && s.eventId == eventId);
    if (index != -1) {
      _sellers[index] = _sellers[index].copyWith(isDefault: true);
      return 1;
    }
    return 0;
  }

  @override
  void dispose() {
    // Mock에서는 특별한 정리 작업 없음
  }
}

void main() {
  late ProviderContainer container;
  late MockSellerRepository mockRepository;
  late MockDatabaseService mockDatabaseService;
  late List<Seller> testSellers;

  setUp(() {
    mockRepository = MockSellerRepository();
    mockDatabaseService = MockDatabaseService();
    
    testSellers = [
      Seller(id: 1, name: '판매자1'),
      Seller(id: 2, name: '판매자2'),
    ];
    
    container = ProviderContainer(
      overrides: [
        databaseServiceProvider.overrideWithValue(mockDatabaseService),
        sellerRepositoryProvider.overrideWithValue(mockRepository),
        sellerNotifierProvider.overrideWith((ref) => SellerNotifier(ref, autoInit: false)),
      ],
    );
    mockRepository.setShouldFail(false);
  });

  tearDown(() {
    container.dispose();
  });

  group('기본 상태 및 초기화 테스트', () {
    test('초기 상태 확인', () {
      final state = container.read(sellerNotifierProvider);
      expect(state.sellers, isEmpty);
      expect(state.isLoading, isFalse);
      expect(state.errorMessage, isNull);
    });

    test('판매자 목록 로드', () async {
      mockRepository.addTestSellers(testSellers);
      
      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.loadSellers();
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.sellers.length, equals(2));
      expect(state.sellers.first.name, equals('판매자1'));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });
  });

  group('판매자 CRUD 테스트', () {
    test('판매자 추가', () async {
      final testSeller = Seller(
        id: 3,
        name: '테스트 판매자',
      );

      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.addSeller(testSeller);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.sellers.length, equals(1));
      expect(state.sellers.first.name, equals('테스트 판매자'));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });

    test('판매자 업데이트', () async {
      mockRepository.addTestSellers(testSellers);
      
      final updatedSeller = testSellers.first.copyWith(
        name: '수정된 판매자',
      );

      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.updateSeller(updatedSeller);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.sellers.first.name, equals('수정된 판매자'));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });

    test('판매자 삭제', () async {
      mockRepository.addTestSellers(testSellers);

      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.deleteSeller(testSellers.first.id!);
      
      // 상태 변경 대기
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.sellers.length, equals(1));
      expect(state.sellers, isNot(contains(testSellers.first)));
      expect(state.errorMessage, isNull);
      expect(state.isLoading, isFalse);
    });
  });

  group('에러 처리 테스트', () {
    test('판매자 로드 실패', () async {
      final mockRepo = MockSellerRepository();
      mockRepo.setShouldFail(true);
      final container = ProviderContainer(
        overrides: [
          sellerRepositoryProvider.overrideWithValue(mockRepo),
          sellerNotifierProvider.overrideWith((ref) => SellerNotifier(ref, autoInit: false)),
        ],
      );
      addTearDown(container.dispose);

      await container.read(sellerNotifierProvider.notifier).loadSellers();
      await ProviderErrorTestHelper.waitForErrorState(container, sellerNotifierProvider);

      final state = container.read(sellerNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorMessage!.isNotEmpty, isTrue);
    });

    test('판매자 추가 실패', () async {
      final testSeller = Seller(id: 3, name: '테스트 판매자');
      final mockRepo = MockSellerRepository();
      mockRepo.setShouldFail(true);
      final container = ProviderContainer(
        overrides: [
          sellerRepositoryProvider.overrideWithValue(mockRepo),
          sellerNotifierProvider.overrideWith((ref) => SellerNotifier(ref, autoInit: false)),
        ],
      );
      addTearDown(container.dispose);

      await container.read(sellerNotifierProvider.notifier).addSeller(testSeller);
      await ProviderErrorTestHelper.waitForErrorState(container, sellerNotifierProvider);

      final state = container.read(sellerNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorMessage!.isNotEmpty, isTrue);
    });

    test('판매자 업데이트 실패', () async {
      mockRepository.addTestSellers(testSellers);
      final updatedSeller = testSellers.first.copyWith(name: '수정된 판매자');
      final mockRepo = MockSellerRepository();
      mockRepo.setShouldFail(true);
      final container = ProviderContainer(
        overrides: [
          sellerRepositoryProvider.overrideWithValue(mockRepo),
          sellerNotifierProvider.overrideWith((ref) => SellerNotifier(ref, autoInit: false)),
        ],
      );
      addTearDown(container.dispose);

      await container.read(sellerNotifierProvider.notifier).updateSeller(updatedSeller);
      await ProviderErrorTestHelper.waitForErrorState(container, sellerNotifierProvider);

      final state = container.read(sellerNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorMessage!.isNotEmpty, isTrue);
    });

    test('판매자 삭제 실패', () async {
      mockRepository.addTestSellers(testSellers);
      final mockRepo = MockSellerRepository();
      mockRepo.setShouldFail(true);
      final container = ProviderContainer(
        overrides: [
          sellerRepositoryProvider.overrideWithValue(mockRepo),
          sellerNotifierProvider.overrideWith((ref) => SellerNotifier(ref, autoInit: false)),
        ],
      );
      addTearDown(container.dispose);

      await container.read(sellerNotifierProvider.notifier).deleteSeller(1);
      await ProviderErrorTestHelper.waitForErrorState(container, sellerNotifierProvider);

      final state = container.read(sellerNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorMessage!.isNotEmpty, isTrue);
    });

    test('에러 복구', () async {
      final mockRepo = MockSellerRepository();
      mockRepo.setShouldFail(true);
      final container = ProviderContainer(
        overrides: [
          sellerRepositoryProvider.overrideWithValue(mockRepo),
          sellerNotifierProvider.overrideWith((ref) => SellerNotifier(ref, autoInit: false)),
        ],
      );
      addTearDown(container.dispose);

      await container.read(sellerNotifierProvider.notifier).loadSellers();
      await ProviderErrorTestHelper.waitForErrorState(container, sellerNotifierProvider);

      final state = container.read(sellerNotifierProvider);
      expect(state.errorMessage, isNotNull);
      expect(state.errorMessage!.isNotEmpty, isTrue);
    });
  });

  group('대표 판매자 테스트', () {
    test('대표 판매자 로드', () async {
      final defaultSeller = Seller(id: 1, name: '대표 판매자', isDefault: true);
      mockRepository.addTestSellers([defaultSeller]);
      
      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.loadSellers();
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.defaultSeller, isNotNull);
      expect(state.defaultSeller!.name, equals('대표 판매자'));
      expect(state.defaultSeller!.isDefault, isTrue);
    });

    test('대표 판매자 설정', () async {
      mockRepository.addTestSellers(testSellers);
      
      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.setDefaultSeller(testSellers.first.id!);
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.defaultSeller, isNotNull);
      expect(state.defaultSeller!.id, equals(testSellers.first.id));
      expect(state.defaultSeller!.isDefault, isTrue);
    });

    test('대표 판매자 해제', () async {
      final defaultSeller = Seller(id: 1, name: '대표 판매자', isDefault: true);
      mockRepository.addTestSellers([defaultSeller]);
      
      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.unsetDefaultSeller(defaultSeller.id!);
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      final state = container.read(sellerNotifierProvider);
      expect(state.defaultSeller, isNull);
    });

    test('대표 판매자 여부 확인', () async {
      final defaultSeller = Seller(id: 1, name: '대표 판매자', isDefault: true);
      mockRepository.addTestSellers([defaultSeller]);
      
      final notifier = container.read(sellerNotifierProvider.notifier);
      final isDefault = await notifier.isDefaultSeller(defaultSeller.id!);
      
      expect(isDefault, isTrue);
    });

    test('대표 판매자 Provider 테스트', () async {
      final defaultSeller = Seller(id: 1, name: '대표 판매자', isDefault: true);
      mockRepository.addTestSellers([defaultSeller]);
      
      final notifier = container.read(sellerNotifierProvider.notifier);
      await notifier.loadSellers();
      
      await Future.delayed(const Duration(milliseconds: 100));
      
      // defaultSellerProvider 테스트
      final defaultSellerFromProvider = container.read(defaultSellerProvider);
      expect(defaultSellerFromProvider, isNotNull);
      expect(defaultSellerFromProvider!.name, equals('대표 판매자'));
      
      // defaultSellerNameProvider 테스트
      final defaultSellerName = container.read(defaultSellerNameProvider);
      expect(defaultSellerName, equals('대표 판매자'));
    });
  });
}
