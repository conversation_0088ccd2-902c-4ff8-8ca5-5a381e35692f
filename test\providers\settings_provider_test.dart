import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

import 'package:parabara/providers/settings_provider.dart';
import 'package:parabara/repositories/settings_repository.dart';
import 'package:parabara/utils/provider_exception.dart';
import '../test_helper.dart';

class MockSettingsRepositoryForTest extends Mock implements SettingsRepository {
  final Map<String, dynamic> _storage = <String, dynamic>{};

  // 실제 Repository와 동일한 기본값 설정
  MockSettingsRepositoryForTest() {
    _storage.addAll({
      'event_day_of_week': 7,
      'enabled_days_of_week': ['1', '2', '3', '4', '5', '6', '7'],
      'collect_day_of_week_from_excel': false,
      'excel_day_of_week_column_index': -1,
      'link_prepayment_to_inventory': false,
      'inventory_columns': 3,
      'sale_columns': 3,
      'inventory_columns_portrait': 3,
      'inventory_columns_landscape': 5,
      'sale_columns_portrait': 3,
      'sale_columns_landscape': 5,
      'is_simple_view_mode': false,
    });
  }

  @override
  Future<int> getEventDayOfWeek() async => _storage['event_day_of_week'] as int? ?? 7;
  
  @override
  Future<bool> setEventDayOfWeek(int dayOfWeek) async {
    _storage['event_day_of_week'] = dayOfWeek;
    return true;
  }

  @override
  Future<bool?> getCollectDayOfWeekFromExcel() async => _storage['collect_day_of_week_from_excel'] as bool? ?? false;
  
  @override
  Future<bool> setCollectDayOfWeekFromExcel(bool value) async {
    _storage['collect_day_of_week_from_excel'] = value;
    return true;
  }

  @override
  Future<int?> getExcelDayOfWeekColumnIndex() async => _storage['excel_day_of_week_column_index'] as int? ?? -1;
  
  @override
  Future<bool> setExcelDayOfWeekColumnIndex(int value) async {
    _storage['excel_day_of_week_column_index'] = value;
    return true;
  }

  @override
  Future<bool?> getLinkPrepaymentToInventory() async => _storage['link_prepayment_to_inventory'] as bool? ?? false;
  
  @override
  Future<bool> setLinkPrepaymentToInventory(bool value) async {
    _storage['link_prepayment_to_inventory'] = value;
    return true;
  }

  // Generic get/set methods (실제 Repository와 동일)
  @override
  Future<int?> getInt(String key) async => _storage[key] as int?;
  
  @override
  Future<bool> setInt(String key, int value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<bool?> getBool(String key) async => _storage[key] as bool?;
  
  @override
  Future<bool> setBool(String key, bool value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<List<String>?> getStringList(String key) async {
    final value = _storage[key];
    if (value is List<String>) return value;
    if (value is List) return value.cast<String>();
    return null;
  }
  
  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _storage[key] = List<String>.from(value);
    return true;
  }

  @override
  Future<String?> getString(String key) async => _storage[key] as String?;
  
  @override
  Future<bool> setString(String key, String value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<double?> getDouble(String key) async {
    final v = _storage[key];
    if (v is double) return v;
    if (v is String) return double.tryParse(v);
    return null;
  }
  
  @override
  Future<bool> setDouble(String key, double value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<bool> remove(String key) async {
    _storage.remove(key);
    return true;
  }
  
  @override
  Future<bool> clearSettings() async {
    _storage.clear();
    return true;
  }
  
  @override
  Future<Set<String>> getKeys() async => _storage.keys.toSet();
}

// 상태 변화를 기다리는 유틸리티 함수
Future<void> waitForStateChange<T>(
  ProviderContainer container,
  StateNotifierProvider<SettingsNotifier, AsyncValue<SettingsState>> provider,
  T Function(SettingsState) getter,
  T expectedValue, {
  Duration timeout = const Duration(seconds: 10),
  bool allowError = false,
}) async {
  final startTime = DateTime.now();
  
  while (DateTime.now().difference(startTime) < timeout) {
    final state = container.read(provider);
    if (state.hasValue) {
      final currentValue = getter(state.value!);
      bool isEqual = false;
      
      // Set 타입인 경우 내용 비교
      if (currentValue is Set && expectedValue is Set) {
        isEqual = currentValue.length == expectedValue.length && 
                  currentValue.containsAll(expectedValue);
      } else {
        isEqual = currentValue == expectedValue;
      }
      
      if (isEqual) {
        return;
      }
    } else if (allowError && state.hasError) {
      return;
    }
    await Future.delayed(const Duration(milliseconds: 100));
  }
  
  throw TimeoutException('상태 변화 대기 시간 초과: $expectedValue', timeout);
}

// Provider 초기화 대기 함수
Future<void> waitForProviderInitialization(
  ProviderContainer container,
  StateNotifierProvider<SettingsNotifier, AsyncValue<SettingsState>> provider, {
  Duration timeout = const Duration(seconds: 10),
}) async {
  final startTime = DateTime.now();
  
  while (DateTime.now().difference(startTime) < timeout) {
    final state = container.read(provider);
    if (state.hasValue && !state.value!.isUpdating) {
      return;
    }
    await Future.delayed(const Duration(milliseconds: 100));
  }
  
  throw TimeoutException('Provider 초기화 대기 시간 초과', timeout);
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockSettingsRepositoryForTest mockRepository;
  late ProviderContainer container;

  setUp(() {
    mockRepository = MockSettingsRepositoryForTest();
    container = ProviderContainer(
      overrides: [
        settingsRepositoryProvider.overrideWithValue(mockRepository),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('SettingsNotifier 기본 동작 테스트', () {
    test('초기 상태', () async {
      // Provider 초기화 대기
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final settingsState = container.read(settingsNotifierProvider);
      
      expect(settingsState.hasValue, isTrue);
      expect(settingsState.value!.eventDayOfWeek, equals(7));
      expect(settingsState.value!.collectDayOfWeekFromExcel, isFalse);
      expect(settingsState.value!.excelDayOfWeekColumnIndex, equals(-1));
      expect(settingsState.value!.linkPrepaymentToInventory, isFalse);
      expect(settingsState.value!.inventoryColumns, equals(3));
      expect(settingsState.value!.saleColumns, equals(3));
      expect(settingsState.value!.inventoryColumnsPortrait, equals(3));
      expect(settingsState.value!.inventoryColumnsLandscape, equals(5));
      expect(settingsState.value!.saleColumnsPortrait, equals(3));
      expect(settingsState.value!.saleColumnsLandscape, equals(5));
      expect(settingsState.value!.isUpdating, isFalse);
    });

    test('이벤트 요일 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setEventDayOfWeek(3);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.eventDayOfWeek,
        3,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.eventDayOfWeek, equals(3));
    });

    test('엑셀 요일 수집 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setCollectDayOfWeekFromExcel(true);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.collectDayOfWeekFromExcel,
        true,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.collectDayOfWeekFromExcel, isTrue);
    });

    test('엑셀 요일 열 인덱스 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setExcelDayOfWeekColumnIndex(5);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.excelDayOfWeekColumnIndex,
        5,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.excelDayOfWeekColumnIndex, equals(5));
    });

    test('재고 연동 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setLinkPrepaymentToInventory(true);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.linkPrepaymentToInventory,
        true,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.linkPrepaymentToInventory, isTrue);
    });

    test('재고현황 열 수 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setInventoryColumns(4);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.inventoryColumns,
        4,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.inventoryColumns, equals(4));
    });

    test('판매 화면 열 수 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setSaleColumns(6);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.saleColumns,
        6,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.saleColumns, equals(6));
    });

    test('재고현황 세로모드 열 수 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setInventoryColumnsPortrait(4);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.inventoryColumnsPortrait,
        4,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.inventoryColumnsPortrait, equals(4));
    });

    test('재고현황 가로모드 열 수 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setInventoryColumnsLandscape(6);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.inventoryColumnsLandscape,
        6,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.inventoryColumnsLandscape, equals(6));
    });

    test('판매 화면 세로모드 열 수 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setSaleColumnsPortrait(4);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.saleColumnsPortrait,
        4,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.saleColumnsPortrait, equals(4));
    });

    test('판매 화면 가로모드 열 수 설정', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final notifier = container.read(settingsNotifierProvider.notifier);
      
      await notifier.setSaleColumnsLandscape(6);
      
      // 상태 변화 대기
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.saleColumnsLandscape,
        6,
      );
      
      final settingsState = container.read(settingsNotifierProvider);
      expect(settingsState.value!.saleColumnsLandscape, equals(6));
    });
  });

  group('SettingsNotifier 유효성 검사 테스트', () {
    test('잘못된 이벤트 요일 설정 시 에러 상태', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      final notifier = container.read(settingsNotifierProvider.notifier);
      await notifier.setEventDayOfWeek(0);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.eventDayOfWeek,
        7, // 값은 바뀌지 않음
        allowError: true,
      );
      final state = container.read(settingsNotifierProvider);
      expect(state.hasError, isTrue);
      expect(state.error, isA<ProviderException>());

      await notifier.setEventDayOfWeek(8);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.eventDayOfWeek,
        7,
        allowError: true,
      );
      final state2 = container.read(settingsNotifierProvider);
      expect(state2.hasError, isTrue);
      expect(state2.error, isA<ProviderException>());
    });

    test('잘못된 엑셀 요일 열 인덱스 설정 시 에러 상태', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      final notifier = container.read(settingsNotifierProvider.notifier);
      await notifier.setExcelDayOfWeekColumnIndex(-2);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.excelDayOfWeekColumnIndex,
        -1,
        allowError: true,
      );
      final state = container.read(settingsNotifierProvider);
      expect(state.hasError, isTrue);
      expect(state.error, isA<ProviderException>());
    });

    test('잘못된 열 수 설정 시 에러 상태', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      final notifier = container.read(settingsNotifierProvider.notifier);
      await notifier.setInventoryColumns(1);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.inventoryColumns,
        3,
        allowError: true,
      );
      final state = container.read(settingsNotifierProvider);
      expect(state.hasError, isTrue);
      expect(state.error, isA<ProviderException>());

      await notifier.setInventoryColumns(16);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.inventoryColumns,
        3,
        allowError: true,
      );
      final state2 = container.read(settingsNotifierProvider);
      expect(state2.hasError, isTrue);
      expect(state2.error, isA<ProviderException>());

      await notifier.setSaleColumns(1);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.saleColumns,
        3,
        allowError: true,
      );
      final state3 = container.read(settingsNotifierProvider);
      expect(state3.hasError, isTrue);
      expect(state3.error, isA<ProviderException>());

      await notifier.setSaleColumns(16);
      await waitForStateChange(
        container,
        settingsNotifierProvider,
        (state) => state.saleColumns,
        3,
        allowError: true,
      );
      final state4 = container.read(settingsNotifierProvider);
      expect(state4.hasError, isTrue);
      expect(state4.error, isA<ProviderException>());
    });
  });

  group('Helper Provider 테스트', () {
    test('eventDayOfWeekProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final eventDay = container.read(eventDayOfWeekProvider);
      expect(eventDay, equals(7));
    });

    test('collectDayOfWeekFromExcelProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final collectFromExcel = container.read(collectDayOfWeekFromExcelProvider);
      expect(collectFromExcel, isFalse);
    });

    test('excelDayOfWeekColumnIndexProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columnIndex = container.read(excelDayOfWeekColumnIndexProvider);
      expect(columnIndex, equals(-1));
    });

    test('linkPrepaymentToInventoryProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final linkToInventory = container.read(linkPrepaymentToInventoryProvider);
      expect(linkToInventory, isFalse);
    });

    test('inventoryColumnsProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columns = container.read(inventoryColumnsProvider);
      expect(columns, equals(3));
    });

    test('saleColumnsProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columns = container.read(saleColumnsProvider);
      expect(columns, equals(3));
    });

    test('inventoryColumnsPortraitProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columns = container.read(inventoryColumnsPortraitProvider);
      expect(columns, equals(3));
    });

    test('inventoryColumnsLandscapeProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columns = container.read(inventoryColumnsLandscapeProvider);
      expect(columns, equals(5));
    });

    test('saleColumnsPortraitProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columns = container.read(saleColumnsPortraitProvider);
      expect(columns, equals(3));
    });

    test('saleColumnsLandscapeProvider', () async {
      await waitForProviderInitialization(container, settingsNotifierProvider);
      
      final columns = container.read(saleColumnsLandscapeProvider);
      expect(columns, equals(5));
    });
  });
} 
