import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/models/product.dart';
import 'package:parabara/models/product_sort_option.dart';
import 'package:parabara/repositories/product_repository.dart';
import 'package:parabara/repositories/sales_log_repository.dart';
import 'package:parabara/models/sales_log.dart';
import 'package:parabara/services/database_service.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  late Database db;
  late DatabaseService databaseService;
  late ProductRepository repository;
  late SalesLogRepository salesLogRepository;

  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    db = await databaseFactory.openDatabase(
      inMemoryDatabasePath,
      options: OpenDatabaseOptions(
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE products (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL UNIQUE,
              quantity INTEGER NOT NULL,
              price INTEGER NOT NULL,
              sellerName TEXT,
              imagePath TEXT,
              isActive INTEGER DEFAULT 1,

              lastServicedDate INTEGER
            )
          ''');
          await db.execute('''
            CREATE TABLE sales_log (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              productId INTEGER,
              productName TEXT NOT NULL,
              sellerName TEXT,
              soldPrice INTEGER NOT NULL,
              soldQuantity INTEGER NOT NULL,
              totalAmount INTEGER NOT NULL,
              saleTimestamp INTEGER NOT NULL,
              transactionType TEXT DEFAULT 'SALE',
              batchSaleId TEXT
            )
          ''');
        },
      ),
    );

    databaseService = TestDatabaseService(db);
    repository = ProductRepository(database: databaseService);
    salesLogRepository = SalesLogRepository(database: databaseService);

    // 테스트 데이터 삽입
    await db.insert('products', {
      'name': 'Test Product',
      'quantity': 10,
      'price': 1000,
      'sellerName': 'Test Seller',
      'isActive': 1,
      'eventId': 1,
    });
  });

  tearDown(() async {
    await db.close();
  });

  group('ProductRepository SQL Injection Tests', () {
    test('getProductsSorted should safely handle all sort options', () async {
      // 모든 정렬 옵션에 대해 테스트
      for (final sortOption in ProductSortOption.values) {
        final products = await repository.getProductsSorted(sortOption);

        // 결과가 정상적인 Product 객체들만 포함해야 함
        for (final product in products) {
          expect(product, isA<Product>());
          expect(product.name, isNotNull);
          expect(product.quantity, isNonNegative);
          expect(product.price, isNonNegative);
        }
      }
    });

    test('getProductsSorted should handle malicious sort options', () async {
      // ProductSortOption은 enum이므로 SQL injection이 불가능해야 함
      final products = await repository.getProductsSorted(
        ProductSortOption.nameAsc,
      );
      expect(products, isNotEmpty);
      expect(products.first, isA<Product>());
    });

    test('getAllProducts should handle malicious table names', () async {
      final products = await repository.getAllProducts();

      // 결과가 정상적인 Product 객체들만 포함해야 함
      for (final product in products) {
        expect(product, isA<Product>());
        expect(product.name, isNotNull);
        expect(product.quantity, isNonNegative);
        expect(product.price, isNonNegative);
      }
    });
  });

  group('상품 판매자 변경 테스트', () {
    test('상품 판매자 변경 시 연관된 판매 기록도 업데이트되어야 함', () async {
      // 테스트 데이터 준비
      final product = Product(
        id: 0,
        name: '테스트 상품',
        price: 1000,
        quantity: 10,
        sellerName: '기존 판매자',

      );
      
      // 상품과 판매 기록 삽입
      final productId = await repository.insertProduct(product);
      final salesLog1 = SalesLog(
        id: 0,
        productId: productId,
        productName: '테스트 상품',
        sellerName: '기존 판매자',
        soldPrice: 1000,
        soldQuantity: 1,
        totalAmount: 1000,
        saleTimestamp: DateTime.now().millisecondsSinceEpoch,
      );
      
      final salesLog2 = SalesLog(
        id: 0,
        productId: productId,
        productName: '테스트 상품',
        sellerName: '기존 판매자',
        soldPrice: 1000,
        soldQuantity: 2,
        totalAmount: 2000,
        saleTimestamp: DateTime.now().millisecondsSinceEpoch,
      );

      await salesLogRepository.addSalesLog(salesLog1);
      await salesLogRepository.addSalesLog(salesLog2);

      // 판매자 변경
      final updatedProduct = product.copyWith(id: productId, sellerName: '새로운 판매자');
      await repository.updateProduct(updatedProduct, updateRelatedSalesLogs: true);

      // 상품 정보 확인
      final retrievedProduct = await repository.getProductById(productId);
      expect(retrievedProduct?.sellerName, equals('새로운 판매자'));

      // 연관된 판매 기록 확인
      final updatedSalesLogs = await salesLogRepository.getAllSalesLogs();
      final productSalesLogs = updatedSalesLogs.where((log) => log.productId == productId).toList();
      
      expect(productSalesLogs.length, equals(2));
      for (final log in productSalesLogs) {
        expect(log.sellerName, equals('새로운 판매자'));
      }
    });

    test('updateRelatedSalesLogs가 false일 때는 판매 기록이 업데이트되지 않아야 함', () async {
      // 테스트 데이터 준비
      final product = Product(
        id: 0,
        name: '테스트 상품 2',
        price: 2000,
        quantity: 5,
        sellerName: '기존 판매자',

      );
      
      // 상품과 판매 기록 삽입
      final productId = await repository.insertProduct(product);
      final salesLog = SalesLog(
        id: 0,
        productId: productId,
        productName: '테스트 상품 2',
        sellerName: '기존 판매자',
        soldPrice: 2000,
        soldQuantity: 1,
        totalAmount: 2000,
        saleTimestamp: DateTime.now().millisecondsSinceEpoch,
      );

      await salesLogRepository.addSalesLog(salesLog);

      // 판매자 변경 (연관 기록 업데이트 비활성화)
      final updatedProduct = product.copyWith(id: productId, sellerName: '새로운 판매자');
      await repository.updateProduct(updatedProduct, updateRelatedSalesLogs: false);

      // 상품 정보 확인
      final retrievedProduct = await repository.getProductById(productId);
      expect(retrievedProduct?.sellerName, equals('새로운 판매자'));

      // 연관된 판매 기록은 변경되지 않아야 함
      final updatedSalesLogs = await salesLogRepository.getAllSalesLogs();
      final productSalesLog = updatedSalesLogs.firstWhere((log) => log.productId == productId);
      expect(productSalesLog.sellerName, equals('기존 판매자'));
    });
  });
}

/// 테스트용 DatabaseService 구현
class TestDatabaseService implements DatabaseService {
  final Database _db;

  TestDatabaseService(this._db);

  @override
  Future<Database> get database async => _db;

  @override
  Future<void> forceMigration() async {}
}
