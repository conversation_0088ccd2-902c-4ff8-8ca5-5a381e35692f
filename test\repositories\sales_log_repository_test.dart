import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/models/transaction_type.dart';
import 'package:parabara/repositories/sales_log_repository.dart';
import 'package:parabara/services/database_service.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  late Database db;
  late DatabaseService databaseService;
  late SalesLogRepository repository;

  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    db = await databaseFactory.openDatabase(
      inMemoryDatabasePath,
      options: OpenDatabaseOptions(
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE sales_log (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              productId INTEGER,
              productName TEXT NOT NULL,
              sellerName TEXT,
              soldPrice INTEGER NOT NULL,
              soldQuantity INTEGER NOT NULL,
              totalAmount INTEGER NOT NULL,
              saleTimestamp INTEGER NOT NULL,
              transactionType TEXT DEFAULT 'SALE',
              batchSaleId TEXT
            )
          ''');
        },
      ),
    );

    databaseService = TestDatabaseService(db);
    repository = SalesLogRepository(database: databaseService);

    // 테스트 데이터 삽입
    await db.insert('sales_log', {
      'productName': 'Test Product',
      'sellerName': 'Test Seller',
      'soldPrice': 1000,
      'soldQuantity': 1,
      'totalAmount': 1000,
      'saleTimestamp': DateTime.now().millisecondsSinceEpoch,
      'transactionType': TransactionType.sale.value,
    });
  });

  tearDown(() async {
    await db.close();
  });

  group('SalesLogRepository SQL Injection Tests', () {
    test(
      'getTotalSalesAmount should safely handle malicious seller names',
      () async {
        final maliciousSellerNames = [
          "' OR '1'='1",
          "'; DROP TABLE sales_log; --",
          "' UNION SELECT * FROM sqlite_master; --",
          "\\'; DELETE FROM sales_log; --",
        ];

        final now = DateTime.now().millisecondsSinceEpoch;
        final startTime = now - 86400000; // 24시간 전
        final endTime = now;

        for (final sellerName in maliciousSellerNames) {
          final amount = await repository.getTotalSalesAmount(
            startTime,
            endTime,
            sellerName,
          );
          // 악의적인 입력이 실행되지 않고 0이 반환되어야 함
          expect(amount, equals(0));
        }
      },
    );

    test(
      'getOverallSalesStats should safely handle malicious table names',
      () async {
        final stats = await repository.getOverallSalesStats();

        // 결과가 정상적인 형태여야 함
        for (final stat in stats) {
          expect(stat.name, isNotNull);
          expect(stat.count, isNonNegative);
          expect(stat.totalAmount, isNonNegative);
          expect(stat.netSalesAmount, isNonNegative);
        }
      },
    );

    test(
      'getSalesStatsBySeller should safely handle malicious seller names',
      () async {
        final maliciousSellerNames = [
          "' OR '1'='1",
          "'; DROP TABLE sales_log; --",
          "' UNION SELECT * FROM sqlite_master; --",
        ];

        for (final sellerName in maliciousSellerNames) {
          final stats = await repository.getSalesStatsBySeller(sellerName);
          // 악의적인 입력이 실행되지 않고 빈 리스트가 반환되어야 함
          expect(stats, isEmpty);
        }
      },
    );
  });
}

/// 테스트용 DatabaseService 구현
class TestDatabaseService implements DatabaseService {
  final Database _db;

  TestDatabaseService(this._db);

  @override
  Future<Database> get database async => _db;


}
