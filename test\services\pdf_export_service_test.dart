import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:parabara/services/pdf_export_service.dart';
import 'package:pdf/widgets.dart' as pw;

void main() {
  group('PdfExportService Tests', () {
    test('PDF 문서 생성 로직 테스트', () {
      // 테스트 데이터 생성
      final dailyStats = <DateTime, Map<String, dynamic>>{
        DateTime(2024, 1, 1): {
          'totalRevenue': 100000,
          'totalQuantity': 50,
        },
        DateTime(2024, 1, 2): {
          'totalRevenue': 150000,
          'totalQuantity': 75,
        },
      };

      final productStats = [
        {
          'productName': 'Test Product 1',
          'totalRevenue': 80000,
          'totalQuantity': 40,
        },
        {
          'productName': 'Test Product 2', 
          'totalRevenue': 70000,
          'totalQuantity': 35,
        },
      ];

      // 헬퍼 메서드들이 올바르게 작동하는지 테스트
      expect(dailyStats.length, equals(2));
      expect(productStats.length, equals(2));
      
      // 전체 통계 계산 로직 테스트
      int totalRevenue = 0;
      int totalQuantity = 0;
      for (final dayData in dailyStats.values) {
        totalRevenue += (dayData['totalRevenue'] as int? ?? 0);
        totalQuantity += (dayData['totalQuantity'] as int? ?? 0);
      }
      
      expect(totalRevenue, equals(250000));
      expect(totalQuantity, equals(125));
      
      final avgDailyRevenue = dailyStats.isNotEmpty ? totalRevenue / dailyStats.length : 0;
      expect(avgDailyRevenue, equals(125000));
      
      print('✅ PDF 문서 생성 로직 테스트 성공!');
    });

    test('날짜 포맷 헬퍼 테스트', () {
      final testDate = DateTime(2024, 1, 15);
      // PDF 서비스의 private 메서드를 직접 테스트할 수 없으므로
      // 동일한 로직을 여기서 테스트
      final formatted = '${testDate.year}-${testDate.month.toString().padLeft(2, '0')}-${testDate.day.toString().padLeft(2, '0')}';
      expect(formatted, equals('2024-01-15'));
      
      print('✅ 날짜 포맷 헬퍼 테스트 성공!');
    });
  });
}
