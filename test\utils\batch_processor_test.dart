import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/batch_processor.dart';
import 'package:parabara/utils/cancellation_token.dart';

void main() {
  group('AdvancedBatchProcessor', () {
    test('정상적인 배치 처리', () async {
      final items = List.generate(50, (i) => i);
      final processedItems = <int>[];
      final processor = AdvancedBatchProcessor<int>(
        items: items,
        processBatch: (batch) async {
          await Future.delayed(const Duration(milliseconds: 5));
          processedItems.addAll(batch);
        },
      );
      final result = await processor.process();
      expect(result.succeeded.length, equals(50));
      expect(processedItems.length, equals(50));
      expect(processedItems, equals(items));
      expect(result.failed.length, equals(0));
      expect(result.hasErrors, isFalse);
    });

    test('타임아웃 처리', () async {
      final items = List.generate(10, (i) => i);
      final processor = AdvancedBatchProcessor<int>(
        items: items,
        processBatch: (batch) async {
          // 타임아웃보다 오래 걸리는 작업
          await Future.delayed(const Duration(milliseconds: 200));
        },
        config: const BatchProcessingConfig(
          timeoutPerBatch: Duration(milliseconds: 50),
          retryAttempts: 0, // 재시도 없음
        ),
      );
      
      final result = await processor.process();
      // 타임아웃으로 인해 모든 아이템이 실패해야 함
      expect(result.succeeded.length, equals(0));
      expect(result.failed.length, equals(10));
      expect(result.hasErrors, isTrue);
    });

    test('재시도 메커니즘', () async {
      var attempts = 0;
      final items = List.generate(5, (i) => i);
      final processedItems = <int>[];
      final processor = AdvancedBatchProcessor<int>(
        items: items,
        processBatch: (batch) async {
          attempts++;
          if (attempts < 3) {
            throw Exception('처리 실패 (시도 $attempts)');
          }
          processedItems.addAll(batch);
        },
        config: const BatchProcessingConfig(retryAttempts: 3),
      );
      final result = await processor.process();
      expect(result.succeeded.length + result.failed.length, equals(5));
      expect(attempts, greaterThanOrEqualTo(3));
    });

    test('취소 기능', () async {
      final items = List.generate(100, (i) => i);
      final processedItems = <int>[];
      final token = CancellationToken();
      final processor = AdvancedBatchProcessor<int>(
        items: items,
        processBatch: (batch) async {
          await Future.delayed(const Duration(milliseconds: 50));
          processedItems.addAll(batch);
        },
      );
      
      final future = processor.processWithCancellation(token);
      await Future.delayed(const Duration(milliseconds: 100));
      token.cancel();
      
      final result = await future;
      // 취소로 인해 일부만 처리되었거나 실패했을 수 있음
      expect(result.succeeded.length + result.failed.length, lessThanOrEqualTo(100));

      // 리소스 정리
      token.dispose();
    });

    test('에러 처리', () async {
      final items = List.generate(10, (i) => i);
      final processor = AdvancedBatchProcessor<int>(
        items: items,
        processBatch: (batch) async {
          throw Exception('처리 중 오류 발생');
        },
        config: const BatchProcessingConfig(retryAttempts: 0),
      );
      
      final result = await processor.process();
      expect(result.succeeded.length, equals(0));
      expect(result.failed.length, equals(10));
      expect(result.hasErrors, isTrue);
      expect(result.successRate, equals(0.0));
    });

    test('빈 배치 처리', () async {
      final processor = AdvancedBatchProcessor<int>(
        items: [],
        processBatch: (batch) async {
          // 아무것도 하지 않음
        },
      );
      
      final result = await processor.process();
      expect(result.succeeded.length, equals(0));
      expect(result.failed.length, equals(0));
      expect(result.hasErrors, isFalse);
      expect(result.successRate, equals(0.0));
    });
  });
}
