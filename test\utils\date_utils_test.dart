import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/date_utils.dart' as app_date_utils;

void main() {
  group('DateUtils Tests', () {
    // 테스트에 사용할 고정된 날짜들
    final testDate = DateTime(
      2024,
      3,
      15,
      14,
      30,
      45,
    ); // 2024년 3월 15일 금요일 14:30:45
    final testTimestamp = testDate.millisecondsSinceEpoch;

    group('타임스탬프 변환 메서드 테스트', () {
      test('getCurrentTimestamp - 현재 타임스탬프 반환', () {
        final timestamp1 = app_date_utils.DateUtils.getCurrentTimestamp();
        // 잠시 대기
        final timestamp2 = app_date_utils.DateUtils.getCurrentTimestamp();

        expect(timestamp1, isA<int>());
        expect(timestamp2, isA<int>());
        expect(timestamp2, greaterThanOrEqualTo(timestamp1));
      });

      test('timestampToDateTime - 타임스탬프를 DateTime으로 변환', () {
        final result = app_date_utils.DateUtils.timestampToDateTime(
          testTimestamp,
        );
        expect(result, equals(testDate));
      });

      test('dateTimeToTimestamp - DateTime을 타임스탬프로 변환', () {
        final result = app_date_utils.DateUtils.dateTimeToTimestamp(testDate);
        expect(result, equals(testTimestamp));
      });
    });

    group('날짜 포맷팅 메서드 테스트', () {
      test('formatDate - yyyy-MM-dd 형식', () {
        final result = app_date_utils.DateUtils.formatDate(testDate);
        expect(result, equals('2024-03-15'));
      });

      test('formatTime - HH:mm:ss 형식', () {
        final result = app_date_utils.DateUtils.formatTime(testDate);
        expect(result, equals('14:30:45'));
      });

      test('formatDateTime - yyyy-MM-dd HH:mm:ss 형식', () {
        final result = app_date_utils.DateUtils.formatDateTime(testDate);
        expect(result, equals('2024-03-15 14:30:45'));
      });

      test('formatDisplayDate - M월 d일 형식', () {
        final result = app_date_utils.DateUtils.formatDisplayDate(testDate);
        expect(result, equals('3월 15일'));
      });

      test('formatDisplayDateTime - M월 d일 HH:mm 형식', () {
        final result = app_date_utils.DateUtils.formatDisplayDateTime(testDate);
        expect(result, equals('3월 15일 14:30'));
      });

      // formatFullDisplay는 한국어 로케일 초기화가 필요해서 테스트 환경에서 제외
      // test('formatFullDisplay - yyyy년 M월 d일 (요일) 형식', () {
      //   final result = app_date_utils.DateUtils.formatFullDisplay(testDate);
      //   expect(result, contains('2024년 3월 15일'));
      //   expect(result, contains('금'));
      // });

      test('formatTimestampToDisplayDate - 타임스탬프를 표시용 날짜로', () {
        final result = app_date_utils.DateUtils.formatTimestampToDisplayDate(
          testTimestamp,
        );
        expect(result, equals('3월 15일'));
      });

      test('formatTimestampToDisplayDateTime - 타임스탬프를 표시용 날짜시간으로', () {
        final result = app_date_utils
            .DateUtils.formatTimestampToDisplayDateTime(testTimestamp);
        expect(result, equals('3월 15일 14:30'));
      });
    });

    group('파싱 메서드 테스트', () {
      test('parseDate - 올바른 날짜 문자열 파싱', () {
        final result = app_date_utils.DateUtils.parseDate('2024-03-15');
        expect(result, isNotNull);
        expect(result!.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(15));
      });

      test('parseDate - 잘못된 날짜 문자열 처리', () {
        expect(app_date_utils.DateUtils.parseDate(''), isNull);
        // DateFormat.parse는 일부 잘못된 형식도 자동 조정하므로 확실히 잘못된 형식만 테스트
        expect(app_date_utils.DateUtils.parseDate('invalid'), isNull);
        expect(app_date_utils.DateUtils.parseDate('2024/03/15'), isNull);
        expect(app_date_utils.DateUtils.parseDate('not-a-date'), isNull);
        expect(app_date_utils.DateUtils.parseDate('2024-03'), isNull);
      });

      test('parseDateTime - 올바른 날짜시간 문자열 파싱', () {
        final result = app_date_utils.DateUtils.parseDateTime(
          '2024-03-15 14:30:45',
        );
        expect(result, isNotNull);
        expect(result!.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(15));
        expect(result.hour, equals(14));
        expect(result.minute, equals(30));
        expect(result.second, equals(45));
      });

      test('parseDateTime - 잘못된 날짜시간 문자열 처리', () {
        expect(app_date_utils.DateUtils.parseDateTime(''), isNull);
        expect(app_date_utils.DateUtils.parseDateTime('2024-03-15'), isNull);
        expect(
          app_date_utils.DateUtils.parseDateTime('invalid datetime'),
          isNull,
        );
      });
    });

    group('요일 관련 메서드 테스트', () {
      test('getDayOfWeekName - 요일 번호를 한글 요일명으로', () {
        expect(app_date_utils.DateUtils.getDayOfWeekName(1), equals('월요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(2), equals('화요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(3), equals('수요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(4), equals('목요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(5), equals('금요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(6), equals('토요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(7), equals('일요일'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(0), equals('알 수 없음'));
        expect(app_date_utils.DateUtils.getDayOfWeekName(8), equals('알 수 없음'));
      });

      test('getDayOfWeekShortName - 요일 번호를 짧은 한글 요일명으로', () {
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(1), equals('월'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(2), equals('화'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(3), equals('수'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(4), equals('목'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(5), equals('금'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(6), equals('토'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(7), equals('일'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(0), equals('?'));
        expect(app_date_utils.DateUtils.getDayOfWeekShortName(8), equals('?'));
      });

      test('getWeekdayNumber - DateTime의 요일을 번호로 반환', () {
        final monday = DateTime(2024, 3, 11); // 월요일
        final friday = DateTime(2024, 3, 15); // 금요일
        final sunday = DateTime(2024, 3, 17); // 일요일

        expect(app_date_utils.DateUtils.getWeekdayNumber(monday), equals(1));
        expect(app_date_utils.DateUtils.getWeekdayNumber(friday), equals(5));
        expect(app_date_utils.DateUtils.getWeekdayNumber(sunday), equals(7));
      });

      test('isTodayDayOfWeek - 오늘이 특정 요일인지 확인', () {
        final today = DateTime.now();
        final todayWeekday = today.weekday;

        expect(app_date_utils.DateUtils.isTodayDayOfWeek(todayWeekday), isTrue);
        expect(
          app_date_utils.DateUtils.isTodayDayOfWeek(todayWeekday == 1 ? 2 : 1),
          isFalse,
        );
      });
    });

    group('날짜 비교 메서드 테스트', () {
      test('isSameDay - 두 날짜가 같은 날인지 확인', () {
        final date1 = DateTime(2024, 3, 15, 10, 30);
        final date2 = DateTime(2024, 3, 15, 18, 45);
        final date3 = DateTime(2024, 3, 16, 10, 30);

        expect(app_date_utils.DateUtils.isSameDay(date1, date2), isTrue);
        expect(app_date_utils.DateUtils.isSameDay(date1, date3), isFalse);
      });

      test('isToday - 오늘인지 확인', () {
        final today = DateTime.now();
        final yesterday = today.subtract(const Duration(days: 1));

        expect(app_date_utils.DateUtils.isToday(today), isTrue);
        expect(app_date_utils.DateUtils.isToday(yesterday), isFalse);
      });

      test('isYesterday - 어제인지 확인', () {
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        final dayBeforeYesterday = now.subtract(const Duration(days: 2));

        expect(app_date_utils.DateUtils.isYesterday(yesterday), isTrue);
        expect(app_date_utils.DateUtils.isYesterday(now), isFalse);
        expect(
          app_date_utils.DateUtils.isYesterday(dayBeforeYesterday),
          isFalse,
        );
      });

      test('isTomorrow - 내일인지 확인', () {
        final now = DateTime.now();
        final tomorrow = now.add(const Duration(days: 1));
        final dayAfterTomorrow = now.add(const Duration(days: 2));

        expect(app_date_utils.DateUtils.isTomorrow(tomorrow), isTrue);
        expect(app_date_utils.DateUtils.isTomorrow(now), isFalse);
        expect(app_date_utils.DateUtils.isTomorrow(dayAfterTomorrow), isFalse);
      });
    });

    group('상대적 날짜 표시 테스트', () {
      test('getRelativeDateString - 상대적 날짜 문자열', () {
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        final tomorrow = now.add(const Duration(days: 1));
        final someOtherDay = DateTime(2024, 1, 1);

        expect(
          app_date_utils.DateUtils.getRelativeDateString(now),
          equals('오늘'),
        );
        expect(
          app_date_utils.DateUtils.getRelativeDateString(yesterday),
          equals('어제'),
        );
        expect(
          app_date_utils.DateUtils.getRelativeDateString(tomorrow),
          equals('내일'),
        );
        expect(
          app_date_utils.DateUtils.getRelativeDateString(someOtherDay),
          equals('1월 1일'),
        );
      });
    });

    group('날짜 범위 메서드 테스트', () {
      test('getStartOfDay - 하루의 시작 시간', () {
        final result = app_date_utils.DateUtils.getStartOfDay(testDate);
        expect(result.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(15));
        expect(result.hour, equals(0));
        expect(result.minute, equals(0));
        expect(result.second, equals(0));
        expect(result.millisecond, equals(0));
      });

      test('getEndOfDay - 하루의 끝 시간', () {
        final result = app_date_utils.DateUtils.getEndOfDay(testDate);
        expect(result.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(15));
        expect(result.hour, equals(23));
        expect(result.minute, equals(59));
        expect(result.second, equals(59));
        expect(result.millisecond, equals(999));
      });

      test('getStartOfWeek - 주의 시작일 (월요일)', () {
        final friday = DateTime(2024, 3, 15); // 금요일
        final result = app_date_utils.DateUtils.getStartOfWeek(friday);

        expect(result.weekday, equals(1)); // 월요일
        expect(result.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(11)); // 2024년 3월 11일 월요일
      });

      test('getEndOfWeek - 주의 끝일 (일요일)', () {
        final friday = DateTime(2024, 3, 15); // 금요일
        final result = app_date_utils.DateUtils.getEndOfWeek(friday);

        expect(result.weekday, equals(7)); // 일요일
        expect(result.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(17)); // 2024년 3월 17일 일요일
      });

      test('getStartOfMonth - 월의 시작일', () {
        final result = app_date_utils.DateUtils.getStartOfMonth(testDate);
        expect(result.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(1));
      });

      test('getEndOfMonth - 월의 끝일', () {
        final result = app_date_utils.DateUtils.getEndOfMonth(testDate);
        expect(result.year, equals(2024));
        expect(result.month, equals(3));
        expect(result.day, equals(31)); // 3월은 31일까지
      });

      test('getEndOfMonth - 2월 윤년 테스트', () {
        final feb2024 = DateTime(2024, 2, 15); // 2024년은 윤년
        final feb2023 = DateTime(2023, 2, 15); // 2023년은 평년

        final result2024 = app_date_utils.DateUtils.getEndOfMonth(feb2024);
        final result2023 = app_date_utils.DateUtils.getEndOfMonth(feb2023);

        expect(result2024.day, equals(29)); // 윤년 2월은 29일
        expect(result2023.day, equals(28)); // 평년 2월은 28일
      });
    });

    group('날짜 계산 메서드 테스트', () {
      test('daysBetween - 두 날짜 간의 일수 차이', () {
        final date1 = DateTime(2024, 3, 10);
        final date2 = DateTime(2024, 3, 15);
        final date3 = DateTime(2024, 3, 8);

        expect(app_date_utils.DateUtils.daysBetween(date1, date2), equals(5));
        expect(app_date_utils.DateUtils.daysBetween(date2, date1), equals(-5));
        expect(app_date_utils.DateUtils.daysBetween(date1, date3), equals(-2));
        expect(app_date_utils.DateUtils.daysBetween(date1, date1), equals(0));
      });
    });

    group('경과 시간 표시 테스트', () {
      test('getTimeAgoString - 경과 시간 문자열', () {
        final now = DateTime.now();
        final minutesAgo = now.subtract(const Duration(minutes: 30));
        final hoursAgo = now.subtract(const Duration(hours: 2));
        final daysAgo = now.subtract(const Duration(days: 3));
        final secondsAgo = now.subtract(const Duration(seconds: 30));

        expect(
          app_date_utils.DateUtils.getTimeAgoString(minutesAgo),
          equals('30분 전'),
        );
        expect(
          app_date_utils.DateUtils.getTimeAgoString(hoursAgo),
          equals('2시간 전'),
        );
        expect(
          app_date_utils.DateUtils.getTimeAgoString(daysAgo),
          equals('3일 전'),
        );
        expect(
          app_date_utils.DateUtils.getTimeAgoString(secondsAgo),
          equals('방금 전'),
        );
      });

      test('getTimestampAgoString - 타임스탬프 경과 시간 문자열', () {
        final now = DateTime.now();
        final hoursAgo = now.subtract(const Duration(hours: 1));
        final timestamp = hoursAgo.millisecondsSinceEpoch;

        expect(
          app_date_utils.DateUtils.getTimestampAgoString(timestamp),
          equals('1시간 전'),
        );
      });
    });

    group('극단적인 케이스 테스트', () {
      test('매우 오래된 날짜 처리', () {
        final oldDate = DateTime(1900, 1, 1);
        expect(
          app_date_utils.DateUtils.formatDate(oldDate),
          equals('1900-01-01'),
        );
        expect(
          app_date_utils.DateUtils.getWeekdayNumber(oldDate),
          equals(1),
        ); // 1900년 1월 1일은 월요일
      });

      test('미래 날짜 처리', () {
        final futureDate = DateTime(2100, 12, 31);
        expect(
          app_date_utils.DateUtils.formatDate(futureDate),
          equals('2100-12-31'),
        );
        expect(app_date_utils.DateUtils.isToday(futureDate), isFalse);
      });

      test('윤년 경계값 테스트', () {
        final feb29_2024 = DateTime(2024, 2, 29); // 유효한 윤년 날짜
        expect(
          app_date_utils.DateUtils.formatDate(feb29_2024),
          equals('2024-02-29'),
        );
      });

      test('월말 경계값 테스트', () {
        final jan31 = DateTime(2024, 1, 31);
        final startOfNextMonth = app_date_utils.DateUtils.getStartOfMonth(
          DateTime(jan31.year, jan31.month + 1, 1),
        );
        expect(startOfNextMonth.month, equals(2));
        expect(startOfNextMonth.day, equals(1));
      });
    });

    group('실제 사용 시나리오 테스트', () {
      test('판매 기록 날짜 포맷팅 시나리오', () {
        final saleDate = DateTime(2024, 3, 15, 9, 30);

        expect(
          app_date_utils.DateUtils.formatDisplayDateTime(saleDate),
          equals('3월 15일 09:30'),
        );
        expect(
          app_date_utils.DateUtils.formatDateTime(saleDate),
          equals('2024-03-15 09:30:00'),
        );
        expect(
          app_date_utils.DateUtils.getWeekdayNumber(saleDate),
          equals(5),
        ); // 금요일
      });

      test('일간 매출 집계를 위한 날짜 범위 시나리오', () {
        final someDay = DateTime(2024, 3, 15, 14, 30);
        final startOfDay = app_date_utils.DateUtils.getStartOfDay(someDay);
        final endOfDay = app_date_utils.DateUtils.getEndOfDay(someDay);

        expect(startOfDay, equals(DateTime(2024, 3, 15, 0, 0, 0)));
        expect(endOfDay, equals(DateTime(2024, 3, 15, 23, 59, 59, 999)));

        // 하루 동안의 모든 시간이 포함되는지 확인
        final middleOfDay = DateTime(2024, 3, 15, 12, 0);
        expect(middleOfDay.isAfter(startOfDay), isTrue);
        expect(middleOfDay.isBefore(endOfDay), isTrue);
      });

      test('주간 매출 집계를 위한 날짜 범위 시나리오', () {
        final wednesday = DateTime(2024, 3, 13); // 수요일
        final startOfWeek = app_date_utils.DateUtils.getStartOfWeek(wednesday);
        final endOfWeek = app_date_utils.DateUtils.getEndOfWeek(wednesday);

        expect(startOfWeek.weekday, equals(1)); // 월요일
        expect(endOfWeek.weekday, equals(7)); // 일요일
        expect(
          app_date_utils.DateUtils.daysBetween(startOfWeek, endOfWeek),
          equals(6),
        );
      });

      test('월간 매출 집계를 위한 날짜 범위 시나리오', () {
        final march15 = DateTime(2024, 3, 15);
        final startOfMonth = app_date_utils.DateUtils.getStartOfMonth(march15);
        final endOfMonth = app_date_utils.DateUtils.getEndOfMonth(march15);

        expect(startOfMonth, equals(DateTime(2024, 3, 1)));
        expect(endOfMonth, equals(DateTime(2024, 3, 31)));
        expect(
          app_date_utils.DateUtils.daysBetween(startOfMonth, endOfMonth),
          equals(30),
        );
      });

      test('최근 활동 표시 시나리오', () {
        final now = DateTime.now();
        final recentActivity = now.subtract(const Duration(minutes: 5));
        final oldActivity = now.subtract(const Duration(days: 2));

        expect(
          app_date_utils.DateUtils.getTimeAgoString(recentActivity),
          equals('5분 전'),
        );
        expect(
          app_date_utils.DateUtils.getTimeAgoString(oldActivity),
          equals('2일 전'),
        );
      });
    });

    group('호환성 테스트', () {
      test('AppDateUtils 별칭 동작 확인', () {
        final testDate = DateTime(2024, 3, 15);

        // DateUtils와 AppDateUtils가 같은 결과를 반환하는지 확인
        expect(
          app_date_utils.DateUtils.formatDate(testDate),
          equals(app_date_utils.AppDateUtils.formatDate(testDate)),
        );
        expect(
          app_date_utils.DateUtils.getWeekdayNumber(testDate),
          equals(app_date_utils.AppDateUtils.getWeekdayNumber(testDate)),
        );
      });
    });
  });
}
