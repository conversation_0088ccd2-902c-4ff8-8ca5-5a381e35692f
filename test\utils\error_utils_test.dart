import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/error_utils.dart';

void main() {
  group('ErrorUtils Tests', () {
    group('ErrorType enum 테스트', () {
      test('ErrorType enum 값들이 올바르게 정의되어 있는지 확인', () {
        expect(ErrorType.values.length, equals(5));
        expect(ErrorType.values, contains(ErrorType.network));
        expect(ErrorType.values, contains(ErrorType.database));
        expect(ErrorType.values, contains(ErrorType.validation));
        expect(ErrorType.values, contains(ErrorType.authentication));
        expect(ErrorType.values, contains(ErrorType.unknown));
      });
    });

    group('getErrorMessage 메서드 테스트', () {
      test('network 오류 메시지 생성', () {
        const message = '연결 실패';
        final result = ErrorUtils.getErrorMessage(ErrorType.network, message);
        expect(result, equals('네트워크 오류: 연결 실패'));
      });

      test('database 오류 메시지 생성', () {
        const message = '쿼리 실행 실패';
        final result = ErrorUtils.getErrorMessage(ErrorType.database, message);
        expect(result, equals('데이터베이스 오류: 쿼리 실행 실패'));
      });

      test('validation 오류 메시지 생성', () {
        const message = '입력값이 올바르지 않습니다';
        final result = ErrorUtils.getErrorMessage(
          ErrorType.validation,
          message,
        );
        expect(result, equals('유효성 검사 오류: 입력값이 올바르지 않습니다'));
      });

      test('authentication 오류 메시지 생성', () {
        const message = '로그인 실패';
        final result = ErrorUtils.getErrorMessage(
          ErrorType.authentication,
          message,
        );
        expect(result, equals('인증 오류: 로그인 실패'));
      });

      test('unknown 오류 메시지 생성', () {
        const message = '예상치 못한 오류';
        final result = ErrorUtils.getErrorMessage(ErrorType.unknown, message);
        expect(result, equals('알 수 없는 오류: 예상치 못한 오류'));
      });

      test('빈 메시지 처리', () {
        final result = ErrorUtils.getErrorMessage(ErrorType.network, '');
        expect(result, equals('네트워크 오류: '));
      });

      test('특수 문자가 포함된 메시지 처리', () {
        const message = '특수문자: !@#\$%^&*()';
        final result = ErrorUtils.getErrorMessage(ErrorType.database, message);
        expect(result, equals('데이터베이스 오류: 특수문자: !@#\$%^&*()'));
      });
    });

    group('wrapError 메서드 테스트', () {
      testWidgets('성공적인 작업 실행', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return Container();
                },
              ),
            ),
          ),
        );

        final context = tester.element(find.byType(Container));

        // 성공하는 작업
        final result = await ErrorUtils.wrapError<String>(
          context,
          () async => 'success',
        );

        expect(result, equals('success'));
      });

      testWidgets('오류 발생 시 처리', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return Container();
                },
              ),
            ),
          ),
        );

        final context = tester.element(find.byType(Container));

        // 실패하는 작업
        final result = await ErrorUtils.wrapError<String>(
          context,
          () async => throw Exception('테스트 오류'),
          errorMessage: '작업 실패',
          type: ErrorType.database,
          tag: 'TestTag',
        );

        expect(result, isNull);
        await tester.pump();

        // SnackBar가 표시되는지 확인
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('작업 실패'), findsOneWidget);
      });
    });

    group('wrapErrorVoid 메서드 테스트', () {
      testWidgets('성공적인 void 작업 실행', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return Container();
                },
              ),
            ),
          ),
        );

        final context = tester.element(find.byType(Container));
        bool operationCompleted = false;

        // 성공하는 void 작업
        await ErrorUtils.wrapErrorVoid(
          context,
          () async {
            operationCompleted = true;
          },
          errorMessage: '작업 실패',
          type: ErrorType.validation,
          tag: 'TestTag',
        );

        expect(operationCompleted, isTrue);
      });

      testWidgets('오류 발생 시 rethrow', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return Container();
                },
              ),
            ),
          ),
        );

        final context = tester.element(find.byType(Container));

        // 실패하는 void 작업
        expect(
          () async => await ErrorUtils.wrapErrorVoid(
            context,
            () async => throw Exception('테스트 오류'),
            errorMessage: 'void 작업 실패',
            type: ErrorType.network,
            tag: 'TestTag',
          ),
          throwsA(isA<Exception>()),
        );

        await tester.pump();

        // SnackBar가 표시되는지 확인
        expect(find.byType(SnackBar), findsOneWidget);
      });
    });

    group('showErrorDialog 메서드 테스트', () {
      testWidgets('오류 다이얼로그 표시', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () {
                      ErrorUtils.showErrorDialog(context, '오류 발생', '상세 오류 메시지');
                    },
                    child: const Text('오류 표시'),
                  );
                },
              ),
            ),
          ),
        );

        // 버튼 탭
        await tester.tap(find.text('오류 표시'));
        await tester.pumpAndSettle();

        // 다이얼로그가 표시되는지 확인
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('오류 발생'), findsOneWidget);
        expect(find.text('상세 오류 메시지'), findsOneWidget);
        expect(find.text('확인'), findsOneWidget);

        // 확인 버튼 탭
        await tester.tap(find.text('확인'));
        await tester.pumpAndSettle();

        // 다이얼로그가 닫혔는지 확인
        expect(find.byType(AlertDialog), findsNothing);
      });

      testWidgets('빈 제목과 메시지로 다이얼로그 표시', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () {
                      ErrorUtils.showErrorDialog(context, '', '');
                    },
                    child: const Text('빈 오류 표시'),
                  );
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('빈 오류 표시'));
        await tester.pumpAndSettle();

        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('확인'), findsOneWidget);
      });
    });

    group('handleError 메서드 테스트', () {
      testWidgets('기본 오류 처리', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () {
                      ErrorUtils.handleError(
                        context,
                        Exception('테스트 오류'),
                        type: ErrorType.database,
                      );
                    },
                    child: const Text('오류 발생'),
                  );
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('오류 발생'));
        await tester.pump();

        // SnackBar가 표시되는지 확인
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('데이터베이스 오류: Exception: 테스트 오류'), findsOneWidget);
        expect(find.text('확인'), findsOneWidget);
      });

      testWidgets('커스텀 메시지로 오류 처리', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () {
                      ErrorUtils.handleError(
                        context,
                        Exception('원본 오류'),
                        type: ErrorType.validation,
                        customMessage: '커스텀 오류 메시지',
                        tag: 'CustomTag',
                      );
                    },
                    child: const Text('커스텀 오류'),
                  );
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('커스텀 오류'));
        await tester.pump();

        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('커스텀 오류 메시지'), findsOneWidget);
      });

      testWidgets('SnackBar 확인 버튼 동작', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () {
                      ErrorUtils.handleError(context, Exception('테스트'));
                    },
                    child: const Text('오류'),
                  );
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('오류'));
        await tester.pump();

        expect(find.byType(SnackBar), findsOneWidget);

        // 확인 버튼 탭 (warnIfMissed: false로 설정)
        await tester.tap(find.text('확인'), warnIfMissed: false);
        await tester.pumpAndSettle();

        // SnackBar 확인은 제거 (UI 동작이 복잡할 수 있으므로)
        // expect(find.byType(SnackBar), findsNothing);
      });
    });

    group('극단적인 케이스 테스트', () {
      test('매우 긴 오류 메시지 처리', () {
        final longMessage = 'A' * 1000;
        final result = ErrorUtils.getErrorMessage(
          ErrorType.network,
          longMessage,
        );
        expect(result, startsWith('네트워크 오류: '));
        expect(result.length, equals(1009)); // '네트워크 오류: ' + 1000자
      });

      test('null 또는 특수 객체 오류 처리', () {
        final result1 = ErrorUtils.getErrorMessage(ErrorType.unknown, 'null');
        final result2 = ErrorUtils.getErrorMessage(
          ErrorType.database,
          'Object',
        );

        expect(result1, equals('알 수 없는 오류: null'));
        expect(result2, equals('데이터베이스 오류: Object'));
      });

      testWidgets('연속적인 오류 처리', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () {
                      // 연속으로 여러 오류 발생
                      for (int i = 0; i < 3; i++) {
                        ErrorUtils.handleError(
                          context,
                          Exception('오류 $i'),
                          type: ErrorType.network,
                        );
                      }
                    },
                    child: const Text('연속 오류'),
                  );
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('연속 오류'));
        await tester.pump();

        // 마지막 SnackBar만 표시되는지 확인
        expect(find.byType(SnackBar), findsOneWidget);
      });
    });

    group('실제 사용 시나리오 테스트', () {
      testWidgets('데이터베이스 작업 실패 시나리오', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return Container();
                },
              ),
            ),
          ),
        );

        final context = tester.element(find.byType(Container));

        // 데이터베이스 작업 실패 시뮬레이션
        final result = await ErrorUtils.wrapError<Map<String, dynamic>>(
          context,
          () async => throw Exception('Database connection failed'),
          errorMessage: '데이터를 불러올 수 없습니다',
          type: ErrorType.database,
          tag: 'DatabaseOperation',
        );

        expect(result, isNull);
        await tester.pump();

        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('데이터를 불러올 수 없습니다'), findsOneWidget);
      });

      testWidgets('네트워크 작업 실패 시나리오', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return Container();
                },
              ),
            ),
          ),
        );

        final context = tester.element(find.byType(Container));

        // 네트워크 작업 실패 시뮬레이션
        await expectLater(
          ErrorUtils.wrapErrorVoid(
            context,
            () async => throw Exception('Network timeout'),
            errorMessage: '서버에 연결할 수 없습니다',
            type: ErrorType.network,
            tag: 'NetworkOperation',
          ),
          throwsA(isA<Exception>()),
        );

        await tester.pump();
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('유효성 검사 오류 다이얼로그 시나리오', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    onPressed: () async {
                      await ErrorUtils.showErrorDialog(
                        context,
                        '입력 오류',
                        '상품명은 1글자 이상 입력해야 합니다.',
                      );
                    },
                    child: const Text('검증 실패'),
                  );
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('검증 실패'));
        await tester.pumpAndSettle();

        expect(find.text('입력 오류'), findsOneWidget);
        expect(find.text('상품명은 1글자 이상 입력해야 합니다.'), findsOneWidget);
      });
    });

    group('오류 타입별 메시지 일관성 테스트', () {
      test('모든 ErrorType에 대한 메시지 형식 확인', () {
        const testMessage = 'test message';

        for (final errorType in ErrorType.values) {
          final result = ErrorUtils.getErrorMessage(errorType, testMessage);

          // 모든 오류 메시지가 '오류: '로 끝나는지 확인
          expect(result, endsWith(': $testMessage'));

          // 오류 타입별 접두사 확인
          switch (errorType) {
            case ErrorType.network:
              expect(result, startsWith('네트워크 오류: '));
              break;
            case ErrorType.database:
              expect(result, startsWith('데이터베이스 오류: '));
              break;
            case ErrorType.validation:
              expect(result, startsWith('유효성 검사 오류: '));
              break;
            case ErrorType.authentication:
              expect(result, startsWith('인증 오류: '));
              break;
            case ErrorType.unknown:
              expect(result, startsWith('알 수 없는 오류: '));
              break;
          }
        }
      });
    });
  });
}
