import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/mobile_performance_utils.dart';
import '../test_helper.dart';

void main() {
  group('MobilePerformanceUtils Tests', () {
    setUpAll(() async {
      await TestEnvironmentOptimizer.initialize();
    });
    
    tearDownAll(() async {
      await TestEnvironmentOptimizer.cleanup();
    });
    
    testWidgets('성능 최적화 초기화 테스트', (WidgetTester tester) async {
      // 테스트 환경에서는 제한적인 초기화만 수행
      final imageCache = PaintingBinding.instance.imageCache;
      imageCache.maximumSize = 1000;
      
      // 이미지 캐시 설정 확인
      expect(imageCache.maximumSize, 1000);
      expect(imageCache.maximumSizeBytes, greaterThan(0));
      
      // 테스트 완료 후 정리
      await tester.pumpAndSettle();
    });

    testWidgets('RepaintBoundary 테스트', (WidgetTester tester) async {
      final testWidget = Container(
        width: 100,
        height: 100,
        color: Colors.red,
      );

      // RepaintBoundary로 감싸기
      final optimizedWidget = RepaintBoundary(child: testWidget);

      await tester.pumpWidget(MaterialApp(home: Scaffold(body: optimizedWidget)));
      await tester.pumpAndSettle();

      // RepaintBoundary가 적용되었는지 확인
      final repaintBoundaries = find.byType(RepaintBoundary);
      expect(repaintBoundaries, findsAtLeastNWidgets(1));
      
      // Container가 있는지 확인
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);
    });

    test('스크롤 컨트롤러 생성 테스트', () {
      final controller = ScrollController(
        keepScrollOffset: true,
        debugLabel: 'test_controller',
      );

      expect(controller, isA<ScrollController>());
      expect(controller.keepScrollOffset, true);
      expect(controller.debugLabel, 'test_controller');
    });

    test('그리드 델리게이트 생성 테스트', () {
      final delegate = SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.0,
        crossAxisSpacing: 4.0,
        mainAxisSpacing: 4.0,
      );

      expect(delegate, isA<SliverGridDelegateWithFixedCrossAxisCount>());
      expect(delegate.crossAxisCount, 3);
      expect(delegate.childAspectRatio, 1.0);
      expect(delegate.crossAxisSpacing, 4.0);
      expect(delegate.mainAxisSpacing, 4.0);
    });

    test('애니메이션 컨트롤러 생성 테스트', () {
      final vsync = TestVSync();
      final controller = AnimationController(
        vsync: vsync,
        duration: const Duration(milliseconds: 300),
      );

      expect(controller, isA<AnimationController>());
      expect(controller.duration, const Duration(milliseconds: 300));
      expect(controller.lowerBound, 0.0);
      expect(controller.upperBound, 1.0);
    });

    testWidgets('디바이스 정보 테스트', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: Scaffold(body: Container())));

      final mediaQuery = MediaQuery.of(tester.element(find.byType(Container)));
      
      expect(mediaQuery.size.width, isA<double>());
      expect(mediaQuery.size.height, isA<double>());
      expect(mediaQuery.devicePixelRatio, isA<double>());
      expect(mediaQuery.textScaler, isA<TextScaler>());
    });

    test('메모리 정리 작업 테스트', () {
      // 메모리 정리 작업 실행 (오류 없이 실행되는지 확인)
      expect(() => MobilePerformanceUtils.performMemoryCleanup(), returnsNormally);
    });
  });
}

/// 테스트용 VSync 구현
class TestVSync extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick, debugLabel: 'test_vsync');
  }
} 
