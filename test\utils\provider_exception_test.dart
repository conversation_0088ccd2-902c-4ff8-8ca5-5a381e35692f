import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/provider_exception.dart';

void main() {
  group('ProviderException Tests', () {
    test('기본 생성자 테스트', () {
      final exception = ProviderException(message: '테스트 오류', code: 'TEST_ERROR');
      expect(exception.message, equals('테스트 오류'));
      expect(exception.code, equals('TEST_ERROR'));
    });

    test('forOperationFailed 팩토리 메서드', () {
      final exception = ProviderException.forOperationFailed('testOperation');
      expect(exception.code, equals('OPERATION_FAILED'));
      expect(exception.message, contains('testOperation'));
    });

    test('forDatabaseError 팩토리 메서드', () {
      final exception = ProviderException.forDatabaseError('Database error');
      expect(exception.code, equals('DATABASE_ERROR'));
      expect(exception.message, contains('Database error'));
    });

    test('forValidationError 팩토리 메서드', () {
      final exception = ProviderException.forValidationError('Validation error');
      expect(exception.code, equals('VALIDATION_ERROR'));
      expect(exception.message, contains('Validation error'));
    });

    test('forMaxRetriesExceeded 팩토리 메서드', () {
      final exception = ProviderException.forMaxRetriesExceeded();
      expect(exception.code, equals('MAX_RETRIES_EXCEEDED'));
      expect(exception.message, equals('최대 재시도 횟수를 초과했습니다'));
    });
  });
}
