import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/sql_utils.dart';

void main() {
  group('SqlUtils Tests', () {
    group('escapeLikePattern', () {
      test('should escape special characters in LIKE pattern', () {
        expect(SqlUtils.escapeLikePattern('100%'), equals('100\\%'));
        expect(SqlUtils.escapeLikePattern('user_name'), equals('user\\_name'));
        expect(
          SqlUtils.escapeLikePattern('test\\test'),
          equals('test\\\\test'),
        );
        expect(SqlUtils.escapeLikePattern('data[0]'), equals('data\\[0\\]'));
      });
    });

    group('createSafeLikePattern', () {
      test('should create safe LIKE pattern with wildcards', () {
        expect(SqlUtils.createSafeLikePattern('test'), equals('%test%'));
        expect(SqlUtils.createSafeLikePattern('100%'), equals('%100\\%%'));
        expect(
          SqlUtils.createSafeLikePattern('user_name'),
          equals('%user\\_name%'),
        );
      });
    });

    group('isValidOrderByColumn', () {
      final allowedColumns = ['name', 'age', 'created_at'];

      test('should validate allowed column names', () {
        expect(SqlUtils.isValidOrderByColumn('name', allowedColumns), isTrue);
        expect(SqlUtils.isValidOrderByColumn('age', allowedColumns), isTrue);
        expect(
          SqlUtils.isValidOrderByColumn('created_at', allowedColumns),
          isTrue,
        );
      });

      test('should reject disallowed column names', () {
        expect(
          SqlUtils.isValidOrderByColumn('invalid', allowedColumns),
          isFalse,
        );
        expect(
          SqlUtils.isValidOrderByColumn(
            'name; DROP TABLE users;',
            allowedColumns,
          ),
          isFalse,
        );
      });
    });

    group('createSafeOrderBy', () {
      final allowedColumns = ['name', 'age', 'created_at'];

      test('should create safe ORDER BY clause', () {
        expect(
          SqlUtils.createSafeOrderBy('name', allowedColumns),
          equals('name ASC'),
        );
        expect(
          SqlUtils.createSafeOrderBy('age', allowedColumns, descending: true),
          equals('age DESC'),
        );
      });

      test('should throw ArgumentError for invalid column names', () {
        expect(
          () => SqlUtils.createSafeOrderBy('invalid', allowedColumns),
          throwsArgumentError,
        );
        expect(
          () => SqlUtils.createSafeOrderBy(
            'name; DROP TABLE users;',
            allowedColumns,
          ),
          throwsArgumentError,
        );
      });
    });

    group('isValidTableName', () {
      final allowedTables = ['users', 'products', 'orders'];

      test('should validate allowed table names', () {
        expect(SqlUtils.isValidTableName('users', allowedTables), isTrue);
        expect(SqlUtils.isValidTableName('products', allowedTables), isTrue);
        expect(SqlUtils.isValidTableName('orders', allowedTables), isTrue);
      });

      test('should reject disallowed table names', () {
        expect(SqlUtils.isValidTableName('invalid', allowedTables), isFalse);
        expect(
          SqlUtils.isValidTableName('users; DROP TABLE users;', allowedTables),
          isFalse,
        );
      });
    });

    group('createSafeWhereIn', () {
      test('should create safe WHERE IN clause', () {
        expect(SqlUtils.createSafeWhereIn('id', 3), equals('id IN (?, ?, ?)'));
        expect(
          SqlUtils.createSafeWhereIn('status', 1),
          equals('status IN (?)'),
        );
      });

      test('should throw ArgumentError for invalid count', () {
        expect(() => SqlUtils.createSafeWhereIn('id', 0), throwsArgumentError);
        expect(() => SqlUtils.createSafeWhereIn('id', -1), throwsArgumentError);
      });
    });

    group('isValidSqlValue', () {
      test('should validate allowed SQL value types', () {
        expect(SqlUtils.isValidSqlValue(null), isTrue);
        expect(SqlUtils.isValidSqlValue(123), isTrue);
        expect(SqlUtils.isValidSqlValue(123.45), isTrue);
        expect(SqlUtils.isValidSqlValue('test'), isTrue);
        expect(SqlUtils.isValidSqlValue(true), isTrue);
        expect(SqlUtils.isValidSqlValue(false), isTrue);
      });

      test('should reject disallowed SQL value types', () {
        expect(SqlUtils.isValidSqlValue([1, 2, 3]), isFalse);
        expect(SqlUtils.isValidSqlValue({'key': 'value'}), isFalse);
        expect(SqlUtils.isValidSqlValue(DateTime.now()), isFalse);
      });
    });

    group('createBatchValuesClause', () {
      test('should create safe batch VALUES clause', () {
        expect(
          SqlUtils.createBatchValuesClause(2, 3),
          equals('(?, ?, ?), (?, ?, ?)'),
        );
        expect(SqlUtils.createBatchValuesClause(1, 2), equals('(?, ?)'));
      });

      test('should throw ArgumentError for invalid counts', () {
        expect(
          () => SqlUtils.createBatchValuesClause(0, 1),
          throwsArgumentError,
        );
        expect(
          () => SqlUtils.createBatchValuesClause(1, 0),
          throwsArgumentError,
        );
        expect(
          () => SqlUtils.createBatchValuesClause(-1, -1),
          throwsArgumentError,
        );
      });
    });
  });
}
